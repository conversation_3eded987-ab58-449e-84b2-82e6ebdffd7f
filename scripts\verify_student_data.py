#!/usr/bin/env python3
"""
验证学生数据导入结果
检查Neo4j数据库中的学生数据是否正确导入
"""

import asyncio
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.config import settings
from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    # 使用指定的Neo4j连接参数
    neo4j_uri = "bolt://localhost:7687"
    neo4j_user = "neo4j"
    neo4j_password = "12345678"
    
    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
    )

    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL,
        base_url=embedding_base_url,
    )

    llm_client = OpenAIGenericClient(config=llm_config)
    embedder = OpenAIEmbedder(config=embedder_config)
    cross_encoder = OpenAIRerankerClient(config=llm_config)

    return Graphiti(
        neo4j_uri,
        neo4j_user,
        neo4j_password,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

class StudentDataVerifier:
    """学生数据验证器"""
    
    def __init__(self, client: Graphiti):
        self.client = client
    
    async def verify_student_nodes(self):
        """验证学生节点"""
        query = """
        MATCH (s:Student)
        RETURN count(s) as student_count,
               collect(s.studentId)[0..5] as sample_ids,
               collect(s.name)[0..5] as sample_names
        """
        
        try:
            result = await self.client.driver.execute_query(query)
            record = result.records[0]
            
            print("📊 学生节点统计:")
            print(f"  总数量: {record['student_count']}")
            print(f"  示例ID: {record['sample_ids']}")
            print(f"  示例姓名: {record['sample_names']}")
            print()
            
            return record['student_count']
            
        except Exception as e:
            logger.error(f"验证学生节点失败: {e}")
            return 0
    
    async def verify_attempt_relationships(self):
        """验证答题记录关系"""
        query = """
        MATCH (s:Student)-[att:ATTEMPTED]->(p:Problem)
        RETURN count(att) as attempt_count,
               count(DISTINCT s) as students_with_attempts,
               count(DISTINCT p) as problems_attempted,
               avg(size(att.selectedOptions)) as avg_options_selected,
               sum(CASE WHEN att.isFullyCorrect THEN 1 ELSE 0 END) as correct_attempts
        """
        
        try:
            result = await self.client.driver.execute_query(query)
            record = result.records[0]
            
            total_attempts = record['attempt_count']
            correct_attempts = record['correct_attempts']
            accuracy = (correct_attempts / total_attempts * 100) if total_attempts > 0 else 0
            
            print("📝 答题记录统计:")
            print(f"  总答题记录: {total_attempts}")
            print(f"  参与学生数: {record['students_with_attempts']}")
            print(f"  涉及题目数: {record['problems_attempted']}")
            print(f"  平均选项数: {record['avg_options_selected']:.1f}")
            print(f"  正确答题数: {correct_attempts}")
            print(f"  总体准确率: {accuracy:.1f}%")
            print()
            
            return total_attempts
            
        except Exception as e:
            logger.error(f"验证答题记录失败: {e}")
            return 0
    
    async def verify_data_quality(self):
        """验证数据质量"""
        queries = {
            "学生无答题记录": """
                MATCH (s:Student)
                WHERE NOT (s)-[:ATTEMPTED]->()
                RETURN count(s) as count
            """,
            "答题记录无时间戳": """
                MATCH ()-[att:ATTEMPTED]->()
                WHERE att.timestamp IS NULL
                RETURN count(att) as count
            """,
            "答题记录无选项": """
                MATCH ()-[att:ATTEMPTED]->()
                WHERE att.selectedOptions IS NULL OR size(att.selectedOptions) = 0
                RETURN count(att) as count
            """,
            "题目节点缺失": """
                MATCH (s:Student)-[:ATTEMPTED]->(p)
                WHERE NOT p:Problem
                RETURN count(p) as count
            """
        }
        
        print("🔍 数据质量检查:")
        
        for check_name, query in queries.items():
            try:
                result = await self.client.driver.execute_query(query)
                count = result.records[0]['count']
                status = "✅ 正常" if count == 0 else f"⚠️ 发现 {count} 个问题"
                print(f"  {check_name}: {status}")
            except Exception as e:
                print(f"  {check_name}: ❌ 检查失败 - {e}")
        
        print()
    
    async def show_sample_data(self):
        """显示示例数据"""
        query = """
        MATCH (s:Student)-[att:ATTEMPTED]->(p:Problem)
        RETURN s.studentId as student_id,
               s.name as student_name,
               p.problemId as problem_id,
               att.selectedOptions as selected_options,
               att.isFullyCorrect as is_correct,
               att.timestamp as timestamp
        ORDER BY s.studentId, att.timestamp
        LIMIT 10
        """
        
        try:
            result = await self.client.driver.execute_query(query)
            
            print("📋 示例数据:")
            print("学生ID | 姓名 | 题目ID | 选项 | 正确 | 时间")
            print("-" * 60)
            
            for record in result.records:
                student_id = record['student_id']
                name = record['student_name']
                problem_id = record['problem_id']
                options = record['selected_options']
                is_correct = "✓" if record['is_correct'] else "✗"
                timestamp = record['timestamp']
                
                print(f"{student_id} | {name} | {problem_id} | {options} | {is_correct} | {timestamp}")
            
            print()
            
        except Exception as e:
            logger.error(f"显示示例数据失败: {e}")
    
    async def verify_all(self):
        """执行完整验证"""
        print("=" * 60)
        print("🔍 学生数据导入验证")
        print("=" * 60)
        
        try:
            # 验证学生节点
            student_count = await self.verify_student_nodes()
            
            # 验证答题记录
            attempt_count = await self.verify_attempt_relationships()
            
            # 验证数据质量
            await self.verify_data_quality()
            
            # 显示示例数据
            await self.show_sample_data()
            
            # 总结
            print("📊 验证总结:")
            print(f"  学生节点: {student_count} 个")
            print(f"  答题记录: {attempt_count} 个")
            
            if student_count > 0 and attempt_count > 0:
                avg_attempts = attempt_count / student_count
                print(f"  平均每人答题: {avg_attempts:.1f} 次")
                print("  ✅ 数据导入验证通过！")
            else:
                print("  ❌ 数据导入可能存在问题")
            
            print("=" * 60)
            
        except Exception as e:
            logger.error(f"验证过程失败: {e}")
            print("❌ 验证失败，请检查数据库连接和数据状态")

async def main():
    """主函数"""
    try:
        # 验证配置
        settings.validate()
        
        # 创建客户端
        client = create_graphiti_client()
        
        # 创建验证器
        verifier = StudentDataVerifier(client)
        
        # 执行验证
        await verifier.verify_all()
        
    except Exception as e:
        logger.error(f"验证失败: {e}")
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
