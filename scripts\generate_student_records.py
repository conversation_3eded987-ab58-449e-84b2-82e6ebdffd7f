#!/usr/bin/env python3
"""
学生做题记录生成器
从题目数据生成符合格式的学生做题记录
"""

import json
import random
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta, timezone
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StudentRecordGenerator:
    """学生做题记录生成器"""
    
    def __init__(self, num_students: int = 50, min_attempts: int = 10, max_attempts: int = 30):
        """
        初始化生成器
        
        Args:
            num_students: 生成的学生数量
            min_attempts: 每个学生最少答题数
            max_attempts: 每个学生最多答题数
        """
        self.num_students = num_students
        self.min_attempts = min_attempts
        self.max_attempts = max_attempts
        self.problems_data = []
        
    def load_problems_data(self, file_path: Path) -> List[Dict[str, Any]]:
        """加载题目数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载题目数据: {len(data)} 道题目")
            return data
        except Exception as e:
            logger.error(f"加载题目数据失败: {e}")
            return []
    
    def generate_student_name(self, student_id: str) -> str:
        """生成学生姓名"""
        surnames = ["张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴", 
                   "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗"]
        given_names = ["伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
                      "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞"]
        
        # 从student_id中提取数字作为种子，确保相同ID生成相同姓名
        import re
        numbers = re.findall(r'\d+', student_id)
        if numbers:
            seed = int(numbers[0])
            random.seed(seed)
        
        surname = random.choice(surnames)
        given_name = random.choice(given_names)
        return f"{surname}{given_name}"
    
    def calculate_student_ability(self, student_index: int) -> float:
        """计算学生能力水平 (0.0-1.0)"""
        # 生成正态分布的能力值，均值0.6，标准差0.2
        random.seed(student_index * 42)  # 确保可重现
        ability = random.gauss(0.6, 0.2)
        return max(0.1, min(0.9, ability))  # 限制在0.1-0.9之间
    
    def calculate_correct_probability(self, student_ability: float, problem_difficulty: float) -> float:
        """
        计算学生答对题目的概率
        使用IRT模型的简化版本
        """
        # 简化的IRT模型: P = 1 / (1 + exp(-(ability - difficulty)))
        import math
        
        # 将难度从0-10映射到0-1
        normalized_difficulty = problem_difficulty / 10.0
        
        # 计算答对概率
        diff = student_ability - normalized_difficulty
        probability = 1 / (1 + math.exp(-3 * diff))  # 3是区分度参数
        
        return max(0.05, min(0.95, probability))  # 限制在5%-95%之间
    
    def select_answer_options(self, problem: Dict[str, Any], is_correct: bool) -> List[str]:
        """选择答案选项"""
        correct_answer = problem.get('correct_answer', 'A')
        misconception_map = problem.get('misconception_map', {})
        
        if is_correct:
            return [correct_answer]
        else:
            # 从易错点选项中随机选择
            wrong_options = [opt for opt in misconception_map.keys() if opt != correct_answer]
            if wrong_options:
                return [random.choice(wrong_options)]
            else:
                # 如果没有易错点，随机选择一个错误选项
                all_options = ['A', 'B', 'C', 'D']
                wrong_options = [opt for opt in all_options if opt != correct_answer]
                return [random.choice(wrong_options)]
    
    def generate_timestamp(self, base_time: datetime, attempt_index: int) -> str:
        """生成答题时间戳"""
        # 在基准时间基础上随机添加时间偏移
        days_offset = random.randint(0, 365)  # 一年内
        hours_offset = random.randint(0, 23)
        minutes_offset = random.randint(0, 59)
        
        timestamp = base_time + timedelta(
            days=days_offset,
            hours=hours_offset,
            minutes=minutes_offset
        )
        
        return timestamp.isoformat().replace('+00:00', 'Z')
    
    def generate_student_attempts(self, student_index: int, problems: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为单个学生生成答题记录"""
        student_ability = self.calculate_student_ability(student_index)
        num_attempts = random.randint(self.min_attempts, self.max_attempts)
        
        # 随机选择题目
        selected_problems = random.sample(problems, min(num_attempts, len(problems)))
        
        attempts = []
        base_time = datetime(2025, 1, 1, tzinfo=timezone.utc)
        
        for i, problem in enumerate(selected_problems):
            problem_difficulty = problem.get('difficulty', 5.0)
            correct_prob = self.calculate_correct_probability(student_ability, problem_difficulty)
            
            # 决定是否答对
            is_correct = random.random() < correct_prob
            
            # 选择答案选项
            selected_options = self.select_answer_options(problem, is_correct)
            
            # 生成时间戳
            timestamp = self.generate_timestamp(base_time, i)
            
            attempt = {
                "problem_id": problem['question_id'],
                "selected_options": selected_options,
                "is_fully_correct": is_correct,
                "timestamp": timestamp
            }
            
            attempts.append(attempt)
        
        return attempts
    
    def generate_all_student_records(self, problems: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成所有学生的做题记录"""
        students = []
        
        logger.info(f"开始生成 {self.num_students} 个学生的做题记录...")
        
        for i in range(self.num_students):
            student_id = f"student_{i+1:03d}"
            student_name = self.generate_student_name(student_id)
            
            # 设置随机种子确保可重现
            random.seed(i * 123)
            
            attempts = self.generate_student_attempts(i, problems)
            
            student = {
                "student_id": student_id,
                "name": student_name,
                "problem_attempts": attempts
            }
            
            students.append(student)
            
            if (i + 1) % 10 == 0:
                logger.info(f"已生成 {i + 1} 个学生记录...")
        
        logger.info(f"学生记录生成完成: {len(students)} 个学生")
        return students
    
    def save_student_records(self, students: List[Dict[str, Any]], output_path: Path) -> None:
        """保存学生记录到文件"""
        try:
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(students, f, ensure_ascii=False, indent=2)
            
            logger.info(f"学生记录已保存到: {output_path}")
            
            # 打印统计信息
            total_attempts = sum(len(student['problem_attempts']) for student in students)
            logger.info(f"统计信息:")
            logger.info(f"  学生数量: {len(students)}")
            logger.info(f"  总答题记录: {total_attempts}")
            logger.info(f"  平均每人答题: {total_attempts / len(students):.1f}")
            
        except Exception as e:
            logger.error(f"保存学生记录失败: {e}")
            raise
    
    def generate_from_problems_file(self, problems_file: Path, output_file: Path) -> None:
        """从题目文件生成学生记录"""
        logger.info("=" * 60)
        logger.info("🎯 开始生成学生做题记录")
        logger.info("=" * 60)
        
        # 加载题目数据
        problems = self.load_problems_data(problems_file)
        if not problems:
            logger.error("没有加载到题目数据，退出")
            return
        
        # 生成学生记录
        students = self.generate_all_student_records(problems)
        
        # 保存到文件
        self.save_student_records(students, output_file)
        
        logger.info("=" * 60)
        logger.info("✅ 学生做题记录生成完成！")
        logger.info("=" * 60)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='从题目数据生成学生做题记录')
    parser.add_argument('--problems-file', 
                       default='data/start_0_end_200_new_data.json',
                       help='题目数据文件路径')
    parser.add_argument('--output-file',
                       default='data/generated_student_data.json',
                       help='输出的学生数据文件路径')
    parser.add_argument('--num-students', type=int, default=50,
                       help='生成的学生数量')
    parser.add_argument('--min-attempts', type=int, default=10,
                       help='每个学生最少答题数')
    parser.add_argument('--max-attempts', type=int, default=30,
                       help='每个学生最多答题数')
    
    args = parser.parse_args()
    
    # 验证输入文件
    problems_file = Path(args.problems_file)
    if not problems_file.exists():
        logger.error(f"题目数据文件不存在: {problems_file}")
        return
    
    # 创建生成器
    generator = StudentRecordGenerator(
        num_students=args.num_students,
        min_attempts=args.min_attempts,
        max_attempts=args.max_attempts
    )
    
    # 生成学生记录
    output_file = Path(args.output_file)
    generator.generate_from_problems_file(problems_file, output_file)
    
    print(f"\n✅ 学生记录生成完成！")
    print(f"📁 输出文件: {output_file}")
    print(f"\n使用以下命令导入到数据库：")
    print(f"python scripts/import_student_data.py --file {output_file}")

if __name__ == "__main__":
    main()
