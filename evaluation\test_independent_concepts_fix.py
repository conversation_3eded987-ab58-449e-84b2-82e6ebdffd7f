#!/usr/bin/env python3
"""
测试独立概念修复
验证 independent_concepts 路径不会出现在 paths_subset 中
"""

import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_independent_concepts_fix():
    """测试独立概念修复"""
    logger.info("测试独立概念修复...")
    
    # 模拟包含 independent_concepts 路径的 AI 规划结果
    mock_ai_planning = {
        'student_id': 'test_student_001',
        'plan_type': 'multi_source_multi_sink_optimal',
        'message': '已为您生成最优的多路径学习计划',
        'learning_paths': [
            {
                'path_id': 'path_1',
                'path_type': 'sequential',
                'start_concept': '概念A',
                'target_concept': '概念B',
                'steps': [
                    {
                        'concept_id': '概念A',
                        'concept_name': '概念A',
                        'level': 1,
                        'current_mastery': 0.8,
                        'is_mastered': True,
                        'is_weak': False,
                        'priority': 'medium'
                    },
                    {
                        'concept_id': '概念B',
                        'concept_name': '概念B',
                        'level': 2,
                        'current_mastery': 0.2,
                        'is_mastered': False,
                        'is_weak': True,
                        'priority': 'high'
                    }
                ],
                'estimated_time': 50
            },
            {
                'path_id': 'independent_concepts',  # 这个路径应该被过滤掉
                'path_type': 'independent',
                'start_concept': None,
                'target_concept': None,
                'steps': [
                    {
                        'concept_id': '独立概念1',
                        'concept_name': '独立概念1',
                        'level': 1,
                        'current_mastery': 0.1,
                        'is_mastered': False,
                        'is_weak': True,
                        'priority': 'high'
                    },
                    {
                        'concept_id': '独立概念2',
                        'concept_name': '独立概念2',
                        'level': 1,
                        'current_mastery': 0.2,
                        'is_mastered': False,
                        'is_weak': True,
                        'priority': 'high'
                    }
                ],
                'estimated_time': 40
            }
        ],
        'independent_weak_concepts': [
            {
                'concept_id': '独立概念3',
                'concept_name': '独立概念3',
                'level': 1,
                'path_type': 'independent'
            }
        ],
        'total_concepts_to_learn': 5,
        'estimated_time': 90
    }
    
    # 模拟修复后的路径处理逻辑
    def simulate_fixed_path_processing(ai_planning):
        """模拟修复后的路径处理逻辑"""
        from datetime import datetime
        
        paths_subset = []
        points_subset = []
        
        # 处理学习路径
        learning_paths = ai_planning.get('learning_paths', [])
        path_counter = 0  # 用于生成路径ID的计数器
        
        for path in learning_paths:
            if isinstance(path, dict) and 'steps' in path:
                path_id = path.get('path_id', '')
                
                # 过滤掉独立概念路径，这些应该放在 points_subset 中
                if path_id == 'independent_concepts':
                    # 将独立概念路径中的概念添加到 points_subset
                    for step in path.get('steps', []):
                        if isinstance(step, dict) and 'concept_id' in step:
                            concept_id = step['concept_id']
                            if concept_id not in points_subset:
                                points_subset.append(concept_id)
                    continue  # 跳过这个路径，不添加到 paths_subset
                
                # 提取路径中的概念
                path_concepts = []
                for step in path.get('steps', []):
                    if isinstance(step, dict) and 'concept_id' in step:
                        path_concepts.append(step['concept_id'])
                
                # 只有当路径包含多个概念时才作为真正的路径
                if len(path_concepts) > 1:
                    # 构建边列表（相邻概念之间的连接）
                    edges = []
                    for j in range(len(path_concepts) - 1):
                        edges.append([path_concepts[j], path_concepts[j + 1]])
                    
                    # 构建符合plan1.json格式的路径结构
                    paths_subset.append({
                        'path_id': path_id if path_id and path_id != 'independent_concepts' else f'optimal_path_{path_counter}',
                        'nodes': path_concepts,  # 路径中的节点列表
                        'edges': edges,  # 路径中的边列表
                        'sequence': path_concepts,  # 学习顺序序列
                        'concepts': path_concepts,  # 概念列表（用于相似度计算）
                        'estimated_time': path.get('estimated_time', 0),
                        'priority': 'medium'  # 默认优先级
                    })
                    path_counter += 1
                elif len(path_concepts) == 1:
                    # 单个概念应该放在 points_subset 中
                    concept_id = path_concepts[0]
                    if concept_id not in points_subset:
                        points_subset.append(concept_id)
        
        # 处理独立薄弱概念
        independent_concepts = ai_planning.get('independent_weak_concepts', [])
        for concept in independent_concepts:
            if isinstance(concept, dict):
                concept_id = concept.get('concept_id', '')
                if concept_id and concept_id not in points_subset:
                    points_subset.append(concept_id)
            else:
                concept_str = str(concept)
                if concept_str not in points_subset:
                    points_subset.append(concept_str)
        
        # 构建最终结果
        planning_result = {
            'evaluation_metadata': {
                'student_id': 'test_student_001',
                'target_concept': None,
                'timestamp': datetime.now().isoformat(),
                'generator_version': '2.0.0',
                'plan_type': 'multi_source_multi_sink_optimal',
                'planning_type': 'multi_target_optimal'
            },
            'planning_result': {
                'paths_subset': paths_subset,
                'points_subset': points_subset,
                'total_concepts_to_learn': len(set([c for path in paths_subset for c in path.get('concepts', [])] + points_subset)),
                'estimated_time': ai_planning.get('estimated_time', 0),
                'planning_confidence': 0.95
            }
        }
        
        return planning_result
    
    # 执行测试
    result = simulate_fixed_path_processing(mock_ai_planning)
    
    logger.info("修复后的规划结果:")
    logger.info(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 验证结果
    paths_subset = result['planning_result']['paths_subset']
    points_subset = result['planning_result']['points_subset']
    
    # 检查是否还有 independent_concepts 路径
    independent_path_found = False
    for path in paths_subset:
        if path.get('path_id') == 'independent_concepts':
            independent_path_found = True
            break
    
    if independent_path_found:
        logger.error("❌ 发现 independent_concepts 路径仍在 paths_subset 中")
        return False
    else:
        logger.info("✅ independent_concepts 路径已被正确过滤")
    
    # 检查独立概念是否正确添加到 points_subset
    expected_points = ['独立概念1', '独立概念2', '独立概念3']
    for expected_point in expected_points:
        if expected_point not in points_subset:
            logger.error(f"❌ 独立概念 '{expected_point}' 未在 points_subset 中找到")
            return False
    
    logger.info("✅ 所有独立概念都正确添加到 points_subset")
    
    # 检查真正的路径是否保留
    if len(paths_subset) != 1:
        logger.error(f"❌ 期望1个路径，实际找到 {len(paths_subset)} 个")
        return False
    
    if paths_subset[0]['path_id'] != 'path_1':
        logger.error(f"❌ 期望路径ID为 'path_1'，实际为 '{paths_subset[0]['path_id']}'")
        return False
    
    logger.info("✅ 真正的学习路径被正确保留")
    
    logger.info(f"✅ 最终结果: {len(paths_subset)} 个路径, {len(points_subset)} 个独立点")
    logger.info(f"✅ 路径: {[p['path_id'] for p in paths_subset]}")
    logger.info(f"✅ 独立点: {points_subset}")
    
    return True

def main():
    """主函数"""
    try:
        success = test_independent_concepts_fix()
        if success:
            print("\n✅ 独立概念修复测试通过")
            print("independent_concepts 路径已被正确过滤，独立概念正确添加到 points_subset")
        else:
            print("\n❌ 独立概念修复测试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        print("\n❌ 独立概念修复测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
