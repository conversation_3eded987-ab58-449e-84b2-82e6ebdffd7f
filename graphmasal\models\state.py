"""
LangGraph 状态定义模块
定义多智能体系统的状态结构
"""
from typing import Annotated, List, Dict, Optional, Any
from typing_extensions import TypedDict
from langgraph.graph import add_messages
from datetime import datetime
from dataclasses import dataclass

@dataclass
class StudentInfo:
    """学生信息数据类"""
    student_id: str
    name: str
    node_uuid: Optional[str] = None
    current_subject: Optional[str] = None
    learning_goals: List[str] = None
    
    def __post_init__(self):
        if self.learning_goals is None:
            self.learning_goals = []

@dataclass
class ProblemAttempt:
    """答题记录数据类"""
    problem_id: int
    selected_options: List[str]
    is_fully_correct: bool
    timestamp: datetime
    difficulty: float
    linked_concepts: List[str]
    
@dataclass
class LearningPath:
    """学习路径数据类"""
    target_concept: str
    path_concepts: List[str]
    estimated_time: int  # 预估学习时间（分钟）
    priority_level: int  # 优先级 1-5
    
@dataclass
class DiagnosticResult:
    """诊断结果数据类"""
    identified_misconceptions: List[str]
    weak_concepts: List[str]
    mastery_levels: Dict[str, float]  # 概念ID -> 掌握度
    recommendations: List[str]

class TutorState(TypedDict):
    """辅导系统的主状态"""
    # 消息历史 - 使用LangGraph的add_messages reducer
    messages: Annotated[List, add_messages]
    
    # 学生信息
    student_info: StudentInfo
    
    # 当前会话状态
    current_mode: str  # "chat", "problem_solving", "path_planning", "diagnostic"
    current_problem: Optional[Dict[str, Any]]
    
    # 学习历史
    problem_attempts: List[ProblemAttempt]
    learning_paths: List[LearningPath]
    
    # 诊断信息
    latest_diagnostic: Optional[DiagnosticResult]
    
    # 系统状态
    last_tool_used: Optional[str]
    context_info: Dict[str, Any]  # 存储额外的上下文信息
    
    # 知识图谱相关
    graphiti_facts: List[str]  # 从Graphiti检索的相关事实
    
    def __init__(self):
        self.messages = []
        self.student_info = None
        self.current_mode = "chat"
        self.current_problem = None
        self.problem_attempts = []
        self.learning_paths = []
        self.latest_diagnostic = None
        self.last_tool_used = None
        self.context_info = {}
        self.graphiti_facts = []

# 状态更新辅助函数
def update_student_mastery(state: TutorState, concept_id: str, mastery_level: float) -> TutorState:
    """更新学生对特定概念的掌握度"""
    if state.latest_diagnostic is None:
        state.latest_diagnostic = DiagnosticResult(
            identified_misconceptions=[],
            weak_concepts=[],
            mastery_levels={},
            recommendations=[]
        )
    
    state.latest_diagnostic.mastery_levels[concept_id] = mastery_level
    return state

def add_problem_attempt(state: TutorState, attempt: ProblemAttempt) -> TutorState:
    """添加新的答题记录"""
    state.problem_attempts.append(attempt)
    return state

def set_learning_path(state: TutorState, path: LearningPath) -> TutorState:
    """设置新的学习路径"""
    # 将新路径添加到列表开头（最高优先级）
    state.learning_paths.insert(0, path)
    return state
