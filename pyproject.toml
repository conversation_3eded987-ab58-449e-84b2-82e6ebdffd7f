[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "graphmasal"
version = "0.1.0"
description = "多智能体教育辅导系统 - Multi-Agent Educational Tutoring System"
readme = "graphmasal/README.md"
license = {text = "MIT"}
authors = [
    {name = "GraphMASAL Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "GraphMASAL Team", email = "<EMAIL>"}
]
keywords = ["education", "ai", "multi-agent", "tutoring", "knowledge-graph", "langchain", "graphiti"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Education",
    "Intended Audience :: Developers",
    "Topic :: Education",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = [
    "graphiti-core>=0.3.0",
    "langchain-openai>=0.1.0",
    "langgraph>=0.2.0",
    "python-dotenv>=1.0.0",
    "neo4j>=5.0.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
]
jupyter = [
    "ipywidgets>=8.0.0",
    "jupyter>=1.0.0",
    "notebook>=6.0.0",
]

[project.scripts]
graphmasal = "graphmasal.main:main"
graphmasal-test = "graphmasal.test_system:main"

[project.urls]
Homepage = "https://github.com/your-username/graphmasal"
Repository = "https://github.com/your-username/graphmasal"
Documentation = "https://github.com/your-username/graphmasal/blob/main/README.md"
"Bug Reports" = "https://github.com/your-username/graphmasal/issues"

[tool.setuptools]
package-dir = {"" = "."}

[tool.setuptools.packages.find]
where = ["."]
include = ["graphmasal*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
graphmasal = [
    "*.md",
    "*.txt",
    "*.env.example",
    "config/*.py",
    "agents/*.py",
    "agents/tools/*.py",
    "knowledge_graph/*.py",
    "models/*.py",
    "utils/*.py",
]

# Black 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort 导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["graphmasal"]

# pytest 配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests", "graphmasal"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"

# mypy 类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "graphiti_core.*",
    "neo4j.*",
    "langgraph.*",
    "langchain_core.*",
    "langchain_openai.*",
]
ignore_missing_imports = true
