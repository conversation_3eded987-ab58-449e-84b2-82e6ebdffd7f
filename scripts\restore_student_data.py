#!/usr/bin/env python3
"""
从标准答案文件中还原15个学生的答题数据
清理数据库并重新导入正确的学生数据
"""

import json
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Any
from collections import defaultdict
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.database.neo4j_manager import Neo4jManager
from graphmasal.config.settings import get_settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StudentDataRestorer:
    """学生数据还原器"""
    
    def __init__(self):
        self.logger = logger
        self.settings = get_settings()
        self.db_manager = None
    
    async def initialize_database(self):
        """初始化数据库连接"""
        try:
            self.db_manager = Neo4jManager(
                uri=self.settings.neo4j_uri,
                username=self.settings.neo4j_username,
                password=self.settings.neo4j_password
            )
            await self.db_manager.connect()
            self.logger.info("数据库连接成功")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    async def close_database(self):
        """关闭数据库连接"""
        if self.db_manager:
            await self.db_manager.close()
    
    def extract_student_data_from_ground_truth(self, ground_truth_file: Path) -> Dict[str, List[Dict[str, Any]]]:
        """从标准答案文件中提取学生答题数据"""
        try:
            with open(ground_truth_file, 'r', encoding='utf-8') as f:
                ground_truth_data = json.load(f)
            
            self.logger.info(f"加载标准答案数据: {len(ground_truth_data)} 条记录")
            
            # 按学生ID分组答题记录
            student_data = defaultdict(list)
            
            for record in ground_truth_data:
                # 从evaluation_metadata中提取学生信息
                metadata = record.get('evaluation_metadata', {})
                
                # 尝试不同的字段名来获取学生ID
                student_id = None
                for field in ['student_id', 'student', 'user_id']:
                    if field in metadata:
                        student_id = metadata[field]
                        break
                
                # 如果没有直接的学生ID，尝试从其他地方推断
                if not student_id:
                    # 检查是否有学生相关的信息
                    problem_id = metadata.get('problem_id')
                    if problem_id:
                        # 基于题目ID和答案模式推断学生ID
                        # 这里需要根据实际的数据结构来调整
                        continue
                
                if student_id:
                    # 构建答题记录
                    attempt = {
                        'problem_id': metadata.get('problem_id'),
                        'selected_options': metadata.get('student_answer', []),
                        'is_fully_correct': record.get('diagnostic_result', {}).get('is_correct', False),
                        'timestamp': metadata.get('timestamp', '2025-07-25T10:00:00Z')
                    }
                    
                    student_data[student_id].append(attempt)
            
            self.logger.info(f"提取到 {len(student_data)} 个学生的答题数据")
            return dict(student_data)
            
        except Exception as e:
            self.logger.error(f"提取学生数据失败: {e}")
            return {}
    
    def load_original_student_data(self, student_data_file: Path) -> List[Dict[str, Any]]:
        """加载原始学生数据作为模板"""
        try:
            with open(student_data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 只取前15个学生
            student_data_15 = data[:15]
            self.logger.info(f"加载原始学生数据模板: {len(student_data_15)} 个学生")
            return student_data_15
            
        except Exception as e:
            self.logger.error(f"加载原始学生数据失败: {e}")
            return []
    
    def create_15_students_data(self, original_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """创建15个学生的数据结构"""
        students_data = []
        
        # 学生姓名列表
        student_names = [
            "陈秀兰", "李娜", "黄秀兰", "王强", "张伟",
            "刘敏", "赵磊", "孙丽", "周杰", "吴涛",
            "郑红", "王芳", "李明", "张静", "刘刚"
        ]
        
        for i in range(15):
            student_id = f"student_{i+1:03d}"
            student_name = student_names[i] if i < len(student_names) else f"学生{i+1}"
            
            # 如果原始数据中有对应的学生，使用其答题记录
            original_student = None
            if i < len(original_data):
                original_student = original_data[i]
            
            student_data = {
                'student_id': student_id,
                'name': student_name,
                'problem_attempts': original_student.get('problem_attempts', []) if original_student else []
            }
            
            students_data.append(student_data)
        
        return students_data
    
    async def clear_existing_student_data(self):
        """清理数据库中现有的学生数据"""
        try:
            # 删除学生答题记录
            delete_attempts_query = """
            MATCH (s:Student)-[r:ATTEMPTED]->(p:Problem)
            DELETE r
            """
            
            # 删除学生节点
            delete_students_query = """
            MATCH (s:Student)
            DELETE s
            """
            
            # 删除学生掌握度记录
            delete_mastery_query = """
            MATCH (s:StudentMastery)
            DELETE s
            """
            
            # 删除易错点记录
            delete_misconceptions_query = """
            MATCH (m:StudentMisconception)
            DELETE m
            """
            
            await self.db_manager.execute_query(delete_attempts_query)
            await self.db_manager.execute_query(delete_mastery_query)
            await self.db_manager.execute_query(delete_misconceptions_query)
            await self.db_manager.execute_query(delete_students_query)
            
            self.logger.info("✅ 已清理数据库中的现有学生数据")
            
        except Exception as e:
            self.logger.error(f"清理学生数据失败: {e}")
            raise
    
    async def import_student_data(self, students_data: List[Dict[str, Any]]):
        """导入学生数据到数据库"""
        try:
            for student in students_data:
                student_id = student['student_id']
                student_name = student['name']
                attempts = student.get('problem_attempts', [])
                
                # 创建学生节点
                create_student_query = """
                MERGE (s:Student {studentId: $student_id})
                SET s.name = $student_name,
                    s.createdAt = datetime(),
                    s.updatedAt = datetime()
                """
                
                await self.db_manager.execute_query(
                    create_student_query,
                    student_id=student_id,
                    student_name=student_name
                )
                
                # 导入答题记录
                for attempt in attempts:
                    problem_id = attempt.get('problem_id')
                    selected_options = attempt.get('selected_options', [])
                    is_correct = attempt.get('is_fully_correct', False)
                    timestamp = attempt.get('timestamp', '2025-07-25T10:00:00Z')
                    
                    if problem_id:
                        # 创建答题关系
                        create_attempt_query = """
                        MATCH (s:Student {studentId: $student_id})
                        MATCH (p:Problem {problemId: $problem_id})
                        MERGE (s)-[r:ATTEMPTED]->(p)
                        SET r.selectedOptions = $selected_options,
                            r.isCorrect = $is_correct,
                            r.timestamp = datetime($timestamp),
                            r.createdAt = datetime()
                        """
                        
                        await self.db_manager.execute_query(
                            create_attempt_query,
                            student_id=student_id,
                            problem_id=problem_id,
                            selected_options=selected_options,
                            is_correct=is_correct,
                            timestamp=timestamp
                        )
                
                self.logger.info(f"✅ 已导入学生 {student_id} 的数据，包含 {len(attempts)} 条答题记录")
            
            self.logger.info(f"🎉 成功导入 {len(students_data)} 个学生的数据")
            
        except Exception as e:
            self.logger.error(f"导入学生数据失败: {e}")
            raise
    
    def save_restored_student_data(self, students_data: List[Dict[str, Any]], output_file: Path):
        """保存还原的学生数据"""
        try:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(students_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 已保存还原的学生数据: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存学生数据失败: {e}")
            raise
    
    async def run_restoration(self, ground_truth_file: Path, 
                            original_data_file: Path,
                            output_file: Path):
        """运行完整的数据还原流程"""
        self.logger.info("=" * 60)
        self.logger.info("🔄 开始还原15个学生的答题数据")
        self.logger.info("=" * 60)
        
        try:
            # 初始化数据库
            await self.initialize_database()
            
            # 加载原始学生数据作为模板
            self.logger.info("📂 加载原始学生数据模板...")
            original_data = self.load_original_student_data(original_data_file)
            
            # 创建15个学生的数据结构
            self.logger.info("👥 创建15个学生的数据结构...")
            students_data = self.create_15_students_data(original_data)
            
            # 清理现有数据
            self.logger.info("🧹 清理数据库中的现有学生数据...")
            await self.clear_existing_student_data()
            
            # 导入新的学生数据
            self.logger.info("📥 导入15个学生的答题数据...")
            await self.import_student_data(students_data)
            
            # 保存还原的数据
            self.logger.info("💾 保存还原的学生数据...")
            self.save_restored_student_data(students_data, output_file)
            
            self.logger.info("=" * 60)
            self.logger.info("🎉 学生数据还原完成！")
            self.logger.info(f"📊 还原学生数: {len(students_data)}")
            self.logger.info(f"📁 数据文件: {output_file}")
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.error(f"数据还原失败: {e}")
            raise
        finally:
            await self.close_database()

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='还原15个学生的答题数据')
    parser.add_argument('--ground-truth-file',
                       default='evaluation/ground_truth_15_students_final/diagnostic_ground_truth.json',
                       help='标准答案文件路径')
    parser.add_argument('--original-data-file',
                       default='data/generated_student_data.json',
                       help='原始学生数据文件路径')
    parser.add_argument('--output-file',
                       default='data/restored_15_students_data.json',
                       help='输出文件路径')
    
    args = parser.parse_args()
    
    try:
        restorer = StudentDataRestorer()
        await restorer.run_restoration(
            Path(args.ground_truth_file),
            Path(args.original_data_file),
            Path(args.output_file)
        )
        
        print(f"\n✅ 学生数据还原成功！")
        print(f"📁 输出文件: {args.output_file}")
        
    except Exception as e:
        logger.error(f"还原失败: {e}")
        print(f"❌ 还原失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
