# GraphMASAL 安装指南

## 安装方式

### 1. 开发模式安装（推荐）

如果你想要修改代码或参与开发，使用可编辑模式安装：

```bash
# 克隆或下载项目到本地
cd /path/to/graphmasal

# 安装为可编辑包（开发模式）
pip install -e .

# 或者安装包含开发依赖
pip install -e ".[dev]"
```

### 2. 标准安装

如果只是使用该包：

```bash
# 从本地目录安装
pip install .

# 或者安装包含测试依赖
pip install ".[test]"

# 安装所有可选依赖
pip install ".[dev,test,jupyter]"
```

### 3. 从源码构建

```bash
# 构建分发包
python -m build

# 安装构建的包
pip install dist/graphmasal-0.1.0-py3-none-any.whl
```

## 验证安装

安装完成后，你可以通过以下方式验证：

### 1. 命令行工具

```bash
# 运行主程序
graphmasal --help

# 运行测试
graphmasal-test
```

### 2. Python 导入测试

```python
# 测试导入
import graphmasal
from graphmasal.agents.tutor_agent import TutorAgent
from graphmasal.models.state import StudentInfo

print("GraphMASAL 安装成功！")
```

### 3. 运行演示

```bash
# 基本聊天演示
graphmasal --demo chat

# 诊断功能演示
graphmasal --demo diagnostic

# 学习路径规划演示
graphmasal --demo planning
```

## 环境配置

### 1. 创建配置文件

```bash
# 复制环境配置模板
cp graphmasal/.env.example .env

# 编辑配置文件
nano .env
```

### 2. 必要的配置项

```env
# Neo4j 数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini
```

## 依赖要求

### 系统要求

- Python 3.8 或更高版本
- Neo4j 数据库（5.0+）
- 至少 4GB 内存

### 核心依赖

- `graphiti-core>=0.3.0` - 知识图谱管理
- `langchain-openai>=0.1.0` - LLM 集成
- `langgraph>=0.2.0` - 智能体图构建
- `python-dotenv>=1.0.0` - 环境变量管理
- `neo4j>=5.0.0` - 图数据库驱动

### 可选依赖

- **开发工具** (`[dev]`): pytest, black, flake8, mypy
- **测试工具** (`[test]`): pytest, pytest-asyncio, pytest-cov
- **Jupyter支持** (`[jupyter]`): ipywidgets, jupyter, notebook

## 故障排除

### 常见问题

1. **导入错误**
   ```bash
   # 确保使用可编辑模式安装
   pip install -e .
   ```

2. **Neo4j 连接失败**
   ```bash
   # 检查 Neo4j 服务状态
   sudo systemctl status neo4j
   
   # 启动 Neo4j 服务
   sudo systemctl start neo4j
   ```

3. **依赖冲突**
   ```bash
   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate  # Windows
   
   # 重新安装
   pip install -e .
   ```

### 开发环境设置

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 安装 pre-commit 钩子
pre-commit install

# 运行代码格式化
black graphmasal/

# 运行类型检查
mypy graphmasal/

# 运行测试
pytest
```

## 卸载

```bash
# 卸载包
pip uninstall graphmasal

# 清理构建文件
rm -rf build/ dist/ *.egg-info/
```

## 更新

```bash
# 如果是可编辑模式安装，直接拉取最新代码即可
git pull origin main

# 如果是标准安装，需要重新安装
pip install --upgrade .
```

## 支持

如果遇到安装问题，请：

1. 检查 Python 版本：`python --version`
2. 检查 pip 版本：`pip --version`
3. 查看详细错误信息：`pip install -e . -v`
4. 提交 Issue 到项目仓库

---

**注意**: 确保在安装前已经正确配置了 Neo4j 数据库和相关环境变量。
