# 学生数据导入格式说明

## 概述

本文档描述了智能体教育辅导系统中学生信息和做题记录的JSON数据格式要求。

## 数据结构

### 顶层结构

学生数据文件应该是一个JSON数组，包含多个学生对象：

```json
[
  {
    "student_id": "学生唯一标识符",
    "name": "学生姓名",
    "problem_attempts": [...]
  },
  ...
]
```

### 学生基本信息

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `student_id` | string | ✅ | 学生唯一标识符，用于在知识图谱中创建Student节点 |
| `name` | string | ✅ | 学生姓名 |

### 答题记录 (problem_attempts)

每个学生可以有多个答题记录，格式如下：

```json
"problem_attempts": [
  {
    "problem_id": 1,
    "selected_options": ["A", "C"],
    "is_fully_correct": false,
    "timestamp": "2025-01-15T10:30:00Z",
    "difficulty": 0.6
  }
]
```

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `problem_id` | integer | ✅ | 题目ID对应题目数据集的question_id |
| `selected_options` | array[string] | ✅ | 学生选择的选项，如["A", "B"]或["A"] |
| `is_fully_correct` | boolean | ✅ | 是否完全正确 |
| `timestamp` | string | ❌ | 答题时间，ISO 8601格式，默认为当前时间 |
| `difficulty` | number | ❌ | 题目难度对应题目数据集的难度 |



## 完整示例

```json
[
  {
    "student_id": "student_001",
    "name": "张三",
    "problem_attempts": [
      {
        "problem_id": 1,
        "selected_options": ["A", "C"],
        "is_fully_correct": false,
        "timestamp": "2025-01-15T10:30:00Z",
        "difficulty": 0.6
      },
      {
        "problem_id": 5,
        "selected_options": ["B"],
        "is_fully_correct": true,
        "timestamp": "2025-01-15T11:00:00Z",
        "difficulty": 0.4
      }
    ]
  },
  {
    "student_id": "student_002",
    "name": "李四",
    "problem_attempts": [
      {
        "problem_id": 2,
        "selected_options": ["A", "B"],
        "is_fully_correct": true,
        "timestamp": "2025-01-15T14:20:00Z",
        "difficulty": 0.7
      },
      {
        "problem_id": 3,
        "selected_options": ["C"],
        "is_fully_correct": false,
        "timestamp": "2025-01-15T15:00:00Z",
        "difficulty": 0.5
      }
    ]
  },
  {
    "student_id": "student_003",
    "name": "王五",
    "problem_attempts": [
      {
        "problem_id": 1,
        "selected_options": ["B"],
        "is_fully_correct": true,
        "timestamp": "2025-01-16T09:15:00Z",
        "difficulty": 0.6
      }
    ]
  }
]
```

## 使用方法

### 1. 生成示例数据文件

```bash
python scripts/import_student_data.py --sample --file data/student_data.json
```

### 2. 导入学生数据

```bash
python scripts/import_student_data.py --file data/student_data.json
```

### 3. 查看导入结果

使用Neo4j Browser或查看脚本验证导入结果：

```cypher
// 查看学生节点
MATCH (s:Student) RETURN s LIMIT 10

// 查看答题记录
MATCH (s:Student)-[att:ATTEMPTED]->(p:Problem)
RETURN s.name, p.problemId, att.selectedOptions, att.isFullyCorrect
LIMIT 10

// 统计学生答题情况
MATCH (s:Student)-[att:ATTEMPTED]->(p:Problem)
RETURN s.name,
       count(att) as total_attempts,
       sum(CASE WHEN att.isFullyCorrect THEN 1 ELSE 0 END) as correct_attempts
ORDER BY s.name
```

## 注意事项

1. **数据完整性**：确保引用的 `problem_id` 在知识图谱中存在对应的Problem节点
2. **时间格式**：所有时间字段使用ISO 8601格式（如：2025-01-15T10:30:00Z）
3. **选项格式**：`selected_options` 应为字符串数组，即使只有一个选项
4. **唯一性**：`student_id` 必须唯一，重复导入会更新现有数据
5. **简化设计**：当前版本只包含基本学生信息和答题记录，掌握度和易错点关系由智能体系统自动计算

## 智能体工具格式

当智能体工具调用诊断功能时，使用以下格式：

```json
{
  "problem_id": 1,
  "selected_options": ["A", "C"],
  "correct_answer": ["B"],
  "misconception_map": {
    "A": "混淆了速度和加速度的概念",
    "C": "忽略了参考系的选择"
  }
}
```

这个格式与 `diagnostic_tool.py` 中的 `diagnose_student_performance` 工具兼容。
