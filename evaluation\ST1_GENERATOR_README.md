# ST1数据标准答案生成器

基于 `data/st1_data.json` 的真实答题记录生成标准的认知诊断和路径规划答案。

## 功能特点

- ✅ **基于真实数据**：使用 `data/st1_data.json` 中的真实学生答题记录
- ✅ **Neo4j数据库集成**：根据题目ID从Neo4j知识图谱中获取题目数据
- ✅ **完整功能保持**：与原 `ground_truth_generator.py` 功能完全一致
- ✅ **认知诊断**：基于答题正确率计算知识点掌握度
- ✅ **路径规划**：使用 OptimalPathPlanner 生成最优学习路径
- ✅ **标准格式**：输出格式与评测系统完全兼容

## 文件结构

```
evaluation/
├── st1_ground_truth_generator.py    # 主程序（Neo4j版本）
├── test_st1_neo4j_generator.py     # Neo4j版本测试程序
├── test_st1_generator.py           # 原版本测试程序
└── ST1_GENERATOR_README.md         # 使用说明
```

## 数据格式

### 输入数据格式

#### ST1数据 (`data/st1_data.json`)
```json
[
  {
    "student_id": "student_001",
    "name": "陈秀兰",
    "problem_attempts": [
      {
        "problem_id": 10201107,
        "selected_options": ["C"],
        "is_fully_correct": true,
        "timestamp": "2025-01-27T14:01:00Z"
      }
    ]
  }
]
```

#### 题目数据（从Neo4j数据库获取）
程序会根据ST1数据中的题目ID，从Neo4j知识图谱中查询题目信息：

**Neo4j查询结构**：
```cypher
MATCH (p:Problem {problemId: $problemId})
OPTIONAL MATCH (p)-[:TESTS_CONCEPT]->(c:Concept)
OPTIONAL MATCH (p)-[:OPTION_TARGETS]->(m:Misconception)
RETURN p,
       collect(DISTINCT c.kpId) as linked_kp_ids,
       collect(DISTINCT {option: m.option_id, misconception: m.description}) as misconceptions
```

**获取的题目数据格式**：
```json
{
  "problem_id": 10201107,
  "content": "题目内容",
  "difficulty": 3.0,
  "correct_answer": "C",
  "linked_kp_ids": ["力学/运动学/位移概念"],
  "misconception_map": {"A": "误解1", "B": "误解2", "C": "正确", "D": "误解3"},
  "image_urls": ["http://example.com/image1.jpg"]
}
```

### 输出数据格式

#### 认知诊断结果
```json
{
  "evaluation_metadata": {
    "student_id": "student_001",
    "timestamp": "2025-07-29T10:00:00",
    "generator_version": "1.0.0",
    "total_problems": 15,
    "data_source": "st1_real_data"
  },
  "diagnostic_result": {
    "mastered_concepts": [
      {
        "concept_id": "力学/运动学/位移概念",
        "concept_name": "位移概念",
        "mastery_level": 0.8,
        "problem_count": 5,
        "correct_count": 4
      }
    ],
    "not_mastered_concepts": [...],
    "total_concepts_evaluated": 10,
    "mastery_rate": 0.6,
    "diagnostic_confidence": 0.9
  }
}
```

#### 路径规划结果
```json
{
  "evaluation_metadata": {
    "student_id": "student_001",
    "target_concept": null,
    "timestamp": "2025-07-29T10:00:00",
    "generator_version": "2.0.0",
    "plan_type": "multi_source_multi_sink_optimal",
    "planning_type": "multi_target_optimal"
  },
  "planning_result": {
    "paths_subset": [
      {
        "path_id": "path_1",
        "nodes": ["概念A", "概念B"],
        "edges": [["概念A", "概念B"]],
        "sequence": ["概念A", "概念B"],
        "concepts": ["概念A", "概念B"],
        "estimated_time": 50,
        "priority": "medium"
      }
    ],
    "points_subset": ["独立概念1", "独立概念2"],
    "total_concepts_to_learn": 4,
    "estimated_time": 100,
    "planning_confidence": 0.95
  }
}
```

## 使用方法

### 1. 基本使用

```bash
# 使用默认参数
python evaluation/st1_ground_truth_generator.py

# 指定文件路径
python evaluation/st1_ground_truth_generator.py \
  --st1-file data/st1_data.json \
  --output-dir evaluation/st1_ground_truth
```

### 2. 参数说明

- `--st1-file`: ST1数据文件路径（默认: `data/st1_data.json`）
- `--output-dir`: 输出目录（默认: `evaluation/st1_ground_truth`）

**注意**：题目数据现在直接从Neo4j数据库获取，不再需要题目文件参数。

### 3. 输出文件

程序会在输出目录中生成以下文件：

- `st1_diagnostic_ground_truth.json`: 认知诊断标准答案
- `st1_planning_ground_truth.json`: 路径规划标准答案

## 核心功能

### 1. 数据转换

将ST1格式的答题记录转换为标准的题目格式：

```python
def convert_st1_to_problem_format(student_data, problems_data):
    # 将ST1答题记录与题目数据匹配
    # 转换为标准的学生答题格式
```

### 2. 认知诊断

基于答题正确率计算知识点掌握度：

```python
async def generate_student_diagnostic_ground_truth(student_id, student_problems, knowledge_verification):
    # 统计每个知识点的答题情况
    # 计算掌握度 = 正确题目数 / 总题目数
    # 按掌握度阈值（0.6）分类为已掌握/未掌握
```

### 3. 路径规划

使用 OptimalPathPlanner 生成最优学习路径：

```python
async def generate_planning_ground_truth(student_id, student_mastery, target_concept=None):
    # 调用路径规划器生成最优路径
    # 转换为标准的paths_subset和points_subset格式
    # 过滤独立概念路径，确保格式正确
```

## 测试验证

运行测试程序验证功能：

```bash
python evaluation/test_st1_generator.py
```

测试包括：
- ✅ ST1数据加载测试
- ✅ 数据格式转换测试  
- ✅ 诊断生成逻辑测试

## 与原程序的差异

| 特性 | 原程序 (ground_truth_generator.py) | 新程序 (st1_ground_truth_generator.py) |
|------|-----------------------------------|----------------------------------------|
| 数据源 | 模拟学生答题 | 真实ST1答题记录 |
| 学生数量 | 可配置（默认20个） | 基于ST1数据中的实际学生数 |
| 答题模拟 | 基于难度随机生成 | 使用真实答题记录 |
| 其他功能 | 完全相同 | 完全相同 |

## 注意事项

1. **环境要求**：需要配置好 Graphiti 和相关依赖
2. **数据文件**：确保 `data/st1_data.json` 和 `data/problems.json` 存在
3. **知识图谱**：需要连接到包含相关知识点的Neo4j数据库
4. **输出目录**：程序会自动创建输出目录

## 错误处理

程序包含完善的错误处理机制：

- 文件不存在时给出明确提示
- 数据格式错误时继续处理其他学生
- 网络连接问题时记录详细错误信息
- 知识点验证失败时使用默认值

## 日志输出

程序提供详细的日志输出：

```
🎯 开始生成ST1标准答案数据集
验证 50 个知识点...
有效知识点: 45/50
开始为 1 个学生生成标准答案...
处理学生 student_001 (陈秀兰)...
学生 student_001 处理完成
✅ ST1标准答案数据集生成完成
📊 诊断标准答案: 1 个学生
🛤️  规划标准答案: 1 个学生
```
