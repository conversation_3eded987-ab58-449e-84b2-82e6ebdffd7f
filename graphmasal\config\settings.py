"""
配置设置模块
包含系统的所有配置参数
"""
import os
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
# 尝试从多个位置加载 .env 文件，包括与根目录同级的目录
import pathlib
current_dir = pathlib.Path(__file__).parent.parent.parent  # 项目根目录

# 扩展搜索路径，包括与根目录同级的目录
env_paths = [
    current_dir / ".env",                    # 项目根目录下的.env
    current_dir.parent / ".env",             # 与根目录同级目录下的.env
    pathlib.Path(".env"),                    # 当前工作目录下的.env
    pathlib.Path("graphmasal/.env"),         # graphmasal子目录下的.env
    current_dir / "graphmasal" / ".env",     # 项目根目录/graphmasal下的.env
]

# 尝试加载.env文件
env_loaded = False
for env_path in env_paths:
    if env_path.exists():
        print(f"Loading .env from: {env_path.absolute()}")
        load_dotenv(env_path)
        env_loaded = True
        break

if not env_loaded:
    print("No .env file found, using system environment variables")
    load_dotenv()  # 使用默认行为

class Settings:
    """系统配置类"""
    
    # Neo4j 数据库配置
    NEO4J_URI: str = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    NEO4J_USER: str = os.getenv("NEO4J_USER", "neo4j")
    NEO4J_PASSWORD: str = os.getenv("NEO4J_PASSWORD", "password")
    
    # OpenAI API 配置
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    OPENAI_BASE_URL: Optional[str] = os.getenv("OPENAI_BASE_URL")

    # Embedding 模型配置
    EMBEDDING_MODEL: str = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
    EMBEDDING_BASE_URL: Optional[str] = os.getenv("EMBEDDING_BASE_URL")
    EMBEDDING_API_KEY: Optional[str] = os.getenv("EMBEDDING_API_KEY")
    
    # LangSmith 配置 (可选)
    LANGCHAIN_TRACING_V2: str = os.getenv("LANGCHAIN_TRACING_V2", "false")
    LANGCHAIN_PROJECT: str = os.getenv("LANGCHAIN_PROJECT", "Educational-Tutor-System")
    LANGSMITH_API_KEY: Optional[str] = os.getenv("LANGSMITH_API_KEY")
    
    # 系统配置
    TEMPERATURE: float = float(os.getenv("TEMPERATURE", "0.1"))
    MAX_SEARCH_RESULTS: int = int(os.getenv("MAX_SEARCH_RESULTS", "10"))
    
    # 学习路径配置
    MAX_PATH_LENGTH: int = int(os.getenv("MAX_PATH_LENGTH", "5"))
    MASTERY_THRESHOLD: float = float(os.getenv("MASTERY_THRESHOLD", "0.8"))
    
    def validate(self) -> bool:
        """验证必要的配置是否存在"""
        required_fields = [
            self.NEO4J_URI,
            self.NEO4J_USER, 
            self.NEO4J_PASSWORD,
            self.OPENAI_API_KEY
        ]
        
        missing_fields = [field for field in required_fields if not field]
        
        if missing_fields:
            raise ValueError(f"缺少必要的配置: {missing_fields}")
        
        return True

# 全局配置实例
settings = Settings()
