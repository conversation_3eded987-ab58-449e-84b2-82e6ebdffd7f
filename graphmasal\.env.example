# Neo4j 数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password_here

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
# 可选：自定义API端点（如使用豆包等其他服务）
OPENAI_BASE_URL=https://api.openai.com/v1/

# Embedding 模型配置
EMBEDDING_MODEL=text-embedding-3-small
# 可选：如果embedding使用不同的API密钥或端点
EMBEDDING_API_KEY=
EMBEDDING_BASE_URL=

# LangSmith 配置 (可选)
LANGCHAIN_TRACING_V2=false
LANGCHAIN_PROJECT=Educational-Tutor-System
LANGSMITH_API_KEY=your_langsmith_api_key_here

# 系统配置
TEMPERATURE=0.1
MAX_SEARCH_RESULTS=10

# 学习路径配置
MAX_PATH_LENGTH=5
MASTERY_THRESHOLD=0.8
