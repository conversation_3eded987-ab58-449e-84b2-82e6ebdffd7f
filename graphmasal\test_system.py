"""
系统测试模块
测试多智能体教育辅导系统的各个功能
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from main import TutorSystem
from knowledge_graph.schema_init import init_knowledge_graph
from knowledge_graph.data_loader import load_sample_data
from agents.tutor_agent import TutorAgent
from models.state import StudentInfo
from utils.helpers import (
    format_diagnostic_result, 
    format_learning_path, 
    format_student_progress
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemTester:
    """系统测试类"""
    
    def __init__(self):
        self.client = None
        self.tutor_agent = None
    
    async def setup(self):
        """设置测试环境"""
        
        try:
            # 使用TutorSystem的客户端创建方法
            tutor_system = TutorSystem()
            self.client = tutor_system._create_graphiti_client()
            
            # 初始化知识图谱（清除现有数据）
            await init_knowledge_graph(self.client, clear_existing=True)
            
            # 加载示例数据
            await load_sample_data(self.client)
            
            # 初始化辅导智能体
            self.tutor_agent = TutorAgent(self.client)
            
            logger.info("测试环境设置完成")
            
        except Exception as e:
            logger.error(f"测试环境设置失败: {e}")
            raise
    
    async def test_basic_chat(self):
        """测试基本对话功能"""
        
        print("\n=== 测试基本对话功能 ===")
        
        student = StudentInfo(
            student_id="test_student",
            name="测试学生",
            current_subject="物理"
        )
        
        test_messages = [
            "你好，我是新学生",
            "什么是牛顿第二定律？",
            "我在学习力学时遇到了困难",
            "能帮我推荐一些练习题吗？"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n测试 {i}: {message}")
            
            try:
                response = await self.tutor_agent.chat(
                    message=message,
                    student_info=student,
                    thread_id="test_chat"
                )
                
                print(f"回复: {response}")
                print("✅ 测试通过")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                logger.error(f"基本对话测试失败: {e}")
    
    async def test_diagnostic_function(self):
        """测试诊断功能"""
        
        print("\n=== 测试诊断功能 ===")
        
        student = StudentInfo(
            student_id="test_student",
            name="测试学生",
            current_subject="物理"
        )
        
        # 测试正确答题
        correct_problem = {
            "problem_id": 10200942,
            "selected_options": ["C"],  # 正确答案
            "correct_answer": ["C"],
            "misconception_map": {
                "A": "未考虑容器加速度对等效重力的影响",
                "B": "混淆加速度方向对有效重力的增强/减弱作用"
            }
        }
        
        print("\n测试1: 正确答题诊断")
        try:
            result = await self.tutor_agent.diagnose_problem(
                student_info=student,
                problem_data=correct_problem,
                thread_id="test_diagnostic_correct"
            )
            
            print("诊断结果:")
            print(result)
            print("✅ 正确答题诊断测试通过")
            
        except Exception as e:
            print(f"❌ 正确答题诊断测试失败: {e}")
        
        # 测试错误答题
        wrong_problem = {
            "problem_id": 10200942,
            "selected_options": ["A"],  # 错误答案
            "correct_answer": ["C"],
            "misconception_map": {
                "A": "未考虑容器加速度对等效重力的影响",
                "B": "混淆加速度方向对有效重力的增强/减弱作用"
            }
        }
        
        print("\n测试2: 错误答题诊断")
        try:
            result = await self.tutor_agent.diagnose_problem(
                student_info=student,
                problem_data=wrong_problem,
                thread_id="test_diagnostic_wrong"
            )
            
            print("诊断结果:")
            print(result)
            print("✅ 错误答题诊断测试通过")
            
        except Exception as e:
            print(f"❌ 错误答题诊断测试失败: {e}")
    
    async def test_planning_function(self):
        """测试学习路径规划功能"""
        
        print("\n=== 测试学习路径规划功能 ===")
        
        student = StudentInfo(
            student_id="test_student",
            name="测试学生",
            current_subject="物理"
        )
        
        target_concepts = [
            "力学/相互作用与牛顿定律/牛顿第二定律应用",
            "力学/运动学/相对运动概念"
        ]
        
        for i, target_concept in enumerate(target_concepts, 1):
            print(f"\n测试 {i}: 规划学习路径 - {target_concept}")
            
            try:
                result = await self.tutor_agent.plan_learning_path(
                    student_info=student,
                    target_concept=target_concept,
                    thread_id=f"test_planning_{i}"
                )
                
                print("规划结果:")
                print(result)
                print("✅ 学习路径规划测试通过")
                
            except Exception as e:
                print(f"❌ 学习路径规划测试失败: {e}")
                logger.error(f"学习路径规划测试失败: {e}")
    
    async def test_knowledge_graph_queries(self):
        """测试知识图谱查询"""
        
        print("\n=== 测试知识图谱查询 ===")
        
        # 测试概念查询
        concept_queries = [
            "SELECT * FROM Concept LIMIT 5",
            "MATCH (c:Concept) RETURN c.name, c.level LIMIT 5",
            "MATCH (s:Subject)-[:HAS_SUB_CONCEPT]->(c:Concept) RETURN s.name, c.name LIMIT 5"
        ]
        
        for i, query in enumerate(concept_queries, 1):
            print(f"\n测试查询 {i}: {query}")
            
            try:
                result = await self.client.driver.execute_query(query)
                
                print(f"查询结果: {len(result.records)} 条记录")
                for record in result.records[:3]:  # 只显示前3条
                    print(f"  {dict(record)}")
                
                print("✅ 查询测试通过")
                
            except Exception as e:
                print(f"❌ 查询测试失败: {e}")
    
    async def test_data_persistence(self):
        """测试数据持久化"""
        
        print("\n=== 测试数据持久化 ===")
        
        # 测试学生数据创建
        test_student_query = """
        MERGE (student:Student {studentId: "persistence_test", name: "持久化测试学生"})
        RETURN student
        """
        
        try:
            result = await self.client.driver.execute_query(test_student_query)
            print("✅ 学生数据创建测试通过")
            
            # 测试数据查询
            query_result = await self.client.driver.execute_query(
                "MATCH (s:Student {studentId: 'persistence_test'}) RETURN s"
            )
            
            if query_result.records:
                print("✅ 学生数据查询测试通过")
            else:
                print("❌ 学生数据查询测试失败")
            
            # 清理测试数据
            await self.client.driver.execute_query(
                "MATCH (s:Student {studentId: 'persistence_test'}) DELETE s"
            )
            print("✅ 测试数据清理完成")
            
        except Exception as e:
            print(f"❌ 数据持久化测试失败: {e}")
    
    async def test_error_handling(self):
        """测试错误处理"""
        
        print("\n=== 测试错误处理 ===")
        
        student = StudentInfo(
            student_id="error_test_student",
            name="错误测试学生",
            current_subject="物理"
        )
        
        # 测试无效输入
        invalid_inputs = [
            "",  # 空输入
            "   ",  # 空白输入
            "a" * 1000,  # 超长输入
        ]
        
        for i, invalid_input in enumerate(invalid_inputs, 1):
            print(f"\n测试 {i}: 无效输入处理")
            
            try:
                response = await self.tutor_agent.chat(
                    message=invalid_input,
                    student_info=student,
                    thread_id=f"error_test_{i}"
                )
                
                print(f"系统响应: {response[:100]}...")
                print("✅ 错误处理测试通过")
                
            except Exception as e:
                print(f"系统错误: {e}")
                print("⚠️ 需要改进错误处理")
    
    async def run_all_tests(self):
        """运行所有测试"""
        
        print("🚀 开始运行系统测试...")
        
        try:
            await self.setup()
            
            # 运行各项测试
            await self.test_basic_chat()
            await self.test_diagnostic_function()
            await self.test_planning_function()
            await self.test_knowledge_graph_queries()
            await self.test_data_persistence()
            await self.test_error_handling()
            
            print("\n🎉 所有测试完成！")
            
        except Exception as e:
            print(f"\n💥 测试过程中发生严重错误: {e}")
            logger.error(f"测试失败: {e}")
        
        finally:
            if self.client:
                await self.client.close()
                print("🔒 测试环境已清理")

def main():
    """主测试函数 - 同步包装器"""
    asyncio.run(async_main())

async def async_main():
    """异步主测试函数"""

    print("=== 多智能体教育辅导系统测试 ===")

    # 检查配置
    try:
        settings.validate()
        print("✅ 配置验证通过")
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return

    # 运行测试
    tester = SystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    main()
