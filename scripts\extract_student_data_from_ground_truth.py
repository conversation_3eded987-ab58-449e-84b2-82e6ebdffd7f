#!/usr/bin/env python3
"""
从diagnostic_ground_truth.json中提取15个学生的答题数据
生成正确的学生数据文件
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from collections import defaultdict
from datetime import datetime
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StudentDataExtractor:
    """学生数据提取器"""
    
    def __init__(self):
        self.logger = logger
    
    def load_ground_truth(self, file_path: Path) -> List[Dict[str, Any]]:
        """加载标准答案数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.logger.info(f"成功加载标准答案数据: {len(data)} 条记录")
            return data
        except Exception as e:
            self.logger.error(f"加载标准答案数据失败: {e}")
            return []
    
    def extract_student_attempts(self, ground_truth_data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """从标准答案数据中提取学生答题记录"""
        student_attempts = defaultdict(list)
        
        for record in ground_truth_data:
            student_id = record.get('student_id')
            if not student_id:
                continue
            
            metadata = record.get('evaluation_metadata', {})
            diagnostic_result = record.get('diagnostic_result', {})
            
            # 构建答题记录
            attempt = {
                'problem_id': metadata.get('problem_id'),
                'selected_options': metadata.get('student_answer', []),
                'is_fully_correct': diagnostic_result.get('is_correct', False),
                'timestamp': metadata.get('timestamp', '2025-07-25T10:00:00Z')
            }
            
            # 只添加有效的答题记录
            if attempt['problem_id']:
                student_attempts[student_id].append(attempt)
        
        self.logger.info(f"提取到 {len(student_attempts)} 个学生的答题数据")
        return dict(student_attempts)
    
    def create_student_data(self, student_attempts: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """创建完整的学生数据结构"""
        # 学生姓名列表
        student_names = [
            "陈秀兰", "李娜", "黄秀兰", "王强", "张伟",
            "刘敏", "赵磊", "孙丽", "周杰", "吴涛",
            "郑红", "王芳", "李明", "张静", "刘刚"
        ]
        
        students_data = []
        
        # 按学生ID排序确保一致性
        sorted_student_ids = sorted(student_attempts.keys())
        
        for i, student_id in enumerate(sorted_student_ids):
            attempts = student_attempts[student_id]
            student_name = student_names[i] if i < len(student_names) else f"学生{i+1}"
            
            # 为每个答题记录添加随机时间戳（保持合理的时间分布）
            base_time = datetime(2025, 1, 1)
            for j, attempt in enumerate(attempts):
                # 生成随机时间戳，确保时间递增
                random_days = random.randint(j * 5, j * 5 + 30)
                random_hours = random.randint(8, 22)
                random_minutes = random.randint(0, 59)
                
                timestamp = base_time.replace(
                    day=min(base_time.day + random_days, 28),
                    hour=random_hours,
                    minute=random_minutes
                )
                attempt['timestamp'] = timestamp.strftime('%Y-%m-%dT%H:%M:%SZ')
            
            student_data = {
                'student_id': student_id,
                'name': student_name,
                'problem_attempts': attempts
            }
            
            students_data.append(student_data)
            
            # 统计信息
            correct_count = sum(1 for attempt in attempts if attempt['is_fully_correct'])
            accuracy = correct_count / len(attempts) if attempts else 0.0
            
            self.logger.info(f"学生 {student_id} ({student_name}): {len(attempts)} 道题, 准确率 {accuracy:.3f}")
        
        return students_data
    
    def save_student_data(self, students_data: List[Dict[str, Any]], output_file: Path):
        """保存学生数据"""
        try:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(students_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 学生数据已保存: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存学生数据失败: {e}")
            raise
    
    def generate_statistics(self, students_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成统计信息"""
        total_students = len(students_data)
        total_attempts = sum(len(student['problem_attempts']) for student in students_data)
        total_correct = sum(
            sum(1 for attempt in student['problem_attempts'] if attempt['is_fully_correct'])
            for student in students_data
        )
        
        # 收集所有题目ID
        all_problem_ids = set()
        for student in students_data:
            for attempt in student['problem_attempts']:
                all_problem_ids.add(attempt['problem_id'])
        
        # 计算每个学生的准确率
        student_accuracies = []
        for student in students_data:
            attempts = student['problem_attempts']
            if attempts:
                correct = sum(1 for attempt in attempts if attempt['is_fully_correct'])
                accuracy = correct / len(attempts)
                student_accuracies.append(accuracy)
        
        statistics = {
            'total_students': total_students,
            'total_attempts': total_attempts,
            'total_correct': total_correct,
            'overall_accuracy': total_correct / total_attempts if total_attempts > 0 else 0.0,
            'unique_problems': len(all_problem_ids),
            'avg_attempts_per_student': total_attempts / total_students if total_students > 0 else 0,
            'min_accuracy': min(student_accuracies) if student_accuracies else 0.0,
            'max_accuracy': max(student_accuracies) if student_accuracies else 0.0,
            'avg_accuracy': sum(student_accuracies) / len(student_accuracies) if student_accuracies else 0.0
        }
        
        return statistics
    
    def run_extraction(self, ground_truth_file: Path, output_file: Path):
        """运行完整的数据提取流程"""
        self.logger.info("=" * 60)
        self.logger.info("🔄 开始从标准答案中提取学生数据")
        self.logger.info("=" * 60)
        
        try:
            # 加载标准答案数据
            self.logger.info("📂 加载标准答案数据...")
            ground_truth_data = self.load_ground_truth(ground_truth_file)
            
            if not ground_truth_data:
                self.logger.error("无法加载标准答案数据，提取终止")
                return
            
            # 提取学生答题记录
            self.logger.info("📊 提取学生答题记录...")
            student_attempts = self.extract_student_attempts(ground_truth_data)
            
            # 创建完整的学生数据结构
            self.logger.info("👥 创建学生数据结构...")
            students_data = self.create_student_data(student_attempts)
            
            # 生成统计信息
            self.logger.info("📈 生成统计信息...")
            statistics = self.generate_statistics(students_data)
            
            # 保存学生数据
            self.logger.info("💾 保存学生数据...")
            self.save_student_data(students_data, output_file)
            
            # 显示统计信息
            self.logger.info("=" * 60)
            self.logger.info("📊 数据提取完成统计:")
            self.logger.info(f"  总学生数: {statistics['total_students']}")
            self.logger.info(f"  总答题次数: {statistics['total_attempts']}")
            self.logger.info(f"  总体准确率: {statistics['overall_accuracy']:.3f}")
            self.logger.info(f"  平均每人答题: {statistics['avg_attempts_per_student']:.1f}")
            self.logger.info(f"  涉及题目数: {statistics['unique_problems']}")
            self.logger.info(f"  准确率范围: {statistics['min_accuracy']:.3f} - {statistics['max_accuracy']:.3f}")
            self.logger.info("=" * 60)
            
            return students_data
            
        except Exception as e:
            self.logger.error(f"数据提取失败: {e}")
            raise

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='从标准答案中提取学生数据')
    parser.add_argument('--ground-truth-file',
                       default='evaluation/ground_truth_15_students_final/diagnostic_ground_truth.json',
                       help='标准答案文件路径')
    parser.add_argument('--output-file',
                       default='data/generated_student_data.json',
                       help='输出文件路径')
    
    args = parser.parse_args()
    
    try:
        extractor = StudentDataExtractor()
        students_data = extractor.run_extraction(
            Path(args.ground_truth_file),
            Path(args.output_file)
        )
        
        if students_data:
            print(f"\n✅ 学生数据提取成功！")
            print(f"📁 输出文件: {args.output_file}")
            print(f"👥 学生数量: {len(students_data)}")
        else:
            print(f"❌ 提取失败")
        
    except Exception as e:
        logger.error(f"提取过程中出现错误: {e}")
        print(f"❌ 提取失败: {e}")

if __name__ == "__main__":
    main()
