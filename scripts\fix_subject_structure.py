#!/usr/bin/env python3
"""
修复学科结构脚本
将错误的多个学科节点修复为单一的"物理"学科节点
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.config import settings
from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    # 确定使用的API密钥和基础URL
    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    # 配置LLM客户端
    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
    )

    # 配置embedding客户端
    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL,
        base_url=embedding_base_url,
    )

    # 创建客户端组件
    llm_client = OpenAIGenericClient(config=llm_config)
    embedder = OpenAIEmbedder(config=embedder_config)
    cross_encoder = OpenAIRerankerClient(config=llm_config)

    return Graphiti(
        settings.NEO4J_URI,
        settings.NEO4J_USER,
        settings.NEO4J_PASSWORD,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

async def fix_subject_structure():
    """修复学科结构"""
    client = create_graphiti_client()
    
    print("🔧 开始修复学科结构...")
    
    # 1. 查看当前的学科节点
    print("\n📊 当前学科节点:")
    query_subjects = "MATCH (s:Subject) RETURN s.name as name ORDER BY s.name"
    result = await client.driver.execute_query(query_subjects)
    current_subjects = [record['name'] for record in result.records]
    for subject in current_subjects:
        print(f"  - {subject}")
    
    if '物理' in current_subjects and len(current_subjects) == 1:
        print("✅ 学科结构已经正确，无需修复")
        return
    
    # 2. 获取所有错误的学科节点及其连接的概念
    print("\n🔍 分析需要修复的结构...")
    query_wrong_subjects = """
    MATCH (s:Subject)-[r:HAS_SUB_CONCEPT]->(c:Concept)
    WHERE s.name <> '物理'
    RETURN s.name as subject_name, collect(c.kpId) as connected_concepts
    """
    result = await client.driver.execute_query(query_wrong_subjects)
    wrong_subjects_data = [(record['subject_name'], record['connected_concepts']) 
                          for record in result.records]
    
    print(f"发现 {len(wrong_subjects_data)} 个需要修复的学科节点:")
    for subject_name, concepts in wrong_subjects_data:
        print(f"  - {subject_name}: {len(concepts)} 个连接的概念")
    
    # 3. 创建正确的"物理"学科节点
    print("\n🏗️ 创建正确的学科结构...")
    create_physics_subject = "MERGE (s:Subject {name: '物理'})"
    await client.driver.execute_query(create_physics_subject)
    print("✅ 创建'物理'学科节点")
    
    # 4. 将所有第一级概念重新连接到"物理"学科
    print("\n🔗 重新建立学科-概念关系...")
    
    # 获取所有第一级概念（level = 1）
    query_first_level = """
    MATCH (c:Concept)
    WHERE c.level = 1
    RETURN c.kpId as concept_id, c.name as concept_name
    """
    result = await client.driver.execute_query(query_first_level)
    first_level_concepts = [(record['concept_id'], record['concept_name']) 
                           for record in result.records]
    
    print(f"找到 {len(first_level_concepts)} 个第一级概念:")
    for concept_id, concept_name in first_level_concepts:
        print(f"  - {concept_name} ({concept_id})")
    
    # 建立"物理"学科到第一级概念的关系
    reconnect_query = """
    MATCH (s:Subject {name: '物理'})
    MATCH (c:Concept)
    WHERE c.level = 1
    MERGE (s)-[:HAS_SUB_CONCEPT]->(c)
    RETURN count(*) as connected_count
    """
    result = await client.driver.execute_query(reconnect_query)
    connected_count = result.records[0]['connected_count']
    print(f"✅ 重新连接了 {connected_count} 个第一级概念到'物理'学科")
    
    # 5. 删除错误的学科节点和它们的关系
    print("\n🗑️ 清理错误的学科节点...")
    
    # 删除错误学科节点的关系
    delete_wrong_relations = """
    MATCH (s:Subject)-[r:HAS_SUB_CONCEPT]->()
    WHERE s.name <> '物理'
    DELETE r
    RETURN count(r) as deleted_relations
    """
    result = await client.driver.execute_query(delete_wrong_relations)
    deleted_relations = result.records[0]['deleted_relations']
    print(f"✅ 删除了 {deleted_relations} 个错误的学科关系")
    
    # 删除错误的学科节点
    delete_wrong_subjects = """
    MATCH (s:Subject)
    WHERE s.name <> '物理'
    DELETE s
    RETURN count(s) as deleted_subjects
    """
    result = await client.driver.execute_query(delete_wrong_subjects)
    deleted_subjects = result.records[0]['deleted_subjects']
    print(f"✅ 删除了 {deleted_subjects} 个错误的学科节点")
    
    # 6. 验证修复结果
    print("\n✅ 验证修复结果:")
    
    # 检查学科节点
    result = await client.driver.execute_query(query_subjects)
    final_subjects = [record['name'] for record in result.records]
    print(f"学科节点: {final_subjects}")
    
    # 检查"物理"学科连接的第一级概念
    verify_query = """
    MATCH (s:Subject {name: '物理'})-[:HAS_SUB_CONCEPT]->(c:Concept)
    WHERE c.level = 1
    RETURN c.name as concept_name
    ORDER BY c.name
    """
    result = await client.driver.execute_query(verify_query)
    physics_concepts = [record['concept_name'] for record in result.records]
    print(f"'物理'学科下的第一级概念 ({len(physics_concepts)} 个):")
    for concept in physics_concepts:
        print(f"  - {concept}")
    
    print("\n🎉 学科结构修复完成！")

async def main():
    """主函数"""
    try:
        # 验证配置
        settings.validate()
        
        # 执行修复
        await fix_subject_structure()
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
