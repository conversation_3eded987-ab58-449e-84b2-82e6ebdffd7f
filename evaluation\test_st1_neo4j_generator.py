#!/usr/bin/env python3
"""
测试基于Neo4j的ST1标准答案生成器
验证从数据库获取题目数据的功能
"""

import json
import logging
from pathlib import Path
import sys
import asyncio

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_st1_data_loading():
    """测试ST1数据加载"""
    logger.info("=== 测试ST1数据加载 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator
        
        # 创建生成器实例
        generator = ST1GroundTruthGenerator(None)
        
        # 测试数据加载
        st1_file = Path("data/st1_data.json")
        if not st1_file.exists():
            logger.error(f"ST1数据文件不存在: {st1_file}")
            return False
        
        st1_data = generator.load_st1_data(st1_file)
        
        if not st1_data:
            logger.error("ST1数据加载失败")
            return False
        
        logger.info(f"✅ 成功加载 {len(st1_data)} 个学生的数据")
        
        # 收集所有题目ID
        all_problem_ids = set()
        for student_data in st1_data:
            for attempt in student_data.get('problem_attempts', []):
                all_problem_ids.add(attempt['problem_id'])
        
        logger.info(f"✅ 发现 {len(all_problem_ids)} 个不同的题目ID")
        logger.info(f"题目ID示例: {list(all_problem_ids)[:5]}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_neo4j_problem_fetching():
    """测试从Neo4j获取题目数据"""
    logger.info("=== 测试Neo4j题目数据获取 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator, create_graphiti_client
        
        # 创建Graphiti客户端
        logger.info("初始化Graphiti客户端...")
        client = create_graphiti_client()
        
        # 创建生成器
        generator = ST1GroundTruthGenerator(client)
        
        # 测试获取几个题目
        test_problem_ids = [10201107, 10200972, 10201108]  # 使用一些示例题目ID
        
        logger.info(f"测试获取题目: {test_problem_ids}")
        problems_data = await generator.fetch_problems_from_neo4j(test_problem_ids)
        
        logger.info(f"✅ 成功获取 {len(problems_data)} 道题目")
        
        # 显示题目详情
        for problem in problems_data:
            logger.info(f"题目 {problem['problem_id']}:")
            logger.info(f"  - 难度: {problem['difficulty']}")
            logger.info(f"  - 正确答案: {problem['correct_answer']}")
            logger.info(f"  - 关联知识点数: {len(problem['linked_kp_ids'])}")
            logger.info(f"  - 易错点数: {len(problem['misconception_map'])}")
            if problem['linked_kp_ids']:
                logger.info(f"  - 知识点示例: {problem['linked_kp_ids'][:2]}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_data_conversion_with_neo4j():
    """测试使用Neo4j数据的格式转换"""
    logger.info("=== 测试数据格式转换（Neo4j版本） ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator, create_graphiti_client
        
        # 创建客户端和生成器
        client = create_graphiti_client()
        generator = ST1GroundTruthGenerator(client)
        
        # 模拟ST1学生数据
        student_data = {
            'student_id': 'student_001',
            'name': '测试学生',
            'problem_attempts': [
                {
                    'problem_id': 10201107,
                    'selected_options': ['C'],
                    'is_fully_correct': True,
                    'timestamp': '2025-01-27T14:01:00Z'
                },
                {
                    'problem_id': 10200972,
                    'selected_options': ['A'],
                    'is_fully_correct': False,
                    'timestamp': '2025-01-13T16:08:00Z'
                }
            ]
        }
        
        # 从Neo4j获取题目数据
        problem_ids = [attempt['problem_id'] for attempt in student_data['problem_attempts']]
        problems_data = await generator.fetch_problems_from_neo4j(problem_ids)
        
        if not problems_data:
            logger.warning("未能从Neo4j获取题目数据，跳过转换测试")
            return True
        
        # 测试转换
        converted_problems = generator.convert_st1_to_problem_format(student_data, problems_data)
        
        logger.info(f"✅ 成功转换 {len(converted_problems)} 道题目")
        
        for i, problem in enumerate(converted_problems):
            logger.info(f"题目 {i+1}:")
            logger.info(f"  - 题目ID: {problem['problem_id']}")
            logger.info(f"  - 学生答案: {problem['student_answer']}")
            logger.info(f"  - 是否正确: {problem['is_correct']}")
            logger.info(f"  - 正确答案: {problem['correct_answer']}")
            logger.info(f"  - 关联知识点: {problem['linked_kp_ids']}")
            logger.info(f"  - 难度: {problem['difficulty']}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_cypher_query_structure():
    """测试Cypher查询结构"""
    logger.info("=== 测试Cypher查询结构 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import create_graphiti_client
        
        # 创建客户端
        client = create_graphiti_client()
        
        # 测试查询一个题目
        test_problem_id = 10201107
        
        cypher_query = """
        MATCH (p:Problem {problemId: $problemId})
        OPTIONAL MATCH (p)-[:TESTS_CONCEPT]->(c:Concept)
        OPTIONAL MATCH (p)-[:OPTION_TARGETS]->(m:Misconception)
        RETURN p, 
               collect(DISTINCT c.kpId) as linked_kp_ids,
               collect(DISTINCT {option: m.option_id, misconception: m.description}) as misconceptions
        """
        
        logger.info(f"测试查询题目 {test_problem_id}")
        logger.info(f"Cypher查询: {cypher_query}")
        
        result = await client.driver.execute_query(
            cypher_query,
            problemId=test_problem_id
        )
        
        if result.records:
            record = result.records[0]
            problem_node = record['p']
            linked_kp_ids = record['linked_kp_ids']
            misconceptions = record['misconceptions']
            
            logger.info("✅ 查询成功")
            logger.info(f"题目属性: {dict(problem_node)}")
            logger.info(f"关联知识点: {linked_kp_ids}")
            logger.info(f"易错点: {misconceptions}")
        else:
            logger.warning(f"题目 {test_problem_id} 在数据库中未找到")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    logger.info("开始测试基于Neo4j的ST1标准答案生成器...")
    
    tests = [
        ("ST1数据加载", test_st1_data_loading, False),  # 同步测试
        ("Cypher查询结构", test_cypher_query_structure, True),  # 异步测试
        ("Neo4j题目数据获取", test_neo4j_problem_fetching, True),  # 异步测试
        ("数据格式转换（Neo4j版本）", test_data_conversion_with_neo4j, True)  # 异步测试
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func, is_async in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if is_async:
                result = await test_func()
            else:
                result = test_func()
                
            if result:
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试总结: {passed}/{total} 个测试通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        print("\n🎉 所有测试通过！基于Neo4j的ST1标准答案生成器可以正常工作")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，请检查相关功能")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
