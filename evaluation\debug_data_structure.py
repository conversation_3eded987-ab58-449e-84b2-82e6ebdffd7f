#!/usr/bin/env python3
"""
调试数据结构
检查 st20plan.json 和 st20true.json 的数据结构
"""

import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_and_debug_json(filepath, name):
    """加载并调试JSON文件"""
    logger.info(f"=== 调试 {name} ===")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"文件加载成功: {filepath}")
        logger.info(f"顶层键: {list(data.keys())}")
        
        # 检查 evaluation_metadata
        if 'evaluation_metadata' in data:
            metadata = data['evaluation_metadata']
            logger.info(f"evaluation_metadata 键: {list(metadata.keys())}")
            logger.info(f"student_id: {metadata.get('student_id', 'N/A')}")
        
        # 检查 planning_result
        if 'planning_result' in data:
            planning = data['planning_result']
            logger.info(f"planning_result 键: {list(planning.keys())}")
            
            # 检查 paths_subset
            paths = planning.get('paths_subset', [])
            logger.info(f"paths_subset 数量: {len(paths)}")
            if paths:
                logger.info(f"第一个路径的键: {list(paths[0].keys())}")
                logger.info(f"第一个路径的概念数: {len(paths[0].get('concepts', []))}")
            
            # 检查 points_subset
            points = planning.get('points_subset', [])
            logger.info(f"points_subset 数量: {len(points)}")
            if points:
                logger.info(f"前3个独立点: {points[:3]}")
        
        return data
        
    except Exception as e:
        logger.error(f"加载文件 {filepath} 失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return None

def test_evaluator_parsing():
    """测试评测器如何解析数据"""
    try:
        from evaluators.path_similarity_evaluator import PathSimilarityEvaluator
        
        logger.info("=== 测试评测器解析 ===")
        
        # 加载数据
        predicted_data = load_and_debug_json("evaluation/st20plan.json", "预测结果")
        ground_truth_data = load_and_debug_json("evaluation/st20true.json", "真实标准")
        
        if not predicted_data or not ground_truth_data:
            return False
        
        # 创建评测器
        evaluator = PathSimilarityEvaluator(
            node_weight=0.4,
            edge_weight=0.3,
            sequence_weight=0.3,
            paths_weight=0.7,
            points_weight=0.3
        )
        
        # 测试数据提取
        logger.info("=== 测试数据提取 ===")
        
        # 检查评测器如何提取数据
        predicted_paths = evaluator._extract_paths_subset(predicted_data)
        predicted_points = evaluator._extract_points_subset(predicted_data)
        
        truth_paths = evaluator._extract_paths_subset(ground_truth_data)
        truth_points = evaluator._extract_points_subset(ground_truth_data)
        
        logger.info(f"评测器提取的预测路径数: {len(predicted_paths)}")
        logger.info(f"评测器提取的预测独立点数: {len(predicted_points)}")
        logger.info(f"评测器提取的真实路径数: {len(truth_paths)}")
        logger.info(f"评测器提取的真实独立点数: {len(truth_points)}")
        
        if predicted_paths:
            logger.info(f"第一个预测路径: {predicted_paths[0]}")
        
        if truth_paths:
            logger.info(f"第一个真实路径: {truth_paths[0]}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试评测器解析失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    try:
        success = test_evaluator_parsing()
        if success:
            print("\n✅ 数据结构调试完成")
        else:
            print("\n❌ 数据结构调试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"调试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        print("\n❌ 数据结构调试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
