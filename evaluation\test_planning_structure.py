#!/usr/bin/env python3
"""
测试重写后的 run_agent_planning 方法的数据结构
验证输出格式是否符合评测方案要求
"""

import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_planning_data_structure():
    """测试规划数据结构"""
    logger.info("测试规划数据结构...")
    
    # 模拟 AI 规划工具的返回结果
    mock_ai_planning = {
        'student_id': 'test_student_001',
        'plan_type': 'multi_source_multi_sink_optimal',
        'message': '已为您生成最优的多路径学习计划',
        'learning_paths': [
            {
                'path_id': 'path_1',
                'path_type': 'sequential',
                'start_concept': 'concept_001',
                'target_concept': 'concept_003',
                'steps': [
                    {
                        'concept_id': 'concept_001',
                        'concept_name': '基础概念1',
                        'level': 1,
                        'current_mastery': 0.8,
                        'is_mastered': True,
                        'is_weak': False,
                        'priority': 'medium'
                    },
                    {
                        'concept_id': 'concept_003',
                        'concept_name': '未掌握概念1',
                        'level': 2,
                        'current_mastery': 0.2,
                        'is_mastered': False,
                        'is_weak': True,
                        'priority': 'high'
                    }
                ],
                'estimated_time': 50
            }
        ],
        'independent_weak_concepts': [
            {
                'concept_id': 'concept_004',
                'concept_name': '未掌握概念2',
                'level': 1,
                'path_type': 'independent'
            }
        ],
        'total_concepts_to_learn': 3,
        'estimated_time': 70
    }
    
    # 模拟诊断结果
    diagnostic_result = {
        'evaluation_metadata': {
            'student_id': 'test_student_001',
            'timestamp': '2024-01-01T10:00:00',
            'generator_version': 'test_1.0.0'
        },
        'diagnostic_result': {
            'mastered_concepts': [
                {
                    'concept_id': 'concept_001',
                    'concept_name': '基础概念1',
                    'mastery_level': 0.8,
                    'problem_count': 2
                },
                {
                    'concept_id': 'concept_002',
                    'concept_name': '基础概念2',
                    'mastery_level': 0.5,  # 薄弱概念
                    'problem_count': 1
                }
            ],
            'not_mastered_concepts': [
                {
                    'concept_id': 'concept_003',
                    'concept_name': '未掌握概念1',
                    'mastery_level': 0.2,
                    'problem_count': 3
                },
                {
                    'concept_id': 'concept_004',
                    'concept_name': '未掌握概念2',
                    'mastery_level': 0.1,
                    'problem_count': 2
                }
            ]
        }
    }
    
    # 模拟重写后的 run_agent_planning 方法的处理逻辑
    def simulate_run_agent_planning(diagnostic_result, ai_planning):
        """模拟重写后的 run_agent_planning 方法"""
        from datetime import datetime
        
        student_id = diagnostic_result.get('evaluation_metadata', {}).get('student_id', 'unknown')
        diagnostic_result_data = diagnostic_result.get('diagnostic_result', {})
        
        # 1. 提取掌握和未掌握的概念
        mastered_concepts = []
        for concept in diagnostic_result_data.get('mastered_concepts', []):
            mastered_concepts.append({
                'concept_id': concept.get('concept_id', concept.get('concept_name', '')),
                'concept_name': concept.get('concept_name', ''),
                'mastery_level': concept.get('mastery_level', 1.0)
            })
        
        not_mastered_concepts = []
        for concept in diagnostic_result_data.get('not_mastered_concepts', []):
            not_mastered_concepts.append({
                'concept_id': concept.get('concept_id', concept.get('concept_name', '')),
                'concept_name': concept.get('concept_name', ''),
                'mastery_level': concept.get('mastery_level', 0.0)
            })
        
        # 提取薄弱概念
        weak_concepts = []
        for concept in mastered_concepts:
            if concept['mastery_level'] < 0.6:
                weak_concepts.append(concept)
        
        all_weak_concepts = not_mastered_concepts + weak_concepts
        
        # 2. 按照评测方案分离路径子集和独立点子集
        paths_subset = []
        points_subset = []
        
        # 处理学习路径
        learning_paths = ai_planning.get('learning_paths', [])
        for i, path in enumerate(learning_paths):
            if isinstance(path, dict) and 'steps' in path:
                # 提取路径中的概念
                path_concepts = []
                for step in path.get('steps', []):
                    if isinstance(step, dict) and 'concept_id' in step:
                        path_concepts.append(step['concept_id'])
                
                # 构建符合评测方案的路径结构
                paths_subset.append({
                    'path_id': path.get('path_id', f'path_{i+1}'),
                    'path_type': path.get('path_type', 'sequential'),
                    'concepts': path_concepts,  # 用于相似度计算
                    'steps': path.get('steps', []),  # 保留原始步骤信息
                    'estimated_time': path.get('estimated_time', 0)
                })
        
        # 处理独立薄弱概念
        independent_concepts = ai_planning.get('independent_weak_concepts', [])
        for concept in independent_concepts:
            if isinstance(concept, dict):
                points_subset.append(concept.get('concept_id', ''))
            else:
                points_subset.append(str(concept))
        
        # 如果没有独立概念，将所有薄弱概念作为独立点
        if not points_subset:
            for concept in all_weak_concepts:
                concept_id = concept.get('concept_id', '')
                if concept_id and concept_id not in [c for path in paths_subset for c in path.get('concepts', [])]:
                    points_subset.append(concept_id)
        
        # 3. 构建最终的规划结果
        planning_result = {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'ai_planning_2.0.0',
                'plan_type': 'optimal_paths_with_separation'
            },
            'planning_result': {
                'paths_subset': paths_subset,
                'points_subset': points_subset,
                'total_concepts_to_learn': len(set([c for path in paths_subset for c in path.get('concepts', [])] + points_subset)),
                'estimated_time': ai_planning.get('estimated_time', 0),
                'planning_confidence': 0.9,
                'ai_reasoning': ai_planning.get('message', '基于AI智能体的最优路径规划'),
                'mastered_concepts_count': len(mastered_concepts),
                'weak_concepts_count': len(all_weak_concepts)
            }
        }
        
        return planning_result
    
    # 执行模拟
    result = simulate_run_agent_planning(diagnostic_result, mock_ai_planning)
    
    # 验证结果结构
    logger.info("规划结果结构:")
    logger.info(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 验证关键字段
    assert 'evaluation_metadata' in result
    assert 'planning_result' in result
    assert 'paths_subset' in result['planning_result']
    assert 'points_subset' in result['planning_result']
    
    paths_subset = result['planning_result']['paths_subset']
    points_subset = result['planning_result']['points_subset']
    
    logger.info(f"✅ 路径子集数量: {len(paths_subset)}")
    logger.info(f"✅ 独立点子集数量: {len(points_subset)}")
    
    # 验证路径结构
    for i, path in enumerate(paths_subset):
        logger.info(f"✅ 路径 {i+1}: {path.get('concepts', [])}")
        assert 'path_id' in path
        assert 'concepts' in path  # 用于相似度计算
        assert 'steps' in path     # 保留详细信息
    
    logger.info(f"✅ 独立点: {points_subset}")
    
    return True

def main():
    """主函数"""
    try:
        success = test_planning_data_structure()
        if success:
            print("\n✅ 规划数据结构测试通过")
            print("重写的 run_agent_planning 方法符合评测方案要求")
        else:
            print("\n❌ 规划数据结构测试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        print("\n❌ 规划数据结构测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
