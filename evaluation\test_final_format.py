#!/usr/bin/env python3
"""
最终格式验证测试
确保盲测生成的规划结果格式与 plan1.json 完全一致
"""

import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_plan1_format():
    """加载 plan1.json 的格式作为参考"""
    plan1_path = Path(__file__).parent / "ground_truth" / "plan1.json"
    with open(plan1_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def simulate_complete_planning_result():
    """模拟完整的规划结果生成过程"""
    from datetime import datetime
    
    # 模拟 AI 规划工具的返回结果
    mock_ai_planning = {
        'student_id': 'student_001',
        'plan_type': 'multi_source_multi_sink_optimal',
        'message': '已为您生成最优的多路径学习计划',
        'learning_paths': [
            {
                'path_id': 'path_1',
                'path_type': 'sequential',
                'start_concept': '力学/运动学/位移、速度、加速度的概念与计算（瞬时与平均）',
                'target_concept': '力学/运动学/相对运动概念（速度合成与分解）',
                'steps': [
                    {
                        'concept_id': '力学/运动学/位移、速度、加速度的概念与计算（瞬时与平均）',
                        'concept_name': '位移、速度、加速度的概念与计算',
                        'level': 1,
                        'current_mastery': 0.8,
                        'is_mastered': True,
                        'is_weak': False,
                        'priority': 'medium'
                    },
                    {
                        'concept_id': '力学/运动学/相对运动概念（速度合成与分解）',
                        'concept_name': '相对运动概念',
                        'level': 2,
                        'current_mastery': 0.2,
                        'is_mastered': False,
                        'is_weak': True,
                        'priority': 'high'
                    }
                ],
                'estimated_time': 50
            }
        ],
        'independent_weak_concepts': [],
        'total_concepts_to_learn': 2,
        'estimated_time': 50
    }
    
    # 按照修改后的逻辑构建结果
    student_id = 'student_001'
    paths_subset = []
    points_subset = []
    
    # 处理学习路径
    learning_paths = mock_ai_planning.get('learning_paths', [])
    for i, path in enumerate(learning_paths):
        if isinstance(path, dict) and 'steps' in path:
            # 提取路径中的概念
            path_concepts = []
            for step in path.get('steps', []):
                if isinstance(step, dict) and 'concept_id' in step:
                    path_concepts.append(step['concept_id'])
            
            # 构建边列表（相邻概念之间的连接）
            edges = []
            for j in range(len(path_concepts) - 1):
                edges.append([path_concepts[j], path_concepts[j + 1]])
            
            # 构建符合plan1.json格式的路径结构
            paths_subset.append({
                'path_id': path.get('path_id', f'optimal_path_{i}'),
                'nodes': path_concepts,  # 路径中的节点列表
                'edges': edges,  # 路径中的边列表
                'sequence': path_concepts,  # 学习顺序序列
                'concepts': path_concepts,  # 概念列表（用于相似度计算）
                'estimated_time': path.get('estimated_time', 0),
                'priority': 'medium'  # 默认优先级
            })
    
    # 处理独立薄弱概念
    independent_concepts = mock_ai_planning.get('independent_weak_concepts', [])
    for concept in independent_concepts:
        if isinstance(concept, dict):
            points_subset.append(concept.get('concept_id', ''))
        else:
            points_subset.append(str(concept))
    
    # 构建最终的规划结果
    planning_result = {
        'evaluation_metadata': {
            'student_id': student_id,
            'target_concept': None,  # 多目标规划
            'timestamp': datetime.now().isoformat(),
            'generator_version': '2.0.0',
            'plan_type': mock_ai_planning.get('plan_type', 'multi_source_multi_sink_optimal'),
            'planning_type': 'multi_target_optimal'
        },
        'planning_result': {
            'paths_subset': paths_subset,
            'points_subset': points_subset,
            'total_concepts_to_learn': len(set([c for path in paths_subset for c in path.get('concepts', [])] + points_subset)),
            'estimated_time': mock_ai_planning.get('estimated_time', 0),
            'planning_confidence': 0.95
        }
    }
    
    return planning_result

def compare_formats(generated, reference):
    """比较生成格式与参考格式"""
    logger.info("=== 详细格式对比 ===")
    
    # 比较 evaluation_metadata
    logger.info("evaluation_metadata 字段对比:")
    gen_meta = generated['evaluation_metadata']
    ref_meta = reference['evaluation_metadata']
    
    for key in ref_meta.keys():
        if key in gen_meta:
            logger.info(f"  ✅ {key}: {type(gen_meta[key]).__name__}")
        else:
            logger.error(f"  ❌ 缺少字段: {key}")
            return False
    
    # 比较 planning_result
    logger.info("planning_result 字段对比:")
    gen_plan = generated['planning_result']
    ref_plan = reference['planning_result']
    
    for key in ref_plan.keys():
        if key in gen_plan:
            logger.info(f"  ✅ {key}: {type(gen_plan[key]).__name__}")
        else:
            logger.error(f"  ❌ 缺少字段: {key}")
            return False
    
    # 比较路径结构（如果存在）
    if gen_plan['paths_subset'] and ref_plan['paths_subset']:
        logger.info("paths_subset 结构对比:")
        gen_path = gen_plan['paths_subset'][0]
        ref_path = ref_plan['paths_subset'][0]
        
        for key in ref_path.keys():
            if key in gen_path:
                logger.info(f"  ✅ {key}: {type(gen_path[key]).__name__}")
            else:
                logger.error(f"  ❌ 路径缺少字段: {key}")
                return False
    
    return True

def main():
    """主函数"""
    try:
        logger.info("开始最终格式验证...")
        
        # 加载参考格式
        reference_plans = load_plan1_format()
        reference_plan = reference_plans[1]  # 使用多路径规划的示例
        
        # 生成测试结果
        generated_plan = simulate_complete_planning_result()
        
        logger.info("生成的完整规划结果:")
        logger.info(json.dumps(generated_plan, ensure_ascii=False, indent=2))
        
        # 格式对比
        success = compare_formats(generated_plan, reference_plan)
        
        if success:
            print("\n✅ 最终格式验证通过")
            print("盲测生成的规划结果格式与 plan1.json 完全一致")
            
            # 保存示例结果
            output_path = Path(__file__).parent / "sample_generated_plan.json"
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(generated_plan, f, ensure_ascii=False, indent=2)
            logger.info(f"示例结果已保存到: {output_path}")
            
        else:
            print("\n❌ 最终格式验证失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        print("\n❌ 最终格式验证失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
