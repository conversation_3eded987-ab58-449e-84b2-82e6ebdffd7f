"""
学习路径规划工具模块 - 多源多汇最短路径算法
基于多个已掌握知识点到所有薄弱知识点的最优路径规划

核心目标：
1. 从多个已掌握知识点出发
2. 到达所有薄弱知识点
3. 如果薄弱点没有前置知识点，则单独设立路径
4. 最终可以有多个路径
5. 总路径长度最短（即学习总的知识点数量最少）

Neo4j 实现步骤：
1. 数据模型确认：使用 (:Concept) 节点和 [:IS_PREREQUISITE_FOR] 关系
2. 识别独立的薄弱知识点：无前置依赖的薄弱点直接加入路径
3. 多源多汇路径计算：使用 Dijkstra 算法计算最短路径
4. 路径优化：贪心算法最小化总学习知识点数量
"""
from typing import List, Dict, Any, Optional, Tuple, Set
from langchain_core.tools import tool
from graphiti_core import Graphiti
import logging
import json

logger = logging.getLogger(__name__)

class OptimalPathPlanner:
    """优化的多源多汇学习路径规划器

    实现基于Neo4j的高效多源多汇最短路径算法
    """

    def __init__(self, client: Graphiti):
        self.client = client
        self.mastery_threshold = 0.7  # 已掌握阈值
        self.weak_threshold = 0.5     # 薄弱点阈值
    
    async def generate_optimal_learning_paths(
        self,
        student_id: str,
        max_path_length: int = 10
    ) -> Dict[str, Any]:
        """
        生成多源多汇最优学习路径规划

        核心算法：
        1. 识别已掌握知识点（源点）和薄弱知识点（汇点）
        2. 识别独立薄弱点（无前置依赖）
        3. 计算多源到多汇的最短路径
        4. 优化路径组合，最小化总学习量
        5. 生成多条独立的学习路径
        """

        logger.info(f"=== 开始为学生 {student_id} 生成最优学习路径 ===")

        try:
            # 1. 获取学生当前掌握情况
            logger.info("步骤1: 获取学生掌握情况...")
            current_mastery = await self._get_student_mastery(student_id)

            if not current_mastery:
                logger.warning(f"学生 {student_id} 没有掌握度数据")
                return self._create_empty_plan(student_id, "没有找到学生的掌握度数据")

            # 2. 分类知识点
            mastered_concepts = [
                concept for concept, mastery in current_mastery.items()
                if mastery >= self.mastery_threshold
            ]

            weak_concepts = [
                concept for concept, mastery in current_mastery.items()
                if mastery < self.weak_threshold
            ]

            logger.info(f"已掌握知识点: {len(mastered_concepts)} 个")
            logger.info(f"薄弱知识点: {len(weak_concepts)} 个")

            if not weak_concepts:
                logger.info("没有发现薄弱知识点，学生掌握情况良好")
                return self._create_empty_plan(student_id, "恭喜！您的知识掌握情况很好，暂时没有需要重点学习的薄弱知识点。")

            # 3. 识别独立薄弱知识点（无前置依赖）
            logger.info("步骤2: 识别独立薄弱知识点...")
            independent_weak_concepts = await self._identify_independent_weak_concepts(weak_concepts)

            # 4. 计算多源多汇最短路径
            logger.info("步骤3: 计算多源多汇最短路径...")
            path_results = await self._calculate_multi_source_paths(
                mastered_concepts, weak_concepts, independent_weak_concepts, max_path_length
            )

            # 5. 优化路径组合，最小化总学习量
            logger.info("步骤4: 优化路径组合...")
            optimized_plan = await self._optimize_path_combinations(path_results, independent_weak_concepts)

            # 6. 生成最终学习计划
            learning_plan = await self._generate_final_learning_plan(
                student_id, current_mastery, optimized_plan, independent_weak_concepts
            )

            logger.info(f"最优学习路径生成完成！")
            logger.info(f"- 独立薄弱点: {len(independent_weak_concepts)} 个")
            logger.info(f"- 学习路径: {len(learning_plan['learning_paths'])} 条")
            logger.info(f"- 总学习概念数: {learning_plan['total_concepts_to_learn']} 个")

            return learning_plan

        except Exception as e:
            logger.error(f"生成学习路径时发生错误: {e}", exc_info=True)
            return self._create_empty_plan(student_id, f"路径规划过程中发生错误: {str(e)}")

    def _create_empty_plan(self, student_id: str, message: str) -> Dict[str, Any]:
        """创建空的学习计划"""
        return {
            'student_id': student_id,
            'plan_type': 'empty',
            'message': message,
            'mastered_concepts': [],
            'weak_concepts': [],
            'independent_weak_concepts': [],
            'learning_paths': [],
            'total_concepts_to_learn': 0,
            'estimated_time': 0,
            'path_summary': []
        }

    async def _identify_independent_weak_concepts(self, weak_concepts: List[str]) -> List[Dict[str, Any]]:
        """
        识别独立的薄弱知识点（无前置依赖）

        这些知识点可以直接学习，不需要路径规划
        """
        if not weak_concepts:
            return []

        # Neo4j查询：找到没有前置知识点的薄弱概念
        query = """
        MATCH (kp:Concept)
        WHERE kp.kpId IN $weak_concepts
        AND NOT (:Concept)-[:IS_PREREQUISITE_FOR]->(kp)
        RETURN kp.kpId AS concept_id,
               kp.name AS concept_name,
               kp.level AS level
        ORDER BY kp.level ASC
        """

        try:
            result = await self.client.driver.execute_query(
                query, weak_concepts=weak_concepts
            )

            independent_concepts = []
            for record in result.records:
                independent_concepts.append({
                    'concept_id': record['concept_id'],
                    'concept_name': record['concept_name'],
                    'level': record['level'],
                    'path_type': 'independent'
                })

            logger.info(f"找到 {len(independent_concepts)} 个独立薄弱知识点")
            return independent_concepts

        except Exception as e:
            logger.error(f"识别独立薄弱知识点失败: {e}")
            return []

    async def _calculate_multi_source_paths(
        self,
        mastered_concepts: List[str],
        weak_concepts: List[str],
        independent_weak_concepts: List[Dict[str, Any]],
        max_path_length: int
    ) -> Dict[str, Any]:
        """
        计算多源多汇最短路径

        对每个已掌握知识点运行Dijkstra算法，找到到所有薄弱知识点的最短路径
        """
        if not mastered_concepts:
            logger.warning("没有已掌握的知识点，无法计算路径")
            return {'paths': [], 'path_costs': {}}

        # 排除已经独立处理的薄弱点
        independent_concept_ids = {concept['concept_id'] for concept in independent_weak_concepts}
        dependent_weak_concepts = [
            concept for concept in weak_concepts
            if concept not in independent_concept_ids
        ]

        if not dependent_weak_concepts:
            logger.info("所有薄弱知识点都是独立的，无需路径计算")
            return {'paths': [], 'path_costs': {}}

        logger.info(f"计算从 {len(mastered_concepts)} 个已掌握点到 {len(dependent_weak_concepts)} 个薄弱点的路径")

        # 使用Neo4j计算所有最短路径
        query = """
        // 对每个已掌握知识点运行最短路径算法
        MATCH (start:Concept) WHERE start.kpId IN $mastered_concepts
        MATCH (target:Concept) WHERE target.kpId IN $weak_concepts

        // 计算最短路径
        MATCH path = shortestPath(
            (start)-[:IS_PREREQUISITE_FOR*1..10]->(target)
        )
        WHERE length(path) <= $max_path_length

        // 返回路径信息
        RETURN start.kpId AS start_concept,
               target.kpId AS target_concept,
               [node IN nodes(path) | {
                   concept_id: node.kpId,
                   concept_name: node.name,
                   level: node.level
               }] AS path_nodes,
               length(path) AS path_length
        ORDER BY path_length ASC, start_concept, target_concept
        """

        try:
            result = await self.client.driver.execute_query(
                query,
                mastered_concepts=mastered_concepts,
                weak_concepts=dependent_weak_concepts,
                max_path_length=max_path_length
            )

            # 组织路径结果
            paths_by_target = {}
            all_paths = []

            for record in result.records:
                start_concept = record['start_concept']
                target_concept = record['target_concept']
                path_nodes = record['path_nodes']
                path_length = record['path_length']

                path_info = {
                    'start_concept': start_concept,
                    'target_concept': target_concept,
                    'path_nodes': path_nodes,
                    'path_length': path_length,
                    'path_cost': path_length  # 简单的成本计算
                }

                all_paths.append(path_info)

                # 为每个目标保留最短路径
                if target_concept not in paths_by_target or path_length < paths_by_target[target_concept]['path_length']:
                    paths_by_target[target_concept] = path_info

            logger.info(f"找到 {len(all_paths)} 条路径，覆盖 {len(paths_by_target)} 个薄弱知识点")

            return {
                'paths': all_paths,
                'best_paths_by_target': paths_by_target,
                'path_costs': {target: info['path_cost'] for target, info in paths_by_target.items()}
            }

        except Exception as e:
            logger.error(f"计算多源路径失败: {e}")
            return {'paths': [], 'path_costs': {}}

    async def _optimize_path_combinations(
        self,
        path_results: Dict[str, Any],
        independent_weak_concepts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        优化路径组合，最小化总学习知识点数量

        使用贪心算法：
        1. 优先选择覆盖最多薄弱点的路径
        2. 处理路径重叠，共享知识点只学习一次
        3. 最小化总学习成本
        """
        best_paths_by_target = path_results.get('best_paths_by_target', {})

        if not best_paths_by_target:
            logger.info("没有找到有效路径，只返回独立知识点")
            return {
                'paths': [],
                'total_concepts': len(independent_weak_concepts),
                'estimated_time': len(independent_weak_concepts) * 20,  # 每个概念20分钟
                'summary': f"需要学习 {len(independent_weak_concepts)} 个独立知识点"
            }

        # 贪心算法选择最优路径组合
        selected_paths = []
        covered_concepts = set()
        total_learning_concepts = set()

        # 按路径效率排序（覆盖薄弱点数 / 路径长度）
        path_efficiency = []
        for target, path_info in best_paths_by_target.items():
            efficiency = 1.0 / path_info['path_length']  # 路径越短效率越高
            path_efficiency.append((efficiency, target, path_info))

        path_efficiency.sort(reverse=True)  # 按效率降序排列

        # 贪心选择路径
        for efficiency, target, path_info in path_efficiency:
            if target not in covered_concepts:
                selected_paths.append(path_info)
                covered_concepts.add(target)

                # 添加路径中的所有概念到学习集合
                for node in path_info['path_nodes']:
                    total_learning_concepts.add(node['concept_id'])

        # 添加独立知识点
        for concept in independent_weak_concepts:
            total_learning_concepts.add(concept['concept_id'])

        # 计算总学习时间（基于概念数量和复杂度）
        estimated_time = len(total_learning_concepts) * 25  # 每个概念平均25分钟

        # 生成路径摘要
        summary_parts = []
        if selected_paths:
            summary_parts.append(f"{len(selected_paths)} 条学习路径")
        if independent_weak_concepts:
            summary_parts.append(f"{len(independent_weak_concepts)} 个独立知识点")

        summary = f"总共需要学习 {len(total_learning_concepts)} 个知识点，包含 " + "、".join(summary_parts)

        logger.info(f"路径优化完成：选择了 {len(selected_paths)} 条路径，总学习概念 {len(total_learning_concepts)} 个")

        return {
            'paths': selected_paths,
            'total_concepts': len(total_learning_concepts),
            'estimated_time': estimated_time,
            'summary': summary,
            'covered_weak_concepts': list(covered_concepts)
        }

    async def _generate_final_learning_plan(
        self,
        student_id: str,
        current_mastery: Dict[str, float],
        optimized_plan: Dict[str, Any],
        independent_weak_concepts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        生成最终的学习计划
        """
        # 构建详细的学习路径
        detailed_paths = []

        # 处理路径型学习计划
        for i, path_info in enumerate(optimized_plan['paths'], 1):
            path_steps = []
            for node in path_info['path_nodes']:
                concept_id = node['concept_id']
                current_mastery_level = current_mastery.get(concept_id, 0.0)

                path_steps.append({
                    'concept_id': concept_id,
                    'concept_name': node['concept_name'],
                    'level': node['level'],
                    'current_mastery': current_mastery_level,
                    'is_mastered': current_mastery_level >= self.mastery_threshold,
                    'is_weak': current_mastery_level < self.weak_threshold,
                    'priority': 'high' if current_mastery_level < self.weak_threshold else 'medium'
                })

            detailed_paths.append({
                'path_id': f"path_{i}",
                'path_type': 'sequential',
                'start_concept': path_info['start_concept'],
                'target_concept': path_info['target_concept'],
                'steps': path_steps,
                'estimated_time': len(path_steps) * 25
            })

        # 独立知识点不作为路径处理，而是单独返回在 independent_weak_concepts 字段中

        # 生成易错点纠正任务
        misconceptions = await self._get_student_misconceptions(student_id)
        priority_tasks = await self._generate_misconception_correction_tasks(misconceptions)

        # 构建最终学习计划
        learning_plan = {
            'student_id': student_id,
            'plan_type': 'multi_source_multi_sink_optimal',
            'message': '已为您生成最优的多路径学习计划，总学习量最小化。',
            'learning_paths': detailed_paths,
            'total_concepts_to_learn': optimized_plan['total_concepts'],
            'estimated_time': optimized_plan['estimated_time'],
            'priority_tasks': priority_tasks,
            'path_summary': optimized_plan['summary'],
            'independent_weak_concepts': independent_weak_concepts,
            'optimization_info': {
                'algorithm': 'greedy_path_selection',
                'total_paths': len(detailed_paths),
                'covered_weak_concepts': optimized_plan.get('covered_weak_concepts', [])
            }
        }

        # 保存学习路径
        await self._save_learning_path(student_id, learning_plan)

        return learning_plan

    # 保留原有的单目标路径计算方法，用于向后兼容
    async def generate_learning_path(
        self,
        student_id: str,
        target_concept: str,
        max_path_length: int = 5
    ) -> Dict[str, Any]:
        """
        为单个目标概念生成学习路径（向后兼容方法）

        这个方法保持与原有接口的兼容性，但内部使用新的优化算法
        """
        try:
            # 获取学生掌握情况
            current_mastery = await self._get_student_mastery(student_id)

            if not current_mastery:
                return {
                    "plan_type": "error",
                    "message": "无法获取学生掌握情况",
                    "learning_path": []
                }

            # 检查目标概念是否为薄弱点
            target_mastery = current_mastery.get(target_concept, 0.0)
            if target_mastery >= self.mastery_threshold:
                return {
                    "plan_type": "already_mastered",
                    "message": f"您已经掌握了 '{target_concept}'，无需额外学习。",
                    "current_mastery_level": target_mastery,
                    "learning_path": []
                }

            # 使用新的多源多汇算法，但只关注单个目标
            weak_concepts = [target_concept]
            mastered_concepts = [
                concept for concept, mastery in current_mastery.items()
                if mastery >= self.mastery_threshold
            ]

            # 检查是否为独立知识点
            independent_concepts = await self._identify_independent_weak_concepts(weak_concepts)
            if independent_concepts:
                concept_details = await self._get_concept_details(target_concept)
                return {
                    "plan_type": "independent_concept",
                    "message": f"'{target_concept}' 是一个基础知识点，可以直接开始学习。",
                    "target_concept": concept_details,
                    "learning_path": [{
                        'concept_id': target_concept,
                        'concept_name': concept_details.get('name', target_concept),
                        'level': concept_details.get('level', 1),
                        'current_mastery': target_mastery,
                        'priority': 'high'
                    }]
                }

            # 计算到目标的路径
            path_results = await self._calculate_multi_source_paths(
                mastered_concepts, weak_concepts, [], max_path_length
            )

            best_paths = path_results.get('best_paths_by_target', {})
            if target_concept in best_paths:
                path_info = best_paths[target_concept]
                enriched_path = []

                for node in path_info['path_nodes']:
                    concept_id = node['concept_id']
                    current_mastery_level = current_mastery.get(concept_id, 0.0)

                    enriched_path.append({
                        'concept_id': concept_id,
                        'concept_name': node['concept_name'],
                        'level': node['level'],
                        'current_mastery': current_mastery_level,
                        'is_mastered': current_mastery_level >= self.mastery_threshold,
                        'priority': 'high' if current_mastery_level < self.weak_threshold else 'medium'
                    })

                return {
                    "plan_type": "direct_path",
                    "message": f"已为您规划出到达 '{target_concept}' 的学习路径。",
                    "target_concept": target_concept,
                    "current_mastery_level": target_mastery,
                    "learning_path": enriched_path,
                    "estimated_time": len(enriched_path) * 25
                }
            else:
                # 无法找到路径，返回基础建议
                return {
                    "plan_type": "no_path_found",
                    "message": f"暂时无法找到到达 '{target_concept}' 的学习路径，建议先学习基础知识。",
                    "target_concept": target_concept,
                    "learning_path": [],
                    "recommendations": ["建议先学习相关的基础知识点", "可以尝试从更基础的概念开始"]
                }

        except Exception as e:
            logger.error(f"生成单目标学习路径失败: {e}", exc_info=True)
            return {
                "plan_type": "error",
                "message": f"路径规划过程中发生错误: {str(e)}",
                "learning_path": []
            }

    # 保留一些有用的辅助方法，但简化实现



    async def _get_concept_details(self, concept_id: str) -> Dict[str, Any]:
        """获取概念的详细信息"""

        query = """
        MATCH (c:Concept {kpId: $concept_id})
        RETURN c.name as name, c.level as level, c.description as description
        """

        try:
            result = await self.client.driver.execute_query(query, concept_id=concept_id)

            if result.records:
                record = result.records[0]
                return {
                    'name': record['name'],
                    'level': record['level'],
                    'description': record.get('description', '')
                }
            else:
                return {'name': concept_id, 'level': 1, 'description': ''}

        except Exception as e:
            logger.error(f"获取概念详情失败: {e}")
            return {'name': concept_id, 'level': 1, 'description': ''}

    async def _find_direct_prerequisites(self, target_concept: str) -> List[Dict[str, Any]]:
        """查找目标概念的所有直接前置知识点"""

        query = """
        MATCH (prereq:Concept)-[:IS_PREREQUISITE_FOR]->(target:Concept {kpId: $target_concept})
        RETURN prereq.kpId AS concept_id,
               prereq.name AS concept_name,
               prereq.level AS level,
               prereq.description AS description
        ORDER BY prereq.level ASC
        """

        try:
            result = await self.client.driver.execute_query(query, target_concept=target_concept)

            prerequisites = []
            for record in result.records:
                prerequisites.append({
                    'concept_id': record['concept_id'],
                    'concept_name': record['concept_name'],
                    'level': record['level'],
                    'description': record.get('description', '')
                })

            logger.info(f"找到目标 '{target_concept}' 的 {len(prerequisites)} 个直接前置知识点")
            return prerequisites

        except Exception as e:
            logger.error(f"查找直接前置知识点失败: {e}")
            return []

    async def _find_foundational_concepts(self) -> List[Dict[str, Any]]:
        """查找所有基石知识点（没有前置依赖的知识点）"""

        query = """
        MATCH (concept:Concept)
        WHERE NOT (concept)<-[:IS_PREREQUISITE_FOR]-(:Concept)
        RETURN concept.kpId AS concept_id,
               concept.name AS concept_name,
               concept.level AS level,
               concept.description AS description
        ORDER BY concept.level ASC
        LIMIT 10
        """

        try:
            result = await self.client.driver.execute_query(query)

            foundational = []
            for record in result.records:
                foundational.append({
                    'concept_id': record['concept_id'],
                    'concept_name': record['concept_name'],
                    'level': record['level'],
                    'description': record.get('description', '')
                })

            logger.info(f"找到 {len(foundational)} 个基石知识点")
            return foundational

        except Exception as e:
            logger.error(f"查找基石知识点失败: {e}")
            return []

    async def _get_student_mastery(self, student_id: str) -> Dict[str, float]:
        """获取学生当前的知识点掌握情况"""

        # 首先检查会话状态中是否已有数据
        from graphmasal.agents.session_state import get_session_state_manager
        session_state = get_session_state_manager()

        cached_mastery = session_state.get_student_mastery(student_id)
        if cached_mastery:
            logger.info(f"从会话状态获取学生 {student_id} 的掌握情况")
            return cached_mastery

        # 如果没有缓存，则从数据库查询
        logger.info(f"从数据库查询学生 {student_id} 的掌握情况")
        query = """
        MATCH (student:Student {studentId: $student_id})-[mastery:HAS_MASTERY_OF]->(concept:Concept)
        RETURN concept.kpId as concept_id, mastery.proficiency as proficiency
        ORDER BY mastery.lastUpdated DESC
        """
        
        try:
            result = await self.client.driver.execute_query(
                query, student_id=student_id
            )
            
            mastery_map = {}
            for record in result.records:
                mastery_map[record['concept_id']] = record['proficiency']
            
            logger.info(f"获取到学生 {student_id} 的掌握情况: {len(mastery_map)} 个概念")

            # 将查询结果保存到会话状态
            session_state.set_student_mastery(student_id, mastery_map)

            return mastery_map
            
        except Exception as e:
            logger.error(f"获取学生掌握情况失败: {e}")
            return {}
    
    async def _get_student_misconceptions(self, student_id: str) -> List[Dict[str, Any]]:
        """获取学生的易错点"""

        # 首先检查会话状态中是否已有数据
        from graphmasal.agents.session_state import get_session_state_manager
        session_state = get_session_state_manager()

        cached_misconceptions = session_state.get_student_misconceptions(student_id)
        if cached_misconceptions:
            logger.info(f"从会话状态获取学生 {student_id} 的易错点")
            return cached_misconceptions

        # 如果没有缓存，则从数据库查询
        logger.info(f"从数据库查询学生 {student_id} 的易错点")

        # 首先检查学生节点是否存在
        check_student_query = """
        MATCH (student:Student {studentId: $student_id})
        RETURN student.studentId as student_id, student.name as student_name
        """

        try:
            # 检查学生是否存在
            student_check_result = await self.client.driver.execute_query(
                check_student_query, student_id=student_id
            )

            if not student_check_result.records:
                logger.warning(f"学生 {student_id} 在数据库中不存在")
                return []
            else:
                student_record = student_check_result.records[0]
                logger.info(f"找到学生: {student_record['student_id']} ({student_record.get('student_name', 'N/A')})")

            # 查询易错点关系
            query = """
            MATCH (student:Student {studentId: $student_id})-[exhibits:EXHIBITS_MISCONCEPTION]->(misconception:Misconception)
            RETURN misconception.misconceptionId as misconception_id,
                   misconception.description as description,
                   exhibits.strength as strength
            ORDER BY exhibits.strength DESC
            LIMIT 10
            """

            result = await self.client.driver.execute_query(
                query, student_id=student_id
            )

            misconceptions = []
            for record in result.records:
                misconceptions.append({
                    'misconception_id': record['misconception_id'],
                    'description': record['description'],
                    'strength': record['strength']
                })

            logger.info(f"获取到学生 {student_id} 的易错点: {len(misconceptions)} 个")

            # 如果没有找到易错点，进一步调试
            if len(misconceptions) == 0:
                # 检查是否有EXHIBITS_MISCONCEPTION关系
                debug_query = """
                MATCH (student:Student {studentId: $student_id})-[r:EXHIBITS_MISCONCEPTION]->()
                RETURN count(r) as relationship_count
                """
                debug_result = await self.client.driver.execute_query(
                    debug_query, student_id=student_id
                )

                if debug_result.records:
                    rel_count = debug_result.records[0]['relationship_count']
                    logger.info(f"学生 {student_id} 的EXHIBITS_MISCONCEPTION关系数量: {rel_count}")

                    if rel_count > 0:
                        # 如果有关系但查询不到，可能是Misconception节点的问题
                        logger.warning(f"学生 {student_id} 有 {rel_count} 个易错点关系，但查询结果为空，可能是Misconception节点数据问题")

            # 将查询结果保存到会话状态
            session_state.set_student_misconceptions(student_id, misconceptions)

            return misconceptions
            
        except Exception as e:
            logger.error(f"获取学生易错点失败: {e}")
            return []
    

    
    async def _generate_misconception_correction_tasks(
        self,
        misconceptions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """生成易错点纠正任务"""

        tasks = []
        for misconception in misconceptions[:3]:  # 只处理前3个最严重的易错点
            try:
                # 通过题目ID查找易错点信息
                problem_id = misconception.get('problem_id')
                if not problem_id:
                    logger.warning(f"易错点数据缺少problem_id: {misconception}")
                    continue

                # 查询题目对应的易错点选项和相关概念
                query = """
                MATCH (p:Problem {problemId: $problem_id})-[:OPTION_TARGETS]->(m:Misconception)
                MATCH (m)-[:IS_CONFUSION_OF]->(c:Concept)
                RETURN m.misconceptionId as misconception_id,
                       m.description as misconception_description,
                       c.kpId as concept_id,
                       c.name as concept_name
                LIMIT 3
                """

                result = await self.client.driver.execute_query(
                    query, problem_id=problem_id
                )

                if result.records:
                    for record in result.records:
                        tasks.append({
                            'type': 'misconception_correction',
                            'problem_id': problem_id,
                            'misconception_id': record['misconception_id'],
                            'description': record['misconception_description'],
                            'target_concept': record['concept_id'],
                            'concept_name': record['concept_name'],
                            'priority': 'urgent',
                            'estimated_time': 15  # 分钟
                        })
                else:
                    # 如果没有找到具体的易错点，创建一个通用的纠正任务
                    tasks.append({
                        'type': 'misconception_correction',
                        'problem_id': problem_id,
                        'description': misconception.get('description', '需要重点关注的易错点'),
                        'priority': 'high',
                        'estimated_time': 10  # 分钟
                    })

            except Exception as e:
                logger.error(f"生成易错点纠正任务失败: {e}")
                # 继续处理下一个易错点，不中断整个流程
                continue

        return tasks
    

    
    async def _save_learning_path(self, student_id: str, learning_plan: Dict[str, Any]):
        """保存学习路径到知识图谱"""

        # 简化保存逻辑，避免复杂的Graphiti episode保存
        try:
            from datetime import datetime, timezone
            import json

            # 只保存关键信息到日志，避免Neo4j数据类型错误
            plan_summary = {
                'student_id': str(learning_plan.get('student_id', '')),
                'plan_type': str(learning_plan.get('plan_type', 'unknown')),
                'estimated_time': int(learning_plan.get('estimated_time', 0)),
                'learning_path_count': len(learning_plan.get('learning_path', [])),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            logger.info(f"HAP学习路径已生成: {json.dumps(plan_summary, ensure_ascii=False)}")

        except Exception as e:
            logger.error(f"保存学习路径失败: {e}")
            # 不抛出异常，让标准答案生成继续进行

# LangChain工具装饰器
@tool
async def generate_learning_path(
    student_id: str,
    target_concept: str,
    max_path_length: int = 5
) -> str:
    """
    为学生生成个性化学习路径的工具（单目标）

    Args:
        student_id: 学生ID
        target_concept: 目标学习概念
        max_path_length: 最大路径长度

    Returns:
        学习路径计划的JSON字符串
    """
    try:
        # 从客户端管理器获取Graphiti客户端
        from graphmasal.agents.client_manager import get_graphiti_client, is_graphiti_client_initialized

        logger.debug(f"尝试获取Graphiti客户端，客户端初始化状态: {is_graphiti_client_initialized()}")

        if not is_graphiti_client_initialized():
            logger.error("Graphiti客户端未初始化")
            raise RuntimeError("Graphiti客户端未初始化")

        try:
            client = get_graphiti_client()
            logger.debug(f"成功获取Graphiti客户端: {client is not None}")
        except RuntimeError as e:
            logger.error(f"获取Graphiti客户端失败: {e}")
            raise RuntimeError("Graphiti客户端未初始化")

        # 使用新的优化路径规划器
        planner = OptimalPathPlanner(client)

        result = await planner.generate_learning_path(
            student_id=student_id,
            target_concept=target_concept,
            max_path_length=max_path_length
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"学习路径规划工具执行失败: {e}")
        return json.dumps({
            'error': f'路径规划失败: {str(e)}',
            'student_id': student_id,
            'target_concept': target_concept,
            'learning_path': [],
            'recommendations': ['请重新尝试或联系技术支持']
        }, ensure_ascii=False)

@tool
async def generate_optimal_learning_paths(
    student_id: str,
    max_path_length: int = 10
) -> str:
    """
    为学生生成多源多汇最优学习路径的工具（多目标优化）

    Args:
        student_id: 学生ID
        max_path_length: 最大路径长度

    Returns:
        优化学习路径计划的JSON字符串
    """
    try:
        # 从客户端管理器获取Graphiti客户端
        from graphmasal.agents.client_manager import get_graphiti_client, is_graphiti_client_initialized

        logger.debug(f"尝试获取Graphiti客户端，客户端初始化状态: {is_graphiti_client_initialized()}")

        if not is_graphiti_client_initialized():
            logger.error("Graphiti客户端未初始化")
            raise RuntimeError("Graphiti客户端未初始化")

        try:
            client = get_graphiti_client()
            logger.debug(f"成功获取Graphiti客户端: {client is not None}")
        except RuntimeError as e:
            logger.error(f"获取Graphiti客户端失败: {e}")
            raise RuntimeError("Graphiti客户端未初始化")

        # 使用新的优化路径规划器
        planner = OptimalPathPlanner(client)

        result = await planner.generate_optimal_learning_paths(
            student_id=student_id,
            max_path_length=max_path_length
        )

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"多路径学习规划工具执行失败: {e}")
        return json.dumps({
            'error': f'多路径规划失败: {str(e)}',
            'student_id': student_id,
            'learning_paths': [],
            'recommendations': ['请重新尝试或联系技术支持']
        }, ensure_ascii=False)
