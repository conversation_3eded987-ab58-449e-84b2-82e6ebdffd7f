# GraphMASAL - 多智能体教育辅导系统

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Neo4j](https://img.shields.io/badge/database-Neo4j-blue.svg)](https://neo4j.com)
[![Graphiti](https://img.shields.io/badge/powered%20by-Graphiti-orange.svg)](https://github.com/getzep/graphiti)
[![LangChain](https://img.shields.io/badge/framework-LangChain-yellow.svg)](https://langchain.com)

> **GraphMASAL** (Graph-based Multi-Agent System for Adaptive Learning) 是一个基于动态知识图谱的多智能体教育辅导系统，专为个性化学习和智能诊断而设计。

## 🎯 项目概述

GraphMASAL 是一个创新的教育技术解决方案，结合了多智能体系统、动态知识图谱和大语言模型技术，为学习者提供个性化的智能辅导体验。系统通过三个专门的智能体协同工作，实现精准的学习诊断、智能的路径规划和个性化的辅导服务。

### 🌟 核心价值

- **🔍 精准诊断**: 基于知识图谱的深度错误分析和概念理解评估
- **🗺️ 智能规划**: 个性化学习路径生成和动态调整
- **🤖 智能辅导**: 多模态交互和实时学习支持
- **📊 数据驱动**: 完整的评估框架和学习分析，包含增强版盲测评估
- **🔧 高度可扩展**: 模块化架构支持多学科扩展

## ✨ 核心特性

### 🔬 诊断智能体 (Diagnostic Agent)
- **错误模式识别**: 自动识别学生的常见错误类型和思维误区
- **概念掌握评估**: 基于知识图谱的概念理解深度分析
- **学习障碍定位**: 精确定位学习过程中的知识盲点
- **个性化报告**: 生成详细的诊断报告和改进建议

### 🗺️ 规划智能体 (Planning Agent)
- **自适应路径生成**: 基于学生当前水平的个性化学习路径
- **多源多汇算法**: 支持复杂的知识依赖关系和学习目标
- **动态路径调整**: 根据学习进度实时优化学习路径
- **资源推荐**: 智能推荐学习资源和练习题目

### 👨‍🏫 辅导智能体 (Tutor Agent)
- **自然语言交互**: 支持多轮对话和上下文理解
- **个性化解答**: 根据学生水平调整解答深度和方式
- **学习引导**: 启发式教学和苏格拉底式提问
- **情感支持**: 学习动机激励和情感状态识别

## 🏗️ 系统架构

```mermaid
flowchart TD
    subgraph s1["用户端"]
        A["学习者界面"]
    end
    
    subgraph s2["多智能体系统"]
        B["辅导智能体"]
        C["诊断智能体"]
        D["规划智能体"]
    end
    
    subgraph s3["核心驱动"]
        E["动态知识图谱<br/>Graphiti"]
    end
    
    subgraph s4["数据存储"]
        F["Neo4j数据库"]
    end
    
    A <-- 交互/反馈 --> B
    B -- 诊断请求 --> C
    C -- 查询 --> E
    C -- 诊断结果 --> B
    B -- 规划请求 --> D
    D -- 查询/更新 --> E
    D -- 学习路径 --> B
    B -- 呈现路径/内容 --> A
    E --- F
```

### 🔧 技术架构特点

- **动态知识图谱**: 使用 Graphiti 实现知识的动态更新和推理
- **多智能体协作**: LangGraph 框架支持的智能体编排
- **图数据库**: Neo4j 提供高性能的图数据存储和查询
- **大语言模型**: 支持多种 LLM API（OpenAI、豆包等）
- **模块化设计**: 松耦合架构支持功能扩展和定制

## 🚀 快速开始

### 📋 环境要求

- **Python**: 3.8 或更高版本
- **Neo4j**: 5.0+ 数据库
- **内存**: 建议 4GB 以上
- **API**: OpenAI 兼容的 LLM API 服务

### ⚡ 一键安装

```bash
# 1. 克隆项目
git clone https://github.com/YOUR_USERNAME/graphmasal.git
cd graphmasal

# 2. 安装依赖（开发模式）
pip install -e .

# 3. 配置环境
cp graphmasal/.env.example .env
# 编辑 .env 文件，配置数据库和API信息

# 4. 启动系统
graphmasal --demo chat
```

### 🎮 立即体验

```bash
# 基本聊天演示
graphmasal --demo chat

# 诊断功能演示
graphmasal --demo diagnostic

# 学习路径规划演示
graphmasal --demo planning

# 运行所有演示
graphmasal --demo all

# 系统测试
graphmasal-test
```

更多详细信息请参考 [快速开始指南](QUICK_START.md)。

## 📚 详细功能介绍

### 🔍 智能诊断功能

诊断智能体能够深入分析学生的答题过程，识别错误模式并提供精准的改进建议：

```python
# 诊断示例
problem_data = {
    "problem_id": 10200942,
    "selected_options": ["A"],  # 学生选择
    "correct_answer": ["C"],    # 正确答案
    "misconception_map": {
        "A": "未考虑容器加速度对等效重力的影响",
        "B": "混淆加速度方向对有效重力的增强/减弱作用"
    }
}

result = await tutor_agent.diagnose_problem(
    student_info=student,
    problem_data=problem_data
)
```

**诊断输出包括：**
- 错误类型分类
- 概念理解评估
- 知识盲点定位
- 个性化建议

### 🗺️ 学习路径规划

规划智能体基于学生当前水平和目标，生成最优的学习路径：

```python
# 路径规划示例
result = await tutor_agent.plan_learning_path(
    student_info=student,
    target_concept="力学/相互作用与牛顿定律/牛顿第二定律应用"
)
```

**规划特性：**
- 多源多汇算法支持
- 动态难度调整
- 个性化资源推荐
- 学习进度跟踪

### 👨‍🏫 智能辅导对话

辅导智能体提供自然、个性化的学习支持：

```python
# 对话示例
response = await tutor_agent.chat(
    message="我不理解牛顿第二定律的应用",
    student_info=student,
    thread_id="learning_session"
)
```

**对话特性：**
- 上下文感知
- 个性化解答
- 启发式引导
- 多轮交互支持

## 🛠️ 技术栈

### 核心依赖

| 组件 | 版本 | 用途 |
|------|------|------|
| **graphiti-core** | ≥0.3.0 | 动态知识图谱管理 |
| **langchain-openai** | ≥0.1.0 | LLM 集成和调用 |
| **langgraph** | ≥0.2.0 | 多智能体编排 |
| **neo4j** | ≥5.0.0 | 图数据库驱动 |
| **python-dotenv** | ≥1.0.0 | 环境配置管理 |

### 可选依赖

```bash
# 开发工具
pip install -e ".[dev]"  # pytest, black, flake8, mypy

# 测试工具  
pip install -e ".[test]"  # pytest-asyncio, pytest-cov

# Jupyter 支持
pip install -e ".[jupyter]"  # notebook, ipywidgets
```

### 支持的 API 服务

- **OpenAI**: GPT-4, GPT-3.5-turbo 等
- **豆包 (Doubao)**: doubao-seed-1.6-250615
- **其他**: 任何兼容 OpenAI 格式的 API 服务

## ⚙️ 安装与配置

### 详细安装指南

请参考 [安装指南](INSTALL.md) 获取完整的安装说明。

### API 配置

请参考 [API 配置指南](API_CONFIG.md) 了解如何配置不同的 LLM 服务。

### 基本配置示例

```env
# Neo4j 数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# LLM API 配置（以豆包为例）
OPENAI_API_KEY=your-doubao-api-key
OPENAI_MODEL=doubao-seed-1.6-250615
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/

# Embedding 配置
EMBEDDING_MODEL=doubao-embedding-text-240715
EMBEDDING_API_KEY=your-doubao-api-key

# 系统配置
TEMPERATURE=0.0
```

## 💡 使用示例

### 基本使用

```python
import asyncio
from graphmasal.main import TutorSystem
from graphmasal.models.state import StudentInfo

async def main():
    # 初始化系统
    system = TutorSystem()
    await system.initialize()
    
    # 创建学生信息
    student = StudentInfo(
        student_id="student_001",
        name="张三",
        current_subject="物理"
    )
    
    # 智能对话
    response = await system.tutor_agent.chat(
        message="请解释一下牛顿第二定律",
        student_info=student,
        thread_id="session_001"
    )
    
    print(response)
    await system.close()

if __name__ == "__main__":
    asyncio.run(main())
```

### 批量学生诊断

```python
# 使用脚本进行批量诊断
python scripts/batch_diagnose_students.py --input data/student_data.json
```

### 知识图谱管理

```python
# 导入知识图谱数据
python scripts/bulk_import_knowledge_graph.py --file data/physics_knowledge.json

# 查看知识图谱结构
python scripts/view_knowledge_graph.py --subject 物理
```

## 📊 评估框架

GraphMASAL 包含完整的评估框架，支持系统性能和教学效果的量化分析，特别是创新的增强版盲测评估系统。

### 🎯 核心创新 - 增强版盲测评估

#### 评估特点
- **真实性测试**: 移除关键信息（linked_kp_ids、misconception_map），模拟真实应用场景
- **推理能力验证**: 测试智能体的真实推理而非信息提取能力
- **知识图谱约束**: 强制智能体只能选择现有概念节点
- **端到端评测**: 完整的诊断→规划流程评测

#### 技术优势
- **集成planning_tool**: 使用真实的OptimalPathPlanner生成高质量标准答案
- **双模式规划**: 支持单目标和多目标最优路径规划
- **多维度评测**: 准确率、相似度、完成率等多个指标

### 评估组件

- **标准答案生成器**: 基于真实规划算法生成高质量标准答案
- **盲测数据处理器**: 移除关键信息，创建真实测试环境
- **增强版盲测评估器**: 客观评估系统诊断和规划准确性
- **路径相似度评估器**: 学习路径质量的多维度评估
- **标准化器**: 统一评估标准和格式
- **数据处理器**: 评估数据的预处理和分析

### 运行评估

#### 增强版盲测评估
```bash
# 生成标准答案（集成planning_tool）
python evaluation/ground_truth_generator.py \
  --problems-file data/start_0_end_200_new_data.json \
  --output-dir evaluation/ground_truth_v2 \
  --num-students 10

# 创建盲测数据集
python evaluation/blind_test_processor.py \
  --problems-file data/start_0_end_200_new_data.json \
  --student-data-file data/generated_student_data.json \
  --output-dir evaluation/blind_test_data

# 运行完整盲测评估
python evaluation/blind_test_evaluator.py \
  --test-type complete \
  --num-test-cases 10

# 简化盲测演示
python evaluation/simple_blind_test.py
```

#### 传统评估方法
```bash
# 运行完整评估
python evaluation/main_evaluator.py

# 15学生盲测
python evaluation/run_15_students_blind_test.py

# 快速演示评估
python evaluation/quick_blind_test_demo.py
```

### 评估指标

#### 诊断评估指标
- **知识点识别准确率**: 智能体推断知识点的准确性
- **易错点识别精确率/召回率**: 错误模式识别的精确度
- **掌握度分析准确性**: 学生概念掌握程度评估的准确性

#### 规划评估指标
- **路径相似度**: 与专家路径的匹配度
- **独立点匹配度**: 关键学习节点的识别准确性
- **总体相似度**: 整体学习规划的质量评估
- **时间效率**: 规划生成的响应时间

#### 系统性能指标
- **学习效果**: 学生学习进步的量化指标
- **系统响应时间**: 各模块的性能指标
- **资源消耗**: 内存和计算资源使用情况

### 评估结果统计

最新评估结果（v2.0）：
- **诊断标准答案**: 75个高质量标准答案
- **规划标准答案**: 9个（包含单目标和多目标规划）
- **知识点覆盖**: 70/70个知识点验证通过
- **题目处理**: 200道题目，100%含知识点和易错点标注

## 🏗️ 项目结构

```
graphmasal/
├── graphmasal/                 # 核心包
│   ├── agents/                # 智能体模块
│   │   ├── tutor_agent.py    # 主辅导智能体
│   │   ├── tools/            # 智能体工具
│   │   │   ├── diagnostic_tool.py    # 诊断工具
│   │   │   ├── planning_tool.py      # 规划工具
│   │   │   └── query_tool.py         # 查询工具
│   │   └── ...
│   ├── config/               # 配置管理
│   ├── knowledge_graph/      # 知识图谱管理
│   ├── models/              # 数据模型
│   ├── utils/               # 工具函数
│   └── main.py              # 主程序入口
├── evaluation/              # 评估框架
│   ├── ground_truth_generator.py     # 标准答案生成器（增强版）
│   ├── blind_test_processor.py       # 盲测数据处理器
│   ├── blind_test_evaluator.py       # 增强版盲测评估器
│   ├── evaluators/                   # 传统评估器
│   ├── standardizers/                # 标准化器
│   ├── ground_truth_v2/              # 增强版标准答案
│   ├── blind_test_data/              # 盲测数据集
│   └── utils/                        # 评估工具
├── scripts/                # 实用脚本
├── data/                   # 数据文件
├── docs/                   # 文档
└── tests/                  # 测试文件
```

### 核心模块说明

#### 智能体模块 (`graphmasal/agents/`)
- **tutor_agent.py**: 主辅导智能体，协调其他智能体工作
- **tools/**: 智能体工具集合
  - **diagnostic_tool.py**: 学生答题诊断和错误分析
  - **planning_tool.py**: 学习路径规划和优化
  - **query_tool.py**: 知识图谱查询和检索

#### 知识图谱模块 (`graphmasal/knowledge_graph/`)
- **schema_init.py**: 知识图谱结构初始化
- **data_loader.py**: 教育数据加载和预处理

#### 评估框架 (`evaluation/`)
- **ground_truth_generator.py**: 增强版标准答案生成器，集成真实规划算法
- **blind_test_processor.py**: 盲测数据处理器，移除关键信息创建真实测试环境
- **blind_test_evaluator.py**: 增强版盲测评估器，端到端评测系统
- **evaluators/**: 传统评估器实现（诊断评估、路径相似度评估等）
- **standardizers/**: 数据标准化处理器
- **ground_truth_v2/**: 增强版高质量标准答案数据
- **blind_test_data/**: 盲测数据集和移除信息日志
- **utils/**: 评估辅助工具和相似度计算器

#### 实用脚本 (`scripts/`)
- **batch_diagnose_students.py**: 批量学生诊断
- **bulk_import_knowledge_graph.py**: 知识图谱批量导入
- **generate_student_records.py**: 学生记录生成

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

### 开发环境设置

```bash
# 1. Fork 项目并克隆
git clone https://github.com/your-username/graphmasal.git
cd graphmasal

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 3. 安装开发依赖
pip install -e ".[dev]"

# 4. 安装 pre-commit 钩子
pre-commit install
```

### 代码规范

- **代码格式**: 使用 Black 进行代码格式化
- **类型检查**: 使用 MyPy 进行类型检查
- **测试**: 编写单元测试和集成测试
- **文档**: 为新功能添加相应文档

```bash
# 代码格式化
black graphmasal/

# 类型检查
mypy graphmasal/

# 运行测试
pytest
```

### 提交流程

1. 创建功能分支: `git checkout -b feature/your-feature`
2. 提交更改: `git commit -m "Add your feature"`
3. 推送分支: `git push origin feature/your-feature`
4. 创建 Pull Request

### 贡献类型

- 🐛 Bug 修复
- ✨ 新功能开发
- 📚 文档改进
- 🧪 测试增强
- 🎨 代码优化
- 🌐 国际化支持

## 🔍 故障排除

### 常见问题

#### 1. Neo4j 连接问题
```bash
# 检查 Neo4j 服务状态
sudo systemctl status neo4j

# 启动 Neo4j 服务
sudo systemctl start neo4j

# Windows 用户
neo4j.bat console
```

#### 2. API 配置错误
- 检查 API Key 是否正确
- 验证 Base URL 配置
- 确认模型名称正确

#### 3. 依赖冲突
```bash
# 创建新的虚拟环境
python -m venv fresh_env
source fresh_env/bin/activate
pip install -e .
```

#### 4. 内存不足
- 调整批处理大小
- 优化查询复杂度
- 增加系统内存

### 日志分析

系统日志保存在 `tutor_system.log` 文件中：

```bash
# 查看实时日志
tail -f tutor_system.log

# 搜索错误信息
grep "ERROR" tutor_system.log

# 查看最近的日志
tail -n 100 tutor_system.log
```

## 📈 性能优化建议

### 数据库优化
- 为频繁查询的属性创建索引
- 使用 APOC 插件进行复杂操作
- 定期清理无用数据

### API 调用优化
- 实现请求缓存机制
- 使用批量处理减少API调用
- 设置合理的超时和重试策略

### 内存管理
- 使用流式处理大数据集
- 及时释放不需要的对象
- 监控内存使用情况

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢以下开源项目和社区的支持：

- **[Graphiti](https://github.com/getzep/graphiti)**: 动态知识图谱框架
- **[LangChain](https://langchain.com)**: LLM 应用开发框架
- **[Neo4j](https://neo4j.com)**: 图数据库技术
- **[OpenAI](https://openai.com)**: 大语言模型技术

特别感谢所有贡献者和测试用户的宝贵反馈！

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/YOUR_USERNAME/graphmasal)
- **问题反馈**: [Issues](https://github.com/YOUR_USERNAME/graphmasal/issues)
- **邮箱**: <EMAIL>

---

<div align="center">

**🎓 让AI赋能教育，让学习更加智能！**

[开始使用](QUICK_START.md) • [查看文档](docs/) • [参与贡献](#贡献指南)

</div>
