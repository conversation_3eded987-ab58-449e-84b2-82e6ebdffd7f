"""
GraphMASAL - 多智能体教育辅导系统
基于 Graphiti 和 LangGraph 构建的智能教育辅导系统
"""

from setuptools import setup, find_packages
import os

# 读取 README 文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), "graphmasal", "README.md")
    if os.path.exists(readme_path):
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return "多智能体教育辅导系统"

# 读取 requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), "graphmasal", "requirements.txt")
    if os.path.exists(requirements_path):
        with open(requirements_path, "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    return []

setup(
    name="graphmasal",
    version="0.1.0",
    author="GraphMASAL Team",
    author_email="<EMAIL>",
    description="多智能体教育辅导系统 - Multi-Agent Educational Tutoring System",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/graphmasal",
    
    # 包配置
    packages=find_packages(),
    package_dir={"": "."},
    
    # 包含非Python文件
    include_package_data=True,
    package_data={
        "graphmasal": [
            "*.md",
            "*.txt",
            "*.env.example",
            "config/*.py",
            "agents/*.py",
            "agents/tools/*.py",
            "knowledge_graph/*.py",
            "models/*.py",
            "utils/*.py",
        ],
    },
    
    # 依赖项
    install_requires=read_requirements(),
    
    # 额外依赖组
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
        ],
    },
    
    # 入口点
    entry_points={
        "console_scripts": [
            "graphmasal=graphmasal.main:main",
            "graphmasal-test=graphmasal.test_system:main",
        ],
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Education",
        "Intended Audience :: Developers",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    
    # Python版本要求
    python_requires=">=3.8",
    
    # 关键词
    keywords="education, ai, multi-agent, tutoring, knowledge-graph, langchain, graphiti",
    
    # 项目URLs
    project_urls={
        "Bug Reports": "https://github.com/your-username/graphmasal/issues",
        "Source": "https://github.com/your-username/graphmasal",
        "Documentation": "https://github.com/your-username/graphmasal/blob/main/README.md",
    },
    
    # 许可证
    license="MIT",
    
    # 是否为zip安全
    zip_safe=False,
)
