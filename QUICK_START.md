# GraphMASAL 快速开始指南

## 🎉 恭喜！GraphMASAL 已成功安装并配置

你的多智能体教育辅导系统现在已经准备就绪！

## ✅ 当前状态

- ✅ GraphMASAL 包已安装 (可编辑模式)
- ✅ 豆包 API 已配置
- ✅ 命令行工具可用
- ✅ 配置文件已设置

## 🚀 立即开始使用

### 1. 基本聊天演示

```bash
graphmasal --demo chat
```

这将启动一个交互式聊天界面，你可以：
- 问物理问题
- 请求学习建议
- 让AI分析你的学习情况

### 2. 诊断功能演示

```bash
graphmasal --demo diagnostic
```

演示系统如何分析学生的答题错误并识别易错点。

### 3. 学习路径规划演示

```bash
graphmasal --demo planning
```

展示系统如何为学生制定个性化的学习路径。

### 4. 运行所有演示

```bash
graphmasal --demo all
```

依次运行诊断、规划和聊天演示。

## 🔧 系统测试

运行完整的系统测试：

```bash
graphmasal-test
```

这将测试所有功能模块，确保系统正常工作。

## 📋 当前配置

- **API 服务**: 豆包 (Doubao)
- **LLM 模型**: doubao-seed-1.6-250615
- **Embedding 模型**: doubao-embedding-text-240715
- **Base URL**: https://ark.cn-beijing.volces.com/api/v3/
- **温度**: 0.0 (精确模式)

## 🛠️ 如果遇到问题

### Neo4j 数据库问题

如果遇到数据库连接错误：

1. **检查 Neo4j 是否运行**:
   ```bash
   # Windows
   neo4j.bat console
   
   # 或者启动 Neo4j Desktop
   ```

2. **检查数据库配置**:
   - 确认 Neo4j 运行在 `bolt://localhost:7687`
   - 用户名: `neo4j`
   - 密码: `12345678` (或你设置的密码)

### API 连接问题

如果遇到 API 错误：

1. **检查 API Key**: 确认豆包 API Key 有效
2. **检查网络**: 确认能访问豆包 API 服务
3. **重新配置**: 运行 `python fix_doubao_config.py`

### 重新配置

如果需要更改配置：

```bash
# 运行配置修复工具
python fix_doubao_config.py

# 或者手动编辑 .env 文件
nano .env
```

## 📚 更多信息

- **完整文档**: 查看 `README.md`
- **API 配置**: 查看 `API_CONFIG.md`
- **安装指南**: 查看 `INSTALL.md`

## 🎯 下一步

1. **尝试基本功能**: 运行 `graphmasal --demo chat`
2. **探索高级功能**: 尝试诊断和规划演示
3. **自定义配置**: 根据需要调整设置
4. **开发扩展**: 查看代码结构，添加新功能

## 💡 使用技巧

- 使用 `Ctrl+C` 退出聊天演示
- 查看 `tutor_system.log` 了解详细日志
- 使用 `--clear` 参数重置数据库
- 在虚拟环境中运行以避免依赖冲突

---

**🎉 开始你的AI辅导之旅吧！**

有任何问题，请查看文档或提交 Issue。
