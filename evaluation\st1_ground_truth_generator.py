#!/usr/bin/env python3
"""
ST1数据标准答案生成器
基于 data/st1_data.json 的真实答题记录生成标准的诊断和规划答案
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Set
from datetime import datetime
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.config import settings
from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# 导入planning_tool中的路径规划器
from graphmasal.agents.tools.planning_tool import OptimalPathPlanner

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    neo4j_uri = "bolt://localhost:7687"
    neo4j_user = "neo4j"
    neo4j_password = "12345678"
    
    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
    )

    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL or "doubao-embedding-text-240715",
        base_url=embedding_base_url,
    )

    llm_client = OpenAIGenericClient(llm_config)
    embedder = OpenAIEmbedder(embedder_config)
    cross_encoder = OpenAIRerankerClient(llm_config)

    return Graphiti(
        neo4j_uri,
        neo4j_user,
        neo4j_password,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

class ST1GroundTruthGenerator:
    """基于ST1数据的标准答案生成器"""
    
    def __init__(self, client: Graphiti):
        self.client = client
        self.logger = logger
        self.planner = OptimalPathPlanner(client)
    
    def load_st1_data(self, st1_file: Path) -> List[Dict[str, Any]]:
        """加载ST1数据文件"""
        try:
            with open(st1_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载ST1数据: {len(data)} 个学生")
            return data
            
        except Exception as e:
            self.logger.error(f"加载ST1数据失败: {e}")
            return []
    
    async def fetch_problems_from_neo4j(self, problem_ids: List[int]) -> List[Dict[str, Any]]:
        """从Neo4j数据库中获取题目数据"""
        try:
            self.logger.info(f"从Neo4j数据库获取 {len(problem_ids)} 道题目...")

            problems_data = []

            for problem_id in problem_ids:
                # 构建Cypher查询，获取题目及其关联的知识点和易错点
                cypher_query = """
                MATCH (p:Problem {problemId: $problemId})
                OPTIONAL MATCH (p)-[:TESTS_CONCEPT]->(c:Concept)
                OPTIONAL MATCH (p)-[:OPTION_TARGETS]->(m:Misconception)
                RETURN p,
                       collect(DISTINCT c.kpId) as linked_kp_ids,
                       collect(DISTINCT {option: m.option_id, misconception: m.description}) as misconceptions
                """

                try:
                    # 执行查询
                    result = await self.client.driver.execute_query(
                        cypher_query,
                        problemId=problem_id
                    )

                    if result.records:
                        record = result.records[0]
                        problem_node = record['p']
                        linked_kp_ids = record['linked_kp_ids']
                        misconceptions = record['misconceptions']

                        # 构建易错点映射
                        misconception_map = {}
                        for misc in misconceptions:
                            if misc['option'] and misc['misconception']:
                                misconception_map[misc['option']] = misc['misconception']

                        # 构建题目数据
                        problem_data = {
                            'problem_id': problem_node['problemId'],
                            'content': problem_node.get('content', ''),
                            'difficulty': problem_node.get('difficulty', 5.0),
                            'correct_answer': problem_node.get('correctAnswer', ['A'])[0] if problem_node.get('correctAnswer') else 'A',
                            'linked_kp_ids': [kp for kp in linked_kp_ids if kp],  # 过滤空值
                            'misconception_map': misconception_map,
                            'image_urls': problem_node.get('imageUrls', [])
                        }

                        problems_data.append(problem_data)

                    else:
                        self.logger.warning(f"题目 {problem_id} 在数据库中未找到")

                except Exception as e:
                    self.logger.error(f"查询题目 {problem_id} 失败: {e}")
                    continue

            self.logger.info(f"成功从数据库获取 {len(problems_data)} 道题目")
            return problems_data

        except Exception as e:
            self.logger.error(f"从Neo4j获取题目数据失败: {e}")
            return []
    
    async def verify_knowledge_points_in_graph(self, knowledge_points: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        验证知识点是否存在于知识图谱中。
        使用精确的Cypher查询替代依赖embedding的client.search()。
        """
        try:
            self.logger.info(f"正在使用精确查询验证 {len(knowledge_points)} 个知识点...")
            verification_results = {}
            
            # 构建一个Cypher查询，一次性验证所有知识点
            cypher_query = """
            UNWIND $kp_ids AS kp_id
            OPTIONAL MATCH (c:Concept {kpId: kp_id})
            RETURN kp_id, c.name AS name, c IS NOT NULL AS exists
            """
            
            # 执行查询
            result = await self.client.driver.execute_query(
                cypher_query,
                kp_ids=knowledge_points
            )
            
            # 处理查询结果
            for record in result.records:
                kp_id = record['kp_id']
                verification_results[kp_id] = {
                    'exists': record['exists'],
                    'name': record['name'] if record['exists'] else None
                }

            # 确保所有请求的知识点都在结果中，即使它们在图中不存在
            for kp in knowledge_points:
                if kp not in verification_results:
                    verification_results[kp] = {'exists': False, 'name': None}

            valid_kp_count = sum(1 for v in verification_results.values() if v.get('exists', False))
            self.logger.info(f"精确查询完成。有效知识点: {valid_kp_count}/{len(knowledge_points)}")
            
            return verification_results
            
        except Exception as e:
            self.logger.error(f"验证知识点失败: {e}")
            # 在出错时返回所有知识点为不存在
            return {kp: {'exists': False, 'name': None} for kp in knowledge_points}

    
    # async def verify_knowledge_points_in_graph(self, knowledge_points: List[str]) -> Dict[str, Dict[str, Any]]:
        """验证知识点是否存在于知识图谱中"""
        try:
            verification_results = {}
            
            for kp in knowledge_points:
                try:
                    # 查询知识点是否存在
                    search_results = await self.client.search(
                        query=kp,
                        num_results=1
                    )
                    
                    if search_results and len(search_results) > 0:
                        verification_results[kp] = {
                            'exists': True,
                            'name': search_results[0].fact
                        }
                    else:
                        verification_results[kp] = {
                            'exists': False,
                            'name': None
                        }
                        
                except Exception as e:
                    self.logger.warning(f"验证知识点 {kp} 时出错: {e}")
                    verification_results[kp] = {
                        'exists': False,
                        'name': None
                    }
            
            return verification_results
            
        except Exception as e:
            self.logger.error(f"验证知识点失败: {e}")
            return {kp: {'exists': False, 'name': None} for kp in knowledge_points}
    
    def convert_st1_to_problem_format(
        self,
        student_data: Dict[str, Any],
        problems_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """将ST1数据转换为题目格式"""
        # 创建题目ID到题目数据的映射
        problems_map = {problem['problem_id']: problem for problem in problems_data}

        student_problems = []

        for attempt in student_data.get('problem_attempts', []):
            problem_id = attempt['problem_id']

            if problem_id in problems_map:
                problem_data = problems_map[problem_id]

                # 转换为标准格式
                student_problem = {
                    'problem_id': problem_id,
                    'student_answer': attempt['selected_options'],
                    'is_correct': attempt['is_fully_correct'],
                    'timestamp': attempt['timestamp'],
                    'correct_answer': problem_data.get('correct_answer', 'A'),
                    'linked_kp_ids': problem_data.get('linked_kp_ids', []),
                    'difficulty': problem_data.get('difficulty', 5.0),
                    'misconception_map': problem_data.get('misconception_map', {}),
                    'content': problem_data.get('content', ''),
                    'image_urls': problem_data.get('image_urls', [])
                }

                student_problems.append(student_problem)
            else:
                self.logger.warning(f"题目 {problem_id} 在数据库中未找到")

        return student_problems
    
    def _generate_simulated_diagnostic(self, student_id: str, problems: List[Dict]) -> Dict:
        """生成模拟的诊断结果"""
        # 按概念ID分组统计答题情况
        concept_stats = {}
        for problem in problems:
            concept_id = problem.get('concept_id', 'unknown_concept')
            concept_name = problem.get('concept_hint', 'unknown_concept')
            is_correct = problem.get('is_correct', False)

            if concept_id not in concept_stats:
                concept_stats[concept_id] = {
                    'correct': 0,
                    'total': 0,
                    'concept_name': concept_name
                }

            concept_stats[concept_id]['total'] += 1
            if is_correct:
                concept_stats[concept_id]['correct'] += 1

        # 基于正确率判断掌握情况
        mastered_concepts = []
        not_mastered_concepts = []

        for concept_id, stats in concept_stats.items():
            accuracy_rate = stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0

            # 使用与ground_truth_generator相同的掌握度计算方式
            # 1. 计算每个问题的掌握度（模拟单题掌握度计算）
            mastery_levels = []
            for i in range(stats['total']):
                is_correct = i < stats['correct']  # 前correct个问题正确
                # 假设平均难度为5（ground_truth_generator中的默认值）
                difficulty = 5

                # 基于答题正确性和题目难度计算掌握度
                base_mastery = 0.85 if is_correct else 0.25
                difficulty_factor = (10 - difficulty) / 10
                mastery_level = base_mastery + (difficulty_factor * 0.15 if is_correct else -difficulty_factor * 0.15)
                mastery_level = max(0.0, min(1.0, mastery_level))
                mastery_levels.append(mastery_level)

            # 2. 使用加权平均计算最终掌握度（与ground_truth_generator相同）
            avg_mastery = sum(mastery_levels) / len(mastery_levels) if mastery_levels else 0.0
            final_mastery = (avg_mastery * 0.7 + accuracy_rate * 0.3)
            final_mastery = round(final_mastery, 3)

            concept_info = {
                'concept_id': concept_id,
                'concept_name': stats['concept_name'],
                'mastery_level': final_mastery,
                'accuracy_rate': round(accuracy_rate, 3),
                'problem_count': stats['total']
            }

            # 3. 分类：掌握度 >= 0.6 为掌握，< 0.6 为未掌握（与ground_truth_generator一致）
            if final_mastery >= 0.6:
                mastered_concepts.append(concept_info)
            else:
                not_mastered_concepts.append(concept_info)

        total_correct = sum(1 for p in problems if p.get('is_correct', False))
        overall_accuracy = total_correct / len(problems) if problems else 0.0

        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'blind_test_agent_1.0.0',
                'total_problems': len(problems),
                'correct_problems': total_correct
            },
            'diagnostic_result': {
                'overall_accuracy': round(overall_accuracy, 3),
                'mastered_concepts': mastered_concepts,
                'not_mastered_concepts': not_mastered_concepts,
                'total_concepts': len(concept_stats),
                'mastered_count': len(mastered_concepts),
                'not_mastered_count': len(not_mastered_concepts),
                'diagnostic_confidence': 0.8
            }
        }

    async def generate_planning_ground_truth(
        self,
        student_id: str,
        student_mastery: Dict[str, float],
        target_concept: str = None
    ) -> Dict[str, Any]:
        """生成路径规划标准答案"""
        try:
            self.logger.info(f"为学生 {student_id} 生成路径规划标准答案...")

            # 使用路径规划器生成最优路径
            planning_result = await self.planner.generate_optimal_learning_paths(
                student_id=student_id,
                max_path_length=10
            )

            if isinstance(planning_result, str):
                planning_data = json.loads(planning_result)
            else:
                planning_data = planning_result

            # 转换为标准格式
            paths_subset = []
            points_subset = []

            # 处理学习路径
            learning_paths = planning_data.get('learning_paths', [])
            for i, path in enumerate(learning_paths):
                if isinstance(path, dict) and 'steps' in path:
                    path_id = path.get('path_id', '')

                    # 跳过独立概念路径
                    if path_id == 'independent_concepts':
                        continue

                    # 提取路径中的概念
                    path_concepts = []
                    for step in path.get('steps', []):
                        if isinstance(step, dict) and 'concept_id' in step:
                            path_concepts.append(step['concept_id'])

                    # 只有当路径包含多个概念时才作为真正的路径
                    if len(path_concepts) > 1:
                        # 构建边列表
                        edges = []
                        for j in range(len(path_concepts) - 1):
                            edges.append([path_concepts[j], path_concepts[j + 1]])

                        paths_subset.append({
                            'path_id': path_id if path_id else f'optimal_path_{i}',
                            'nodes': path_concepts,
                            'edges': edges,
                            'sequence': path_concepts,
                            'concepts': path_concepts,
                            'estimated_time': path.get('estimated_time', 0),
                            'priority': 'medium'
                        })
                    elif len(path_concepts) == 1:
                        # 单个概念作为独立点
                        concept_id = path_concepts[0]
                        if concept_id not in points_subset:
                            points_subset.append(concept_id)

            # 处理独立薄弱概念
            independent_concepts = planning_data.get('independent_weak_concepts', [])
            for concept in independent_concepts:
                if isinstance(concept, dict):
                    concept_id = concept.get('concept_id', '')
                    if concept_id and concept_id not in points_subset:
                        points_subset.append(concept_id)
                else:
                    concept_str = str(concept)
                    if concept_str and concept_str not in points_subset:
                        points_subset.append(concept_str)

            return {
                'evaluation_metadata': {
                    'student_id': student_id,
                    'target_concept': target_concept,
                    'timestamp': datetime.now().isoformat(),
                    'generator_version': '2.0.0',
                    'plan_type': planning_data.get('plan_type', 'multi_source_multi_sink_optimal'),
                    'planning_type': 'multi_target_optimal'
                },
                'planning_result': {
                    'paths_subset': paths_subset,
                    'points_subset': points_subset,
                    'total_concepts_to_learn': len(set([c for path in paths_subset for c in path.get('concepts', [])] + points_subset)),
                    'estimated_time': planning_data.get('estimated_time', 0),
                    'planning_confidence': 0.95
                }
            }

        except Exception as e:
            self.logger.error(f"生成路径规划标准答案失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

            return {
                'evaluation_metadata': {
                    'student_id': student_id,
                    'target_concept': target_concept,
                    'timestamp': datetime.now().isoformat(),
                    'generator_version': '2.0.0',
                    'error': str(e)
                },
                'planning_result': {
                    'paths_subset': [],
                    'points_subset': [],
                    'total_concepts_to_learn': 0,
                    'estimated_time': 0,
                    'planning_confidence': 0.1,
                    'plan_type': 'error'
                }
            }

    async def generate_st1_ground_truth_dataset(
        self,
        st1_file: Path,
        output_dir: Path
    ) -> None:
        """生成基于ST1数据的完整标准答案数据集"""

        self.logger.info("=" * 60)
        self.logger.info("🎯 开始生成ST1标准答案数据集")
        self.logger.info("=" * 60)

        # 加载ST1数据
        st1_data = self.load_st1_data(st1_file)

        if not st1_data:
            self.logger.error("ST1数据加载失败，无法继续")
            return

        # 收集所有题目ID
        all_problem_ids = set()
        for student_data in st1_data:
            for attempt in student_data.get('problem_attempts', []):
                all_problem_ids.add(attempt['problem_id'])

        self.logger.info(f"发现 {len(all_problem_ids)} 个不同的题目ID")

        # 从Neo4j数据库获取题目数据
        problems_data = await self.fetch_problems_from_neo4j(list(all_problem_ids))

        if not problems_data:
            self.logger.error("题目数据获取失败，无法继续")
            return

        # 验证所有知识点
        all_kp_ids = set()
        for problem in problems_data:
            all_kp_ids.update(problem.get('linked_kp_ids', []))

        self.logger.info(f"验证 {len(all_kp_ids)} 个知识点...")
        knowledge_verification = await self.verify_knowledge_points_in_graph(list(all_kp_ids))

        valid_kp_count = sum(1 for v in knowledge_verification.values() if v.get('exists', False))
        self.logger.info(f"有效知识点: {valid_kp_count}/{len(all_kp_ids)}")

        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成标准答案
        diagnostic_ground_truth = []
        planning_ground_truth = []

        self.logger.info(f"开始为 {len(st1_data)} 个学生生成标准答案...")

        for student_data in st1_data:
            student_id = student_data['student_id']
            student_name = student_data.get('name', 'Unknown')

            self.logger.info(f"处理学生 {student_id} ({student_name})...")

            # 转换答题记录格式
            student_problems = self.convert_st1_to_problem_format(student_data, problems_data)

            if not student_problems:
                self.logger.warning(f"学生 {student_id} 没有有效的答题记录")
                continue

            # 生成诊断标准答案
            diagnostic_gt = await self.generate_student_diagnostic_ground_truth(
                student_id, student_problems, knowledge_verification
            )
            diagnostic_ground_truth.append(diagnostic_gt)

            # 提取掌握度信息用于路径规划
            student_mastery = {}
            for concept in diagnostic_gt['diagnostic_result']['mastered_concepts']:
                student_mastery[concept['concept_id']] = concept['mastery_level']
            for concept in diagnostic_gt['diagnostic_result']['not_mastered_concepts']:
                student_mastery[concept['concept_id']] = concept['mastery_level']

            # 生成路径规划标准答案
            planning_gt = await self.generate_planning_ground_truth(
                student_id, student_mastery
            )
            planning_ground_truth.append(planning_gt)

            self.logger.info(f"学生 {student_id} 处理完成")

        # 保存结果
        diagnostic_file = output_dir / "st1_diagnostic_ground_truth.json"
        planning_file = output_dir / "st1_planning_ground_truth.json"

        with open(diagnostic_file, 'w', encoding='utf-8') as f:
            json.dump(diagnostic_ground_truth, f, ensure_ascii=False, indent=2)

        with open(planning_file, 'w', encoding='utf-8') as f:
            json.dump(planning_ground_truth, f, ensure_ascii=False, indent=2)

        self.logger.info("=" * 60)
        self.logger.info("✅ ST1标准答案数据集生成完成")
        self.logger.info(f"📊 诊断标准答案: {len(diagnostic_ground_truth)} 个学生")
        self.logger.info(f"🛤️  规划标准答案: {len(planning_ground_truth)} 个学生")
        self.logger.info(f"💾 诊断结果保存至: {diagnostic_file}")
        self.logger.info(f"💾 规划结果保存至: {planning_file}")
        self.logger.info("=" * 60)

async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='基于ST1数据生成标准答案数据集')
    parser.add_argument('--st1-file',
                       default='data/st1_data.json',
                       help='ST1数据文件路径')
    parser.add_argument('--output-dir',
                       default='evaluation/st1_ground_truth',
                       help='输出目录')

    args = parser.parse_args()

    # 转换为Path对象
    st1_file = Path(args.st1_file)
    output_dir = Path(args.output_dir)

    # 检查输入文件是否存在
    if not st1_file.exists():
        logger.error(f"ST1数据文件不存在: {st1_file}")
        return

    try:
        # 创建Graphiti客户端
        logger.info("初始化Graphiti客户端...")
        client = create_graphiti_client()

        # 创建生成器
        generator = ST1GroundTruthGenerator(client)

        # 生成标准答案数据集
        await generator.generate_st1_ground_truth_dataset(
            st1_file=st1_file,
            output_dir=output_dir
        )

        logger.info("🎉 程序执行完成")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
