#!/usr/bin/env python3
# 测试解析方法

test_llm_output = """```json
{
    "is_correct": true,
    "confidence_score": 0.85,
    "mastery_level": 0.8,
    "cognitive_analysis": {
        "reasoning_process": "学生正确理解了概念",
        "knowledge_gaps": [],
        "cognitive_level": "理解"
    },
    "identified_misconceptions": [],
    "recommendations": ["继续保持"]
}
```"""

# 直接导入并测试
import sys
sys.path.append('.')

# 直接测试解析逻辑，不依赖DiagnosticTool类
import json
import re

def parse_llm_diagnostic_output(llm_output: str):
    """解析LLM诊断输出"""
    try:
        # 尝试提取JSON部分
        json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # 如果没有代码块，尝试直接解析
            json_str = llm_output.strip()

        # 解析JSON
        result = json.loads(json_str)

        # 验证必要字段
        required_fields = ['is_correct', 'confidence_score', 'mastery_level']
        for field in required_fields:
            if field not in result:
                print(f"LLM输出缺少字段: {field}")
                result[field] = 0.5  # 默认值

        print(f"✅ LLM输出解析成功")
        return result

    except Exception as e:
        print(f"❌ LLM输出解析失败: {e}")
        print(f"原始输出: {llm_output}")

        # 返回默认结果
        return {
            'is_correct': False,
            'confidence_score': 0.3,
            'cognitive_analysis': {
                'reasoning_process': '解析失败，无法分析',
                'knowledge_gaps': ['LLM输出解析错误'],
                'cognitive_level': '未知'
            },
            'identified_misconceptions': [],
            'mastery_level': 0.3,
            'recommendations': ['请重新尝试或联系技术支持']
        }

# 测试解析方法
try:
    result = parse_llm_diagnostic_output(test_llm_output)
    print('✅ 解析成功!')
    print('结果:', result)
except Exception as e:
    print('❌ 解析失败:', e)
    import traceback
    traceback.print_exc()
