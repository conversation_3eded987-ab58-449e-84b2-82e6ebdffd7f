### 认知诊断方案
准确率 = (系统正确诊断的知识点掌握状态数量) / (总的知识点诊断数量)

### 学习路径匹配评测方案

#### **第一步：集合分离 (Set Separation)**



将输入的两个原始集合（Set A 和 Set B）各自拆分为“路径子集”和“独立点子集”。

* `Paths_A`, `Points_A`
* `Paths_B`, `Points_B`

---

#### **第二步：分别计算子集相似度 (Subset Similarity Calculation)**

##### **2.1. 独立点相似度 ($Sim_{points}$)**


使用 Jaccard 相似度计算 `Points_A` 和 `Points_B` 的相似度。
$Sim_{points} = J(\text{Points}_A, \text{Points}_B)$

##### **2.2. 路径相似度 ($Sim_{paths}$) - (核心升级部分)**

这是算法升级的核心。它分为三个子步骤：

* **子步骤 2.2.1: 定义单条路径间的相似度函数 $Sim_{path}(p_i, p_j)$**

    首先，我们需要一个能比较两条独立路径 $p_i$ 和 $p_j$ 相似度的函数。一个健壮的函数应该同时考虑节点、边和它们的顺序。

    对于任意两条路径 $p_i$ 和 $p_j$：
    1.  提取各自的节点集 $V_i, V_j$ 和边集 $E_i, E_j$。
    2.  提取各自的节点序列 $S_i, S_j$ (例如，$p_i = a \to b \to c$ 的序列是 `[a, b, c]`)。
    3.  计算各分量相似度：
        * **节点重合度**: $J_{nodes} = J(V_i, V_j)$
        * **边重合度**: $J_{edges} = J(E_i, E_j)$
        * **顺序相似度**: $Sim_{seq}$，可以使用**归一化的莱文斯坦距离（Levenshtein Distance）**来计算。莱文斯坦距离衡量的是将一个序列变成另一个序列所需的最少编辑操作（增、删、改）次数。归一化后可以得到一个0-1之间的相似度分数。
            $Sim_{seq}(S_i, S_j) = 1 - \frac{\text{Levenshtein}(S_i, S_j)}{\max(|S_i|, |S_j|)}$

    4.  **加权组合得到单条路径的相似度**：
        $Sim_{path}(p_i, p_j) = w_{node} \cdot J_{nodes} + w_{edge} \cdot J_{edges} + w_{seq} \cdot Sim_{seq}$
        （权重 $w_{node} + w_{edge} + w_{seq} = 1$，可以根据需求调整）

* **子步骤 2.2.2: 计算跨集合的相似度矩阵 (Cross-Similarity Matrix)**

    现在，我们使用上面定义的 $Sim_{path}$ 函数，计算 `Paths_A` 中的每一条路径与 `Paths_B` 中每一条路径的相似度，形成一个矩阵 `M`。

    * 矩阵 `M` 的大小为 `|Paths_A| × |Paths_B|`。
    * 矩阵中的元素 $M_{ij} = Sim_{path}(p_{A_i}, p_{B_j})$。

* **子步骤 2.2.3: 整合矩阵得到最终路径相似度**

    为了得到一个总的 $Sim_{paths}$ 分数，我们采用**双向平均最佳匹配（Symmetric Average Best Match）**的方法，这能很好地处理两个路径集合大小不等的情况。

    1.  **从A到B的相似度**：对 `Paths_A` 中的每一条路径，从矩阵`M`的对应行中找到其在 `Paths_B` 中的最佳匹配（即最大相似度），然后计算这些最佳匹配的平均值。
        $\text{AvgSim}(A \to B) = \frac{1}{|Paths_A|} \sum_{i=1}^{|Paths_A|} \max_{j=1}^{|Paths_B|} (M_{ij})$

    2.  **从B到A的相似度**：同理，对 `Paths_B` 中的每一条路径，找到其在 `Paths_A` 中的最佳匹配（即每列的最大值），然后取平均。
        $\text{AvgSim}(B \to A) = \frac{1}{|Paths_B|} \sum_{j=1}^{|Paths_B|} \max_{i=1}^{|Paths_A|} (M_{ij})$

    3.  **最终的路径相似度**是这两者的平均值：
        $Sim_{paths} = \frac{\text{AvgSim}(A \to B) + \text{AvgSim}(B \to A)}{2}$

---

#### **第三步：合并总相似度 (Total Similarity Combination)**

$TotalSim = w_{paths} \cdot Sim_{paths} + w_{points} \cdot Sim_{points}$

