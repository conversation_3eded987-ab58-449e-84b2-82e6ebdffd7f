"""
辅助函数模块
提供系统中常用的工具函数
"""
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

def format_learning_path(path_data: Dict[str, Any]) -> str:
    """格式化学习路径为可读文本"""
    
    if not path_data:
        return "暂无学习路径数据"
    
    result = []
    
    # 基本信息
    result.append(f"🎯 目标概念: {path_data.get('target_concept', '未知')}")
    result.append(f"👤 学生: {path_data.get('student_id', '未知')}")
    
    current_mastery = path_data.get('current_mastery_level', 0)
    result.append(f"📊 当前掌握度: {current_mastery:.1%}")
    
    # 优先任务
    priority_tasks = path_data.get('priority_tasks', [])
    if priority_tasks:
        result.append("\n🚨 优先纠错任务:")
        for i, task in enumerate(priority_tasks, 1):
            result.append(f"  {i}. {task.get('description', '未知任务')}")
            result.append(f"     ⏱️ 预计时间: {task.get('estimated_time', 0)} 分钟")
    
    # 学习路径
    learning_path = path_data.get('learning_path', [])
    if learning_path:
        result.append("\n📚 学习路径:")
        for i, concept in enumerate(learning_path, 1):
            name = concept.get('concept_name', concept.get('concept_id', '未知概念'))
            mastery = concept.get('current_mastery', 0)
            priority = concept.get('priority', 'medium')
            
            status = "✅" if concept.get('is_mastered', False) else "📖"
            priority_icon = {"urgent": "🔥", "high": "⚡", "medium": "📝", "low": "💡"}.get(priority, "📝")
            
            result.append(f"  {i}. {status} {name} (掌握度: {mastery:.1%}) {priority_icon}")
    
    # 时间估算
    estimated_time = path_data.get('estimated_time', 0)
    if estimated_time:
        result.append(f"\n⏰ 预计总学习时间: {estimated_time} 分钟")
    
    # 建议
    recommendations = path_data.get('recommendations', [])
    if recommendations:
        result.append("\n💡 学习建议:")
        for rec in recommendations:
            result.append(f"  • {rec}")
    
    return "\n".join(result)

def format_diagnostic_result(diagnostic_data: Dict[str, Any]) -> str:
    """格式化诊断结果为可读文本"""
    
    if not diagnostic_data:
        return "暂无诊断数据"
    
    result = []
    
    # 答题结果
    is_correct = diagnostic_data.get('is_correct', False)
    result.append(f"📝 答题结果: {'✅ 正确' if is_correct else '❌ 错误'}")
    
    # 易错点分析
    misconceptions = diagnostic_data.get('identified_misconceptions', [])
    if misconceptions:
        result.append("\n🎯 发现的易错点:")
        for i, misconception in enumerate(misconceptions, 1):
            option = misconception.get('option', '未知')
            description = misconception.get('description', '无描述')
            result.append(f"  {i}. 选项 {option}: {description}")
    
    # 掌握度分析
    mastery_analysis = diagnostic_data.get('mastery_analysis', {})
    if mastery_analysis:
        result.append("\n📊 知识点掌握度分析:")
        for concept_id, mastery_level in mastery_analysis.items():
            level_text = "优秀" if mastery_level >= 0.8 else "良好" if mastery_level >= 0.6 else "需要提高"
            result.append(f"  • {concept_id}: {mastery_level:.1%} ({level_text})")
    
    # 建议
    recommendations = diagnostic_data.get('recommendations', [])
    if recommendations:
        result.append("\n💡 学习建议:")
        for rec in recommendations:
            result.append(f"  • {rec}")
    
    return "\n".join(result)

def format_student_progress(progress_data: Dict[str, Any]) -> str:
    """格式化学生进度为可读文本"""
    
    if not progress_data:
        return "暂无进度数据"
    
    result = []
    
    student_id = progress_data.get('student_id', '未知')
    result.append(f"👤 学生: {student_id}")
    
    # 整体进度
    overall_progress = progress_data.get('overall_progress', 0)
    result.append(f"📈 整体进度: {overall_progress:.1%}")
    
    # 答题统计
    attempt_stats = progress_data.get('attempt_statistics', {})
    if attempt_stats:
        total = attempt_stats.get('total_attempts', 0)
        correct = attempt_stats.get('correct_attempts', 0)
        accuracy = attempt_stats.get('accuracy_rate', 0)
        
        result.append(f"\n📊 答题统计:")
        result.append(f"  • 总答题数: {total}")
        result.append(f"  • 正确答题数: {correct}")
        result.append(f"  • 正确率: {accuracy:.1%}")
    
    # 掌握度排行
    mastery_data = progress_data.get('mastery_data', [])
    if mastery_data:
        result.append(f"\n🏆 知识点掌握情况 (前5名):")
        for i, item in enumerate(mastery_data[:5], 1):
            name = item.get('concept_name', item.get('concept_id', '未知'))
            proficiency = item.get('proficiency', 0)
            result.append(f"  {i}. {name}: {proficiency:.1%}")
    
    # 易错点
    misconceptions = progress_data.get('misconceptions', [])
    if misconceptions:
        result.append(f"\n⚠️ 主要易错点:")
        for i, item in enumerate(misconceptions[:3], 1):
            description = item.get('description', '无描述')
            strength = item.get('strength', 0)
            result.append(f"  {i}. {description} (出现次数: {strength})")
    
    return "\n".join(result)

def parse_json_safely(json_str: str) -> Optional[Dict[str, Any]]:
    """安全解析JSON字符串"""
    
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败: {e}")
        return None
    except Exception as e:
        logger.error(f"解析过程中发生未知错误: {e}")
        return None

def validate_student_info(student_data: Dict[str, Any]) -> bool:
    """验证学生信息的完整性"""
    
    required_fields = ['student_id', 'name']
    
    for field in required_fields:
        if field not in student_data or not student_data[field]:
            logger.error(f"学生信息缺少必要字段: {field}")
            return False
    
    return True

def validate_problem_data(problem_data: Dict[str, Any]) -> bool:
    """验证题目数据的完整性"""
    
    required_fields = ['problem_id', 'selected_options', 'correct_answer']
    
    for field in required_fields:
        if field not in problem_data:
            logger.error(f"题目数据缺少必要字段: {field}")
            return False
    
    # 验证选项格式
    selected = problem_data['selected_options']
    correct = problem_data['correct_answer']
    
    if not isinstance(selected, list) or not isinstance(correct, list):
        logger.error("选项数据必须是列表格式")
        return False
    
    return True

def calculate_difficulty_level(difficulty_score: float) -> str:
    """根据难度分数计算难度等级"""
    
    if difficulty_score <= 3.0:
        return "简单"
    elif difficulty_score <= 6.0:
        return "中等"
    elif difficulty_score <= 8.0:
        return "较难"
    else:
        return "困难"

def get_mastery_level_description(mastery_score: float) -> str:
    """根据掌握度分数获取描述"""
    
    if mastery_score >= 0.9:
        return "精通"
    elif mastery_score >= 0.8:
        return "优秀"
    elif mastery_score >= 0.6:
        return "良好"
    elif mastery_score >= 0.4:
        return "一般"
    else:
        return "需要提高"

def format_timestamp(timestamp: Optional[datetime] = None) -> str:
    """格式化时间戳"""
    
    if timestamp is None:
        timestamp = datetime.now(timezone.utc)
    
    return timestamp.strftime("%Y-%m-%d %H:%M:%S")

def truncate_text(text: str, max_length: int = 100) -> str:
    """截断文本到指定长度"""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."

def extract_concept_from_path(concept_path: str) -> str:
    """从概念路径中提取概念名称"""
    
    if "/" in concept_path:
        return concept_path.split("/")[-1]
    
    return concept_path
