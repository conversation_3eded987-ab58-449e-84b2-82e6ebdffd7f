"""
学习路径相似度评测器
实现学习路径匹配评测方案
"""

import json
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import numpy as np

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from utils.similarity_calculator import SimilarityCalculator
except ImportError:
    from evaluation.utils.similarity_calculator import SimilarityCalculator

logger = logging.getLogger(__name__)

class PathSimilarityEvaluator:
    """学习路径相似度评测器"""
    
    def __init__(
        self, 
        node_weight: float = 0.4, 
        edge_weight: float = 0.3, 
        sequence_weight: float = 0.3,
        paths_weight: float = 0.7,
        points_weight: float = 0.3
    ):
        """
        初始化评测器
        
        Args:
            node_weight: 节点相似度权重
            edge_weight: 边相似度权重
            sequence_weight: 序列相似度权重
            paths_weight: 路径子集相似度权重
            points_weight: 独立点子集相似度权重
        """
        self.similarity_calculator = SimilarityCalculator(node_weight, edge_weight, sequence_weight)
        self.paths_weight = paths_weight
        self.points_weight = points_weight
        self.logger = logger
        
        # 确保权重和为1
        total_weight = paths_weight + points_weight
        if abs(total_weight - 1.0) > 1e-6:
            self.logger.warning(f"路径和独立点权重和不为1 ({total_weight})，将进行归一化")
            self.paths_weight /= total_weight
            self.points_weight /= total_weight
    
    def evaluate_path_similarity(
        self, 
        predicted_results: List[Dict[str, Any]], 
        ground_truth: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        评测学习路径相似度
        
        Args:
            predicted_results: 智能体预测的规划结果列表
            ground_truth: 真实标准路径列表
            
        Returns:
            评测结果字典
        """
        try:
            if len(predicted_results) != len(ground_truth):
                raise ValueError(f"预测结果数量({len(predicted_results)})与真实标签数量({len(ground_truth)})不匹配")
            
            detailed_results = []
            similarity_scores = []
            
            # 逐个比较路径规划结果
            for i, (predicted, truth) in enumerate(zip(predicted_results, ground_truth)):
                result = self._compare_single_planning(predicted, truth, i)
                detailed_results.append(result)
                similarity_scores.append(result['total_similarity'])
            
            # 计算总体相似度
            overall_similarity = np.mean(similarity_scores) if similarity_scores else 0.0
            
            # 生成评测报告
            evaluation_report = {
                'evaluation_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'evaluator_version': '1.0.0',
                    'total_cases': len(predicted_results),
                    'weights': {
                        'paths_weight': self.paths_weight,
                        'points_weight': self.points_weight,
                        'node_weight': self.similarity_calculator.node_weight,
                        'edge_weight': self.similarity_calculator.edge_weight,
                        'sequence_weight': self.similarity_calculator.sequence_weight
                    }
                },
                'similarity_metrics': {
                    'overall_similarity': overall_similarity,
                    'similarity_scores': similarity_scores,
                    'paths_similarity_avg': np.mean([r['paths_similarity'] for r in detailed_results]),
                    'points_similarity_avg': np.mean([r['points_similarity'] for r in detailed_results])
                },
                'detailed_results': detailed_results,
                'summary_statistics': self._calculate_summary_statistics(detailed_results)
            }
            
            self.logger.info(f"路径相似度评测完成: 总体相似度 {overall_similarity:.3f}")
            return evaluation_report
            
        except Exception as e:
            self.logger.error(f"路径相似度评测失败: {e}")
            return self._create_error_evaluation(str(e))
    
    def _compare_single_planning(
        self, 
        predicted: Dict[str, Any], 
        truth: Dict[str, Any], 
        case_index: int
    ) -> Dict[str, Any]:
        """
        比较单个规划结果
        
        Args:
            predicted: 预测的规划结果
            truth: 真实标准路径
            case_index: 案例索引
            
        Returns:
            单个案例的比较结果
        """
        try:
            # 提取预测的路径子集和独立点子集
            predicted_planning = predicted.get('planning_result', {})
            predicted_paths = predicted_planning.get('paths_subset', [])
            predicted_points = predicted_planning.get('points_subset', [])
            
            # 提取真实的路径子集和独立点子集
            truth_planning = truth.get('planning_result', {})
            truth_paths = truth_planning.get('paths_subset', [])
            truth_points = truth_planning.get('points_subset', [])
            
            # 计算相似度
            similarity_result = self.similarity_calculator.total_similarity(
                predicted_paths, predicted_points,
                truth_paths, truth_points,
                self.paths_weight, self.points_weight
            )
            
            # 计算详细的路径匹配信息
            path_matching_details = self._analyze_path_matching(predicted_paths, truth_paths)
            
            return {
                'case_index': case_index,
                'student_id': predicted.get('evaluation_metadata', {}).get('student_id', 'unknown'),
                'total_similarity': similarity_result['total_similarity'],
                'paths_similarity': similarity_result['paths_similarity'],
                'points_similarity': similarity_result['points_similarity'],
                'predicted_stats': {
                    'num_paths': len(predicted_paths),
                    'num_points': len(predicted_points),
                    'total_concepts': len(predicted_points) + sum(len(p.get('concepts', [])) for p in predicted_paths)
                },
                'truth_stats': {
                    'num_paths': len(truth_paths),
                    'num_points': len(truth_points),
                    'total_concepts': len(truth_points) + sum(len(p.get('concepts', [])) for p in truth_paths)
                },
                'path_matching_details': path_matching_details,
                'weights_used': similarity_result['weights']
            }
            
        except Exception as e:
            self.logger.error(f"单个规划比较失败 (案例 {case_index}): {e}")
            return {
                'case_index': case_index,
                'total_similarity': 0.0,
                'paths_similarity': 0.0,
                'points_similarity': 0.0,
                'error': str(e)
            }
    
    def _analyze_path_matching(
        self, 
        predicted_paths: List[Dict[str, Any]], 
        truth_paths: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        分析路径匹配详情
        
        Args:
            predicted_paths: 预测的路径列表
            truth_paths: 真实的路径列表
            
        Returns:
            路径匹配分析结果
        """
        try:
            if not predicted_paths or not truth_paths:
                return {
                    'similarity_matrix': [],
                    'best_matches': [],
                    'avg_best_match_similarity': 0.0
                }
            
            # 计算相似度矩阵
            similarity_matrix = self.similarity_calculator.cross_similarity_matrix(
                predicted_paths, truth_paths
            )
            
            # 找到最佳匹配
            best_matches = []
            for i, predicted_path in enumerate(predicted_paths):
                if i < similarity_matrix.shape[0]:
                    best_match_idx = np.argmax(similarity_matrix[i])
                    best_similarity = similarity_matrix[i][best_match_idx]
                    
                    best_matches.append({
                        'predicted_path_index': i,
                        'predicted_path_id': predicted_path.get('path_id', f'path_{i}'),
                        'best_match_index': int(best_match_idx),
                        'best_match_id': truth_paths[best_match_idx].get('path_id', f'truth_path_{best_match_idx}'),
                        'similarity': float(best_similarity),
                        'predicted_concepts': predicted_path.get('concepts', []),
                        'matched_concepts': truth_paths[best_match_idx].get('concepts', [])
                    })
            
            avg_best_match_similarity = np.mean([m['similarity'] for m in best_matches]) if best_matches else 0.0
            
            return {
                'similarity_matrix': similarity_matrix.tolist(),
                'best_matches': best_matches,
                'avg_best_match_similarity': avg_best_match_similarity,
                'matrix_shape': similarity_matrix.shape
            }
            
        except Exception as e:
            self.logger.error(f"路径匹配分析失败: {e}")
            return {
                'similarity_matrix': [],
                'best_matches': [],
                'avg_best_match_similarity': 0.0,
                'error': str(e)
            }
    
    def _calculate_summary_statistics(self, detailed_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算汇总统计信息
        
        Args:
            detailed_results: 详细结果列表
            
        Returns:
            汇总统计信息
        """
        try:
            total_similarities = [r.get('total_similarity', 0.0) for r in detailed_results]
            paths_similarities = [r.get('paths_similarity', 0.0) for r in detailed_results]
            points_similarities = [r.get('points_similarity', 0.0) for r in detailed_results]
            
            # 路径数量统计
            predicted_path_counts = [r.get('predicted_stats', {}).get('num_paths', 0) for r in detailed_results]
            truth_path_counts = [r.get('truth_stats', {}).get('num_paths', 0) for r in detailed_results]
            
            # 独立点数量统计
            predicted_point_counts = [r.get('predicted_stats', {}).get('num_points', 0) for r in detailed_results]
            truth_point_counts = [r.get('truth_stats', {}).get('num_points', 0) for r in detailed_results]
            
            return {
                'similarity_statistics': {
                    'total_similarity': {
                        'mean': np.mean(total_similarities),
                        'std': np.std(total_similarities),
                        'min': np.min(total_similarities),
                        'max': np.max(total_similarities),
                        'median': np.median(total_similarities)
                    },
                    'paths_similarity': {
                        'mean': np.mean(paths_similarities),
                        'std': np.std(paths_similarities),
                        'min': np.min(paths_similarities),
                        'max': np.max(paths_similarities)
                    },
                    'points_similarity': {
                        'mean': np.mean(points_similarities),
                        'std': np.std(points_similarities),
                        'min': np.min(points_similarities),
                        'max': np.max(points_similarities)
                    }
                },
                'structure_statistics': {
                    'predicted_paths': {
                        'mean': np.mean(predicted_path_counts),
                        'std': np.std(predicted_path_counts),
                        'total': np.sum(predicted_path_counts)
                    },
                    'truth_paths': {
                        'mean': np.mean(truth_path_counts),
                        'std': np.std(truth_path_counts),
                        'total': np.sum(truth_path_counts)
                    },
                    'predicted_points': {
                        'mean': np.mean(predicted_point_counts),
                        'std': np.std(predicted_point_counts),
                        'total': np.sum(predicted_point_counts)
                    },
                    'truth_points': {
                        'mean': np.mean(truth_point_counts),
                        'std': np.std(truth_point_counts),
                        'total': np.sum(truth_point_counts)
                    }
                },
                'performance_distribution': {
                    'excellent': sum(1 for sim in total_similarities if sim >= 0.9),
                    'good': sum(1 for sim in total_similarities if 0.7 <= sim < 0.9),
                    'fair': sum(1 for sim in total_similarities if 0.5 <= sim < 0.7),
                    'poor': sum(1 for sim in total_similarities if sim < 0.5)
                }
            }
            
        except Exception as e:
            self.logger.error(f"汇总统计计算失败: {e}")
            return {'error': str(e)}
    
    def _create_error_evaluation(self, error_message: str) -> Dict[str, Any]:
        """创建错误评测结果"""
        return {
            'evaluation_metadata': {
                'timestamp': datetime.now().isoformat(),
                'evaluator_version': '1.0.0',
                'error': error_message
            },
            'similarity_metrics': {
                'overall_similarity': 0.0,
                'similarity_scores': [],
                'paths_similarity_avg': 0.0,
                'points_similarity_avg': 0.0
            },
            'detailed_results': [],
            'summary_statistics': {}
        }
    
    def save_evaluation_results(self, results: Dict[str, Any], filepath: str) -> bool:
        """保存评测结果到文件"""
        try:
            # 转换numpy类型为Python原生类型
            def convert_numpy_types(obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, dict):
                    return {key: convert_numpy_types(value) for key, value in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(item) for item in obj]
                else:
                    return obj

            converted_results = convert_numpy_types(results)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(converted_results, f, ensure_ascii=False, indent=2)

            self.logger.info(f"评测结果已保存到: {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"保存评测结果失败: {e}")
            return False
    
    def generate_evaluation_report(self, results: Dict[str, Any]) -> str:
        """生成可读的评测报告"""
        try:
            report_lines = []
            report_lines.append("=" * 60)
            report_lines.append("学习路径相似度评测报告")
            report_lines.append("=" * 60)
            
            # 基本信息
            metadata = results.get('evaluation_metadata', {})
            report_lines.append(f"评测时间: {metadata.get('timestamp', 'N/A')}")
            report_lines.append(f"评测案例数: {metadata.get('total_cases', 0)}")
            
            # 权重信息
            weights = metadata.get('weights', {})
            report_lines.append(f"权重设置:")
            report_lines.append(f"  路径权重: {weights.get('paths_weight', 0.7):.2f}")
            report_lines.append(f"  独立点权重: {weights.get('points_weight', 0.3):.2f}")
            report_lines.append(f"  节点权重: {weights.get('node_weight', 0.4):.2f}")
            report_lines.append(f"  边权重: {weights.get('edge_weight', 0.3):.2f}")
            report_lines.append(f"  序列权重: {weights.get('sequence_weight', 0.3):.2f}")
            report_lines.append("")
            
            # 相似度指标
            similarity_metrics = results.get('similarity_metrics', {})
            report_lines.append("相似度指标:")
            report_lines.append(f"  总体相似度: {similarity_metrics.get('overall_similarity', 0.0):.3f}")
            report_lines.append(f"  平均路径相似度: {similarity_metrics.get('paths_similarity_avg', 0.0):.3f}")
            report_lines.append(f"  平均独立点相似度: {similarity_metrics.get('points_similarity_avg', 0.0):.3f}")
            report_lines.append("")
            
            # 汇总统计
            summary_stats = results.get('summary_statistics', {})
            if 'similarity_statistics' in summary_stats:
                sim_stats = summary_stats['similarity_statistics']['total_similarity']
                report_lines.append("相似度统计:")
                report_lines.append(f"  平均值: {sim_stats.get('mean', 0.0):.3f}")
                report_lines.append(f"  标准差: {sim_stats.get('std', 0.0):.3f}")
                report_lines.append(f"  最小值: {sim_stats.get('min', 0.0):.3f}")
                report_lines.append(f"  最大值: {sim_stats.get('max', 0.0):.3f}")
                report_lines.append("")
            
            # 性能分布
            if 'performance_distribution' in summary_stats:
                perf_dist = summary_stats['performance_distribution']
                report_lines.append("性能分布:")
                report_lines.append(f"  优秀 (≥0.9): {perf_dist.get('excellent', 0)} 个案例")
                report_lines.append(f"  良好 (0.7-0.9): {perf_dist.get('good', 0)} 个案例")
                report_lines.append(f"  一般 (0.5-0.7): {perf_dist.get('fair', 0)} 个案例")
                report_lines.append(f"  较差 (<0.5): {perf_dist.get('poor', 0)} 个案例")
            
            report_lines.append("=" * 60)
            
            return "\n".join(report_lines)
            
        except Exception as e:
            self.logger.error(f"生成评测报告失败: {e}")
            return f"报告生成失败: {str(e)}"
