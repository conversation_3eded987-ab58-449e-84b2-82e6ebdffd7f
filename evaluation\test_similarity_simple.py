#!/usr/bin/env python3
"""
简单的相似度计算测试
直接测试 PathSimilarityEvaluator 和 SimilarityCalculator
"""

import json
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_json_file(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载文件 {filepath} 失败: {e}")
        return None

def test_similarity_calculator():
    """测试相似度计算器 - 使用 st20plan.json 和 st20true.json"""
    try:
        from evaluators.path_similarity_evaluator import PathSimilarityEvaluator

        logger.info("创建 PathSimilarityEvaluator...")
        evaluator = PathSimilarityEvaluator(
            node_weight=0.4,
            edge_weight=0.3,
            sequence_weight=0.3,
            paths_weight=0.7,
            points_weight=0.3
        )

        # 加载真实测试数据
        logger.info("加载测试数据文件...")
        predicted_data = load_json_file("evaluation/st20plan.json")
        ground_truth_data = load_json_file("evaluation/st20true.json")

        if not predicted_data or not ground_truth_data:
            logger.error("无法加载测试数据文件")
            return False

        logger.info("测试数据加载成功")
        logger.info(f"预测结果路径数: {len(predicted_data.get('planning_result', {}).get('paths_subset', []))}")
        logger.info(f"预测结果独立点数: {len(predicted_data.get('planning_result', {}).get('points_subset', []))}")
        logger.info(f"真实标准路径数: {len(ground_truth_data.get('planning_result', {}).get('paths_subset', []))}")
        logger.info(f"真实标准独立点数: {len(ground_truth_data.get('planning_result', {}).get('points_subset', []))}")

        # 测试单个规划比较
        logger.info("开始相似度计算...")
        result = evaluator._compare_single_planning(predicted_data, ground_truth_data, 0)

        logger.info("=== 相似度计算结果 ===")
        logger.info(f"总相似度: {result.get('total_similarity', 0.0):.3f}")
        logger.info(f"路径相似度: {result.get('paths_similarity', 0.0):.3f}")
        logger.info(f"独立点相似度: {result.get('points_similarity', 0.0):.3f}")

        # 显示详细的路径匹配信息
        path_matching = result.get('path_matching_details', {})
        if path_matching:
            logger.info("=== 路径匹配详情 ===")
            best_matches = path_matching.get('best_matches', [])
            for match in best_matches:
                logger.info(f"预测路径 {match.get('predicted_path_id', 'N/A')} -> "
                          f"最佳匹配 {match.get('best_match_id', 'N/A')} "
                          f"(相似度: {match.get('similarity', 0.0):.3f})")

        # 显示统计信息
        predicted_stats = result.get('predicted_stats', {})
        truth_stats = result.get('truth_stats', {})
        logger.info("=== 统计信息 ===")
        logger.info(f"预测结果: {predicted_stats.get('num_paths', 0)} 路径, "
                   f"{predicted_stats.get('num_points', 0)} 独立点, "
                   f"总概念 {predicted_stats.get('total_concepts', 0)} 个")
        logger.info(f"真实标准: {truth_stats.get('num_paths', 0)} 路径, "
                   f"{truth_stats.get('num_points', 0)} 独立点, "
                   f"总概念 {truth_stats.get('total_concepts', 0)} 个")

        return True

    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_similarity_calculator_direct():
    """直接测试 SimilarityCalculator"""
    try:
        from utils.similarity_calculator import SimilarityCalculator
        
        logger.info("创建 SimilarityCalculator...")
        calculator = SimilarityCalculator(
            node_weight=0.4,
            edge_weight=0.3,
            sequence_weight=0.3
        )
        
        # 测试 Jaccard 相似度
        set_a = {'A', 'B', 'C'}
        set_b = {'B', 'C', 'D'}
        
        jaccard_sim = calculator.jaccard_similarity(set_a, set_b)
        logger.info(f"Jaccard 相似度 ({set_a} vs {set_b}): {jaccard_sim:.3f}")
        
        # 测试序列相似度
        seq_a = ['A', 'B', 'C']
        seq_b = ['A', 'C', 'D']
        
        seq_sim = calculator.sequence_similarity(seq_a, seq_b)
        logger.info(f"序列相似度 ({seq_a} vs {seq_b}): {seq_sim:.3f}")
        
        # 测试莱文斯坦距离
        lev_dist = calculator.levenshtein_distance(seq_a, seq_b)
        logger.info(f"莱文斯坦距离 ({seq_a} vs {seq_b}): {lev_dist}")
        
        return True
        
    except Exception as e:
        logger.error(f"直接测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("开始相似度计算测试...")
    
    # 测试 SimilarityCalculator
    logger.info("=== 测试 SimilarityCalculator ===")
    success1 = test_similarity_calculator_direct()
    
    # 测试 PathSimilarityEvaluator
    logger.info("\n=== 测试 PathSimilarityEvaluator ===")
    success2 = test_similarity_calculator()
    
    if success1 and success2:
        print("\n✅ 所有相似度计算测试通过")
    else:
        print("\n❌ 相似度计算测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
