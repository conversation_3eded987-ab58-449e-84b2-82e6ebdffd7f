"""
学习路径标准化器
将智能体规划输出转换为标准格式，分离路径子集和独立点子集
"""

import json
import logging
from typing import Dict, List, Any, Tuple, Set
from datetime import datetime

logger = logging.getLogger(__name__)

class PlanningStandardizer:
    """学习路径标准化器"""
    
    def __init__(self):
        """初始化标准化器"""
        self.logger = logger
    
    def standardize_planning_output(self, raw_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        将原始规划输出标准化为评测格式
        
        Args:
            raw_output: 智能体原始规划输出
            
        Returns:
            标准化的规划结果JSON，包含路径子集和独立点子集
        """
        try:
            # 解析原始输出（如果是字符串格式）
            if isinstance(raw_output, str):
                raw_output = json.loads(raw_output)
            
            # 提取基本信息
            student_id = raw_output.get('student_id', 'unknown')
            plan_type = raw_output.get('plan_type', 'unknown')
            
            # 提取学习路径
            learning_paths = raw_output.get('learning_paths', [])
            independent_weak_concepts = raw_output.get('independent_weak_concepts', [])
            
            # 分离路径子集和独立点子集
            paths_subset, points_subset = self._separate_paths_and_points(
                learning_paths, independent_weak_concepts
            )
            
            # 构建标准化输出
            standardized_output = {
                'evaluation_metadata': {
                    'student_id': student_id,
                    'plan_type': plan_type,
                    'timestamp': datetime.now().isoformat(),
                    'evaluator_version': '1.0.0'
                },
                'planning_result': {
                    'paths_subset': paths_subset,
                    'points_subset': points_subset,
                    'total_concepts_to_learn': raw_output.get('total_concepts_to_learn', 0),
                    'estimated_time': raw_output.get('estimated_time', 0),
                    'planning_confidence': self._calculate_planning_confidence(raw_output)
                },
                'raw_output': raw_output
            }
            
            self.logger.info(f"成功标准化规划结果 - 学生: {student_id}, 路径数: {len(paths_subset)}, 独立点数: {len(points_subset)}")
            return standardized_output
            
        except Exception as e:
            self.logger.error(f"规划结果标准化失败: {e}")
            return self._create_error_output(raw_output, str(e))
    
    def _separate_paths_and_points(
        self, 
        learning_paths: List[Dict[str, Any]], 
        independent_concepts: List[str]
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        分离路径子集和独立点子集
        
        Args:
            learning_paths: 学习路径列表
            independent_concepts: 独立概念列表
            
        Returns:
            (路径子集, 独立点子集)
        """
        paths_subset = []
        points_subset = set()
        
        # 处理学习路径
        for path in learning_paths:
            if isinstance(path, dict):
                # 提取路径中的概念序列
                concepts = path.get('concepts', [])
                if not concepts:
                    # 尝试其他可能的字段名
                    concepts = path.get('path_concepts', [])
                    if not concepts:
                        concepts = path.get('concept_sequence', [])
                
                if len(concepts) > 1:  # 只有多个概念才构成路径
                    # 标准化路径格式
                    standardized_path = {
                        'path_id': path.get('path_id', f"path_{len(paths_subset)}"),
                        'concepts': concepts,
                        'nodes': self._extract_nodes_from_path(concepts),
                        'edges': self._extract_edges_from_path(concepts),
                        'sequence': concepts,  # 保持原始顺序
                        'estimated_time': path.get('estimated_time', 0),
                        'priority': path.get('priority', 'medium')
                    }
                    paths_subset.append(standardized_path)
                else:
                    # 单个概念作为独立点
                    if concepts:
                        points_subset.add(concepts[0])
        
        # 处理独立概念
        for concept in independent_concepts:
            if isinstance(concept, str):
                points_subset.add(concept)
            elif isinstance(concept, dict):
                concept_id = concept.get('concept_id') or concept.get('name') or concept.get('kpId')
                if concept_id:
                    points_subset.add(concept_id)
        
        return paths_subset, list(points_subset)
    
    def _extract_nodes_from_path(self, concepts: List[str]) -> List[str]:
        """从路径中提取节点集合"""
        return list(set(concepts))  # 去重
    
    def _extract_edges_from_path(self, concepts: List[str]) -> List[Tuple[str, str]]:
        """从路径中提取边集合"""
        edges = []
        for i in range(len(concepts) - 1):
            edges.append((concepts[i], concepts[i + 1]))
        return edges
    
    def _calculate_planning_confidence(self, raw_output: Dict[str, Any]) -> float:
        """计算规划置信度"""
        try:
            factors = []
            
            # 因素1: 路径完整性
            learning_paths = raw_output.get('learning_paths', [])
            if learning_paths:
                avg_path_length = sum(len(path.get('concepts', [])) for path in learning_paths) / len(learning_paths)
                factors.append(min(avg_path_length / 5.0, 1.0))  # 假设5个概念为理想路径长度
            
            # 因素2: 时间估算合理性
            estimated_time = raw_output.get('estimated_time', 0)
            if estimated_time > 0:
                factors.append(0.8)
            
            # 因素3: 优先任务识别
            priority_tasks = raw_output.get('priority_tasks', [])
            if priority_tasks:
                factors.append(0.9)
            
            return sum(factors) / len(factors) if factors else 0.5
            
        except Exception:
            return 0.5
    
    def _create_error_output(self, raw_output: Any, error_message: str) -> Dict[str, Any]:
        """创建错误输出格式"""
        return {
            'evaluation_metadata': {
                'student_id': 'unknown',
                'plan_type': 'error',
                'timestamp': datetime.now().isoformat(),
                'evaluator_version': '1.0.0',
                'error': error_message
            },
            'planning_result': {
                'paths_subset': [],
                'points_subset': [],
                'total_concepts_to_learn': 0,
                'estimated_time': 0,
                'planning_confidence': 0.0
            },
            'raw_output': raw_output
        }
    
    def batch_standardize(self, raw_outputs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量标准化规划输出"""
        standardized_outputs = []
        
        for i, raw_output in enumerate(raw_outputs):
            try:
                standardized = self.standardize_planning_output(raw_output)
                standardized_outputs.append(standardized)
            except Exception as e:
                self.logger.error(f"批量标准化第{i+1}项失败: {e}")
                standardized_outputs.append(self._create_error_output(raw_output, str(e)))
        
        return standardized_outputs
    
    def save_standardized_results(self, results: List[Dict[str, Any]], filepath: str) -> bool:
        """保存标准化结果到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"标准化结果已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存标准化结果失败: {e}")
            return False
    
    def extract_path_statistics(self, standardized_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提取路径统计信息"""
        stats = {
            'total_plans': len(standardized_results),
            'total_paths': 0,
            'total_independent_points': 0,
            'avg_path_length': 0,
            'path_length_distribution': {},
            'concept_frequency': {}
        }
        
        all_path_lengths = []
        
        for result in standardized_results:
            planning_result = result.get('planning_result', {})
            paths = planning_result.get('paths_subset', [])
            points = planning_result.get('points_subset', [])
            
            stats['total_paths'] += len(paths)
            stats['total_independent_points'] += len(points)
            
            # 统计路径长度
            for path in paths:
                path_length = len(path.get('concepts', []))
                all_path_lengths.append(path_length)
                
                # 路径长度分布
                if path_length not in stats['path_length_distribution']:
                    stats['path_length_distribution'][path_length] = 0
                stats['path_length_distribution'][path_length] += 1
                
                # 概念频率统计
                for concept in path.get('concepts', []):
                    if concept not in stats['concept_frequency']:
                        stats['concept_frequency'][concept] = 0
                    stats['concept_frequency'][concept] += 1
            
            # 独立点概念频率
            for point in points:
                if point not in stats['concept_frequency']:
                    stats['concept_frequency'][point] = 0
                stats['concept_frequency'][point] += 1
        
        # 计算平均路径长度
        if all_path_lengths:
            stats['avg_path_length'] = sum(all_path_lengths) / len(all_path_lengths)
        
        return stats
