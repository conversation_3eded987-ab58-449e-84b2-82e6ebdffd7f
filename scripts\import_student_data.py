#!/usr/bin/env python3
"""
学生信息和做题记录导入脚本
支持批量导入学生基本信息、答题记录、掌握度和易错点数据
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime, timezone
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入生成器
from generate_student_records import StudentRecordGenerator

from graphmasal.config import settings
from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    # 使用环境变量或默认配置
    neo4j_uri = "bolt://localhost:7687"
    neo4j_user = "neo4j"
    neo4j_password = "12345678"

    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
    )

    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL,
        base_url=embedding_base_url,
    )

    llm_client = OpenAIGenericClient(config=llm_config)
    embedder = OpenAIEmbedder(config=embedder_config)
    cross_encoder = OpenAIRerankerClient(config=llm_config)

    return Graphiti(
        neo4j_uri,
        neo4j_user,
        neo4j_password,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

class StudentDataImporter:
    """学生数据导入器"""
    
    def __init__(self, client: Graphiti):
        self.client = client
        self.stats = {
            'students_created': 0,
            'attempts_created': 0
        }
    
    async def load_student_data(self, file_path: Path) -> List[Dict[str, Any]]:
        """加载学生数据文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载学生数据文件: {file_path}, 学生数: {len(data)}")
            return data
        except Exception as e:
            logger.error(f"加载学生数据文件失败 {file_path}: {e}")
            return []
    
    async def batch_create_students(self, students_data: List[Dict]) -> None:
        """批量创建学生节点"""
        if not students_data:
            return

        students = []
        for student in students_data:
            students.append({
                'studentId': student['student_id'],
                'name': student['name'],
                'createdAt': datetime.now(timezone.utc).isoformat()
            })

        query = """
        UNWIND $students AS student
        MERGE (s:Student {studentId: student.studentId})
        SET s.name = student.name,
            s.createdAt = student.createdAt
        RETURN count(s) as created_count
        """

        try:
            result = await self.client.driver.execute_query(query, students=students)
            count = result.records[0]['created_count']
            self.stats['students_created'] += count
            logger.info(f"批量创建学生节点完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建学生节点失败: {e}")
    
    async def batch_create_attempts(self, students_data: List[Dict]) -> None:
        """批量创建答题记录"""
        attempts = []
        
        for student in students_data:
            student_id = student['student_id']
            for attempt in student.get('problem_attempts', []):
                attempts.append({
                    'studentId': student_id,
                    'problemId': attempt['problem_id'],
                    'selectedOptions': attempt['selected_options'],
                    'isFullyCorrect': attempt['is_fully_correct'],
                    'timestamp': attempt.get('timestamp', datetime.now(timezone.utc).isoformat()),
                    'difficulty': attempt.get('difficulty', 0.0)
                })
        
        if not attempts:
            return
        
        query = """
        UNWIND $attempts AS attempt
        MERGE (student:Student {studentId: attempt.studentId})
        MERGE (problem:Problem {problemId: attempt.problemId})
        MERGE (student)-[att:ATTEMPTED]->(problem)
        SET att.selectedOptions = attempt.selectedOptions,
            att.isFullyCorrect = attempt.isFullyCorrect,
            att.timestamp = datetime(attempt.timestamp),
            att.difficulty = attempt.difficulty
        RETURN count(att) as created_count
        """
        
        try:
            result = await self.client.driver.execute_query(query, attempts=attempts)
            count = result.records[0]['created_count']
            self.stats['attempts_created'] += count
            logger.info(f"批量创建答题记录完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建答题记录失败: {e}")
    

    
    async def import_all_student_data(self, file_path: Path) -> None:
        """导入所有学生数据"""
        logger.info("开始批量导入学生数据...")

        start_time = datetime.now()

        try:
            # 加载数据
            students_data = await self.load_student_data(file_path)
            if not students_data:
                return

            # 批量创建学生节点
            await self.batch_create_students(students_data)

            # 批量创建答题记录
            await self.batch_create_attempts(students_data)

            # 打印统计信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info("=" * 60)
            logger.info("🎉 学生数据导入完成！统计信息:")
            logger.info(f"👤 学生节点: {self.stats['students_created']}")
            logger.info(f"✍️ 答题记录: {self.stats['attempts_created']}")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"学生数据导入失败: {e}")
            raise

    async def generate_and_import_from_problems(self, problems_file: Path, num_students: int = 50) -> None:
        """从题目数据生成并导入学生记录"""
        logger.info("开始从题目数据生成并导入学生记录...")

        try:
            # 创建生成器
            generator = StudentRecordGenerator(
                num_students=num_students,
                min_attempts=10,
                max_attempts=30
            )

            # 加载题目数据
            problems = generator.load_problems_data(problems_file)
            if not problems:
                logger.error("无法加载题目数据")
                return

            # 生成学生记录
            students_data = generator.generate_all_student_records(problems)

            # 直接导入到数据库
            await self.batch_create_students(students_data)
            await self.batch_create_attempts(students_data)

            logger.info("=" * 60)
            logger.info("🎉 从题目数据生成并导入学生记录完成！")
            logger.info(f"👤 学生节点: {self.stats['students_created']}")
            logger.info(f"✍️ 答题记录: {self.stats['attempts_created']}")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"从题目数据生成并导入失败: {e}")
            raise

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='导入学生信息和做题记录')
    parser.add_argument('--file',
                       default='data/student_data.json',
                       help='学生数据文件路径')
    parser.add_argument('--sample', action='store_true',
                       help='生成示例数据文件')
    parser.add_argument('--generate-from-problems',
                       help='从题目数据文件生成并导入学生记录')
    parser.add_argument('--num-students', type=int, default=50,
                       help='生成的学生数量（与--generate-from-problems一起使用）')
    
    args = parser.parse_args()
    
    if args.sample:
        # 生成示例数据文件
        await generate_sample_data(Path(args.file))
        return

    if args.generate_from_problems:
        # 从题目数据生成并导入
        problems_file = Path(args.generate_from_problems)
        if not problems_file.exists():
            logger.error(f"题目数据文件不存在: {problems_file}")
            return

        try:
            # 验证配置
            settings.validate()

            # 创建 Graphiti 客户端
            client = create_graphiti_client()

            # 创建导入器
            importer = StudentDataImporter(client)

            # 从题目数据生成并导入
            await importer.generate_and_import_from_problems(problems_file, args.num_students)

            logger.info("从题目数据生成并导入学生记录完成！")
            return

        except Exception as e:
            logger.error(f"从题目数据生成并导入失败: {e}")
            raise

    # 验证文件路径
    data_file = Path(args.file)
    if not data_file.exists():
        logger.error(f"学生数据文件不存在: {data_file}")
        return
    
    try:
        # 验证配置
        settings.validate()
        
        # 创建 Graphiti 客户端
        client = create_graphiti_client()
        
        # 创建导入器
        importer = StudentDataImporter(client)
        
        # 执行导入
        await importer.import_all_student_data(data_file)
        
        logger.info("学生数据导入任务完成！")
        
    except Exception as e:
        logger.error(f"学生数据导入失败: {e}")
        raise

async def generate_sample_data(file_path: Path):
    """生成示例学生数据文件"""
    sample_data = [
        {
            "student_id": "student_001",
            "name": "张三",
            "problem_attempts": [
                {
                    "problem_id": 1,
                    "selected_options": ["A", "C"],
                    "is_fully_correct": False,
                    "timestamp": "2025-01-15T10:30:00Z",
                    "difficulty": 0.6
                },
                {
                    "problem_id": 5,
                    "selected_options": ["B"],
                    "is_fully_correct": True,
                    "timestamp": "2025-01-15T11:00:00Z",
                    "difficulty": 0.4
                }
            ]
        },
        {
            "student_id": "student_002",
            "name": "李四",
            "problem_attempts": [
                {
                    "problem_id": 2,
                    "selected_options": ["A", "B"],
                    "is_fully_correct": True,
                    "timestamp": "2025-01-15T14:20:00Z",
                    "difficulty": 0.7
                },
                {
                    "problem_id": 3,
                    "selected_options": ["C"],
                    "is_fully_correct": False,
                    "timestamp": "2025-01-15T15:00:00Z",
                    "difficulty": 0.5
                }
            ]
        },
        {
            "student_id": "student_003",
            "name": "王五",
            "problem_attempts": [
                {
                    "problem_id": 1,
                    "selected_options": ["B"],
                    "is_fully_correct": True,
                    "timestamp": "2025-01-16T09:15:00Z",
                    "difficulty": 0.6
                }
            ]
        }
    ]

    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)

    logger.info(f"示例学生数据文件已生成: {file_path}")
    print(f"示例数据文件已生成: {file_path}")
    print("\n数据格式说明:")
    print("- student_id: 学生唯一标识符")
    print("- name: 学生姓名")
    print("- problem_attempts: 答题记录列表")
    print("  - problem_id: 题目ID（必须在知识图谱中存在）")
    print("  - selected_options: 选择的选项")
    print("  - is_fully_correct: 是否完全正确")
    print("  - timestamp: 答题时间（可选）")
    print("  - difficulty: 题目难度（可选）")
    print(f"\n使用以下命令导入数据：")
    print(f"python scripts/import_student_data.py --file {file_path}")

if __name__ == "__main__":
    asyncio.run(main())
