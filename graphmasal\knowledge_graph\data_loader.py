"""
数据加载器模块
加载示例数据到知识图谱
"""
import json
import logging
from typing import Dict, List, Any
from pathlib import Path
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class DataLoader:
    """数据加载器类"""
    
    def __init__(self, client: Graphiti):
        self.client = client
    
    async def load_sample_problem(self, problem_data: Dict[str, Any]):
        """加载单个题目数据"""
        
        try:
            # 创建题目节点
            problem_query = """
            MERGE (problem:Problem {
                problemId: $problem_id,
                content: $content,
                difficulty: $difficulty,
                correctAnswer: $correct_answer,
                imageUrls: $image_urls
            })
            RETURN problem
            """
            
            await self.client.driver.execute_query(
                problem_query,
                problem_id=problem_data['question_id'],
                content=problem_data['content'],
                difficulty=problem_data['difficulty'],
                correct_answer=problem_data['correct_answer'],
                image_urls=problem_data.get('question_img_list', [])
            )
            
            # 创建题目与知识点的关系
            for kp_id in problem_data.get('linked_kp_ids', []):
                concept_relation_query = """
                MERGE (problem:Problem {problemId: $problem_id})
                MERGE (concept:Concept {kpId: $kp_id})
                MERGE (problem)-[:TESTS_CONCEPT]->(concept)
                """
                
                await self.client.driver.execute_query(
                    concept_relation_query,
                    problem_id=problem_data['question_id'],
                    kp_id=kp_id
                )
            
            # 创建易错点和关系
            misconception_map = problem_data.get('misconception_map', {})
            for option, description in misconception_map.items():
                misconception_id = f"{problem_data['question_id']}_{option}"
                
                # 创建易错点节点
                misconception_query = """
                MERGE (misconception:Misconception {
                    misconceptionId: $misconception_id,
                    description: $description
                })
                RETURN misconception
                """
                
                await self.client.driver.execute_query(
                    misconception_query,
                    misconception_id=misconception_id,
                    description=description
                )
                
                # 创建题目选项与易错点的关系
                option_relation_query = """
                MERGE (problem:Problem {problemId: $problem_id})
                MERGE (misconception:Misconception {misconceptionId: $misconception_id})
                MERGE (problem)-[:OPTION_TARGETS {option_id: $option}]->(misconception)
                """
                
                await self.client.driver.execute_query(
                    option_relation_query,
                    problem_id=problem_data['question_id'],
                    misconception_id=misconception_id,
                    option=option
                )
                
                # 尝试将易错点与相关概念关联
                # 这里简化处理，实际应该有更复杂的逻辑
                for kp_id in problem_data.get('linked_kp_ids', []):
                    confusion_query = """
                    MERGE (misconception:Misconception {misconceptionId: $misconception_id})
                    MERGE (concept:Concept {kpId: $kp_id})
                    MERGE (misconception)-[:IS_CONFUSION_OF]->(concept)
                    """
                    
                    await self.client.driver.execute_query(
                        confusion_query,
                        misconception_id=misconception_id,
                        kp_id=kp_id
                    )
            
            logger.info(f"题目 {problem_data['question_id']} 加载成功")
            
        except Exception as e:
            logger.error(f"加载题目 {problem_data.get('question_id', 'unknown')} 失败: {e}")
    
    async def load_sample_data(self):
        """加载示例数据"""
        
        # 示例题目数据（基于examplejsondata.txt）
        sample_problem = {
            "question_id": 10200942,
            "content": "如图所示，完全相同的容器E、F、G，小孔a与大气相通,容器口封闭，T为阀门，水面的高度相同。在E静止、F、G同时竖直向上和向下以加速度a运动的同时打开三个容器的阀门，则以下说法中正确的是",
            "correct_answer": "C",
            "with_img": True,
            "question_img_list": [
                "MLLM_data_2_v2/PHYSICS_images/10200942_question_0.png"
            ],
            "difficulty": 7.3,
            "linked_kp_ids": [
                "力学/运动学/相对运动概念",
                "力学/相互作用与牛顿定律/牛顿第二定律应用",
                "力学/动量与能量/动能定理与机械能守恒定律"
            ],
            "misconception_map": {
                "A": "未考虑容器加速度对等效重力的影响（参见力学/运动学/相对运动概念）",
                "B": "混淆加速度方向对有效重力的增强/减弱作用（参见力学/相互作用与牛顿定律/牛顿第二定律应用）",
                "D": "忽略向下加速度可能导致液面失压的临界条件（参见力学/动量与能量/动能定理与机械能守恒定律）"
            }
        }
        
        # 加载示例题目
        await self.load_sample_problem(sample_problem)
        
        # 创建示例学生
        await self.create_sample_student("student_001", "张三")
        
        logger.info("示例数据加载完成")
    
    async def create_sample_student(self, student_id: str, name: str):
        """创建示例学生"""
        
        try:
            # 创建学生节点
            student_query = """
            MERGE (student:Student {
                studentId: $student_id,
                name: $name
            })
            RETURN student
            """
            
            await self.client.driver.execute_query(
                student_query,
                student_id=student_id,
                name=name
            )
            
            # 添加一些初始的掌握度数据
            initial_mastery = [
                ("力学/运动学/相对运动概念", 0.3),
                ("力学/相互作用与牛顿定律/力的概念与合成", 0.7),
                ("力学/运动学/加速度的概念", 0.5)
            ]
            
            for concept_id, proficiency in initial_mastery:
                mastery_query = """
                MERGE (student:Student {studentId: $student_id})
                MERGE (concept:Concept {kpId: $concept_id})
                MERGE (student)-[mastery:HAS_MASTERY_OF]->(concept)
                SET mastery.proficiency = $proficiency,
                    mastery.lastUpdated = datetime()
                """
                
                await self.client.driver.execute_query(
                    mastery_query,
                    student_id=student_id,
                    concept_id=concept_id,
                    proficiency=proficiency
                )
            
            # 将学生信息添加到Graphiti
            await self.client.add_episode(
                name=f"Student Profile: {name}",
                episode_body=f"学生 {name} (ID: {student_id}) 开始使用教育辅导系统",
                source=EpisodeType.text,
                source_description="Student Registration",
                reference_time=datetime.now(timezone.utc)
            )
            
            logger.info(f"示例学生 {name} ({student_id}) 创建成功")
            
        except Exception as e:
            logger.error(f"创建示例学生失败: {e}")
    
    async def load_prerequisite_relationships(self):
        """加载前置依赖关系"""
        
        # 示例前置依赖关系（基于examplejsondata.txt中的target_concept和direct_prerequisites）
        prerequisites = [
            {
                "target": "力学/相互作用与牛顿定律/牛顿第二定律应用",
                "prerequisites": [
                    "力学/相互作用与牛顿定律/力的概念与合成",
                    "力学/运动学/加速度的概念"
                ]
            }
        ]
        
        try:
            for item in prerequisites:
                target_concept = item["target"]
                
                for prerequisite in item["prerequisites"]:
                    query = """
                    MERGE (prerequisite:Concept {kpId: $prerequisite})
                    MERGE (target:Concept {kpId: $target})
                    MERGE (prerequisite)-[:IS_PREREQUISITE_FOR]->(target)
                    """
                    
                    await self.client.driver.execute_query(
                        query,
                        prerequisite=prerequisite,
                        target=target_concept
                    )
            
            logger.info("前置依赖关系加载完成")
            
        except Exception as e:
            logger.error(f"加载前置依赖关系失败: {e}")
    
    async def load_all_sample_data(self):
        """加载所有示例数据"""
        
        logger.info("开始加载示例数据...")
        
        # 加载前置依赖关系
        await self.load_prerequisite_relationships()
        
        # 加载示例数据
        await self.load_sample_data()
        
        logger.info("所有示例数据加载完成")

async def load_sample_data(client: Graphiti):
    """加载示例数据的便捷函数"""
    loader = DataLoader(client)
    await loader.load_all_sample_data()
    return loader
