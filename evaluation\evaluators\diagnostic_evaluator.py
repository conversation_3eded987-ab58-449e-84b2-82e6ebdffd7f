"""
认知诊断评测器
计算诊断准确率和生成评测报告
"""

import json
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import numpy as np

logger = logging.getLogger(__name__)

class DiagnosticEvaluator:
    """认知诊断评测器"""
    
    def __init__(self):
        """初始化评测器"""
        self.logger = logger
    
    def evaluate_diagnostic_accuracy(
        self, 
        predicted_results: List[Dict[str, Any]], 
        ground_truth: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        评测诊断准确率
        
        Args:
            predicted_results: 智能体预测的诊断结果列表
            ground_truth: 真实标签列表
            
        Returns:
            评测结果字典
        """
        try:
            if len(predicted_results) != len(ground_truth):
                raise ValueError(f"预测结果数量({len(predicted_results)})与真实标签数量({len(ground_truth)})不匹配")
            
            total_diagnoses = 0
            correct_diagnoses = 0
            detailed_results = []
            
            # 逐个比较诊断结果
            for i, (predicted, truth) in enumerate(zip(predicted_results, ground_truth)):
                result = self._compare_single_diagnosis(predicted, truth, i)
                detailed_results.append(result)
                
                total_diagnoses += result['total_knowledge_points']
                correct_diagnoses += result['correct_knowledge_points']
            
            # 计算总体准确率
            overall_accuracy = correct_diagnoses / total_diagnoses if total_diagnoses > 0 else 0.0
            
            # 生成评测报告
            evaluation_report = {
                'evaluation_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'evaluator_version': '1.0.0',
                    'total_cases': len(predicted_results)
                },
                'accuracy_metrics': {
                    'overall_accuracy': overall_accuracy,
                    'total_diagnoses': total_diagnoses,
                    'correct_diagnoses': correct_diagnoses,
                    'accuracy_by_case': [r['case_accuracy'] for r in detailed_results]
                },
                'detailed_results': detailed_results,
                'summary_statistics': self._calculate_summary_statistics(detailed_results)
            }
            
            self.logger.info(f"诊断准确率评测完成: 总体准确率 {overall_accuracy:.3f}")
            return evaluation_report
            
        except Exception as e:
            self.logger.error(f"诊断准确率评测失败: {e}")
            return self._create_error_evaluation(str(e))
    
    def _compare_single_diagnosis(
        self, 
        predicted: Dict[str, Any], 
        truth: Dict[str, Any], 
        case_index: int
    ) -> Dict[str, Any]:
        """
        比较单个诊断结果
        
        Args:
            predicted: 预测的诊断结果
            truth: 真实标签
            case_index: 案例索引
            
        Returns:
            单个案例的比较结果
        """
        try:
            # 提取预测的知识点掌握情况
            predicted_mastery = predicted.get('diagnostic_result', {}).get('knowledge_point_mastery', {})
            
            # 提取真实的知识点掌握情况
            truth_mastery = truth.get('knowledge_point_mastery', {})
            
            # 找到所有需要诊断的知识点
            all_knowledge_points = set(predicted_mastery.keys()) | set(truth_mastery.keys())
            
            correct_count = 0
            total_count = len(all_knowledge_points)
            knowledge_point_results = {}
            
            for kp in all_knowledge_points:
                predicted_status = self._get_mastery_status(predicted_mastery.get(kp, {}))
                truth_status = self._get_mastery_status(truth_mastery.get(kp, {}))
                
                is_correct = predicted_status == truth_status
                if is_correct:
                    correct_count += 1
                
                knowledge_point_results[kp] = {
                    'predicted_status': predicted_status,
                    'truth_status': truth_status,
                    'is_correct': is_correct,
                    'predicted_mastery_level': predicted_mastery.get(kp, {}).get('mastery_level', 0.0),
                    'truth_mastery_level': truth_mastery.get(kp, {}).get('mastery_level', 0.0)
                }
            
            case_accuracy = correct_count / total_count if total_count > 0 else 0.0
            
            return {
                'case_index': case_index,
                'student_id': predicted.get('evaluation_metadata', {}).get('student_id', 'unknown'),
                'problem_id': predicted.get('evaluation_metadata', {}).get('problem_id', 0),
                'case_accuracy': case_accuracy,
                'total_knowledge_points': total_count,
                'correct_knowledge_points': correct_count,
                'knowledge_point_results': knowledge_point_results,
                'misconception_analysis': self._compare_misconceptions(predicted, truth)
            }
            
        except Exception as e:
            self.logger.error(f"单个诊断比较失败 (案例 {case_index}): {e}")
            return {
                'case_index': case_index,
                'case_accuracy': 0.0,
                'total_knowledge_points': 0,
                'correct_knowledge_points': 0,
                'error': str(e)
            }
    
    def _get_mastery_status(self, mastery_info: Dict[str, Any]) -> str:
        """
        从掌握度信息中提取掌握状态
        
        Args:
            mastery_info: 掌握度信息字典
            
        Returns:
            掌握状态字符串
        """
        if isinstance(mastery_info, dict):
            if 'status' in mastery_info:
                return mastery_info['status']
            elif 'mastery_level' in mastery_info:
                level = mastery_info['mastery_level']
                if level >= 0.8:
                    return 'mastered'
                elif level >= 0.6:
                    return 'partially_mastered'
                elif level >= 0.3:
                    return 'weak'
                else:
                    return 'not_mastered'
        
        return 'unknown'
    
    def _compare_misconceptions(
        self, 
        predicted: Dict[str, Any], 
        truth: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        比较易错点识别结果
        
        Args:
            predicted: 预测结果
            truth: 真实标签
            
        Returns:
            易错点比较结果
        """
        try:
            predicted_misconceptions = set()
            truth_misconceptions = set()
            
            # 提取预测的易错点
            pred_misc = predicted.get('diagnostic_result', {}).get('identified_misconceptions', [])
            for misc in pred_misc:
                if isinstance(misc, dict):
                    misc_id = misc.get('misconception_id', misc.get('description', ''))
                else:
                    misc_id = str(misc)
                predicted_misconceptions.add(misc_id)
            
            # 提取真实的易错点
            truth_misc = truth.get('identified_misconceptions', [])
            for misc in truth_misc:
                if isinstance(misc, dict):
                    misc_id = misc.get('misconception_id', misc.get('description', ''))
                else:
                    misc_id = str(misc)
                truth_misconceptions.add(misc_id)
            
            # 计算精确率、召回率和F1分数
            intersection = predicted_misconceptions & truth_misconceptions
            
            precision = len(intersection) / len(predicted_misconceptions) if predicted_misconceptions else 0.0
            recall = len(intersection) / len(truth_misconceptions) if truth_misconceptions else 0.0
            f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
            
            return {
                'predicted_misconceptions': list(predicted_misconceptions),
                'truth_misconceptions': list(truth_misconceptions),
                'correctly_identified': list(intersection),
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score
            }
            
        except Exception as e:
            self.logger.error(f"易错点比较失败: {e}")
            return {
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'error': str(e)
            }
    
    def _calculate_summary_statistics(self, detailed_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算汇总统计信息
        
        Args:
            detailed_results: 详细结果列表
            
        Returns:
            汇总统计信息
        """
        try:
            accuracies = [r.get('case_accuracy', 0.0) for r in detailed_results]
            
            # 易错点识别统计
            misconception_precisions = []
            misconception_recalls = []
            misconception_f1s = []
            
            for result in detailed_results:
                misc_analysis = result.get('misconception_analysis', {})
                misconception_precisions.append(misc_analysis.get('precision', 0.0))
                misconception_recalls.append(misc_analysis.get('recall', 0.0))
                misconception_f1s.append(misc_analysis.get('f1_score', 0.0))
            
            return {
                'accuracy_statistics': {
                    'mean': np.mean(accuracies),
                    'std': np.std(accuracies),
                    'min': np.min(accuracies),
                    'max': np.max(accuracies),
                    'median': np.median(accuracies)
                },
                'misconception_statistics': {
                    'avg_precision': np.mean(misconception_precisions),
                    'avg_recall': np.mean(misconception_recalls),
                    'avg_f1_score': np.mean(misconception_f1s)
                },
                'performance_distribution': {
                    'excellent': sum(1 for acc in accuracies if acc >= 0.9),
                    'good': sum(1 for acc in accuracies if 0.7 <= acc < 0.9),
                    'fair': sum(1 for acc in accuracies if 0.5 <= acc < 0.7),
                    'poor': sum(1 for acc in accuracies if acc < 0.5)
                }
            }
            
        except Exception as e:
            self.logger.error(f"汇总统计计算失败: {e}")
            return {'error': str(e)}
    
    def _create_error_evaluation(self, error_message: str) -> Dict[str, Any]:
        """创建错误评测结果"""
        return {
            'evaluation_metadata': {
                'timestamp': datetime.now().isoformat(),
                'evaluator_version': '1.0.0',
                'error': error_message
            },
            'accuracy_metrics': {
                'overall_accuracy': 0.0,
                'total_diagnoses': 0,
                'correct_diagnoses': 0
            },
            'detailed_results': [],
            'summary_statistics': {}
        }
    
    def save_evaluation_results(self, results: Dict[str, Any], filepath: str) -> bool:
        """保存评测结果到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"评测结果已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存评测结果失败: {e}")
            return False
    
    def generate_evaluation_report(self, results: Dict[str, Any]) -> str:
        """生成可读的评测报告"""
        try:
            report_lines = []
            report_lines.append("=" * 60)
            report_lines.append("认知诊断评测报告")
            report_lines.append("=" * 60)
            
            # 基本信息
            metadata = results.get('evaluation_metadata', {})
            report_lines.append(f"评测时间: {metadata.get('timestamp', 'N/A')}")
            report_lines.append(f"评测案例数: {metadata.get('total_cases', 0)}")
            report_lines.append("")
            
            # 准确率指标
            accuracy_metrics = results.get('accuracy_metrics', {})
            report_lines.append("准确率指标:")
            report_lines.append(f"  总体准确率: {accuracy_metrics.get('overall_accuracy', 0.0):.3f}")
            report_lines.append(f"  正确诊断数: {accuracy_metrics.get('correct_diagnoses', 0)}")
            report_lines.append(f"  总诊断数: {accuracy_metrics.get('total_diagnoses', 0)}")
            report_lines.append("")
            
            # 汇总统计
            summary_stats = results.get('summary_statistics', {})
            if 'accuracy_statistics' in summary_stats:
                acc_stats = summary_stats['accuracy_statistics']
                report_lines.append("准确率统计:")
                report_lines.append(f"  平均值: {acc_stats.get('mean', 0.0):.3f}")
                report_lines.append(f"  标准差: {acc_stats.get('std', 0.0):.3f}")
                report_lines.append(f"  最小值: {acc_stats.get('min', 0.0):.3f}")
                report_lines.append(f"  最大值: {acc_stats.get('max', 0.0):.3f}")
                report_lines.append("")
            
            # 性能分布
            if 'performance_distribution' in summary_stats:
                perf_dist = summary_stats['performance_distribution']
                report_lines.append("性能分布:")
                report_lines.append(f"  优秀 (≥0.9): {perf_dist.get('excellent', 0)} 个案例")
                report_lines.append(f"  良好 (0.7-0.9): {perf_dist.get('good', 0)} 个案例")
                report_lines.append(f"  一般 (0.5-0.7): {perf_dist.get('fair', 0)} 个案例")
                report_lines.append(f"  较差 (<0.5): {perf_dist.get('poor', 0)} 个案例")
                report_lines.append("")
            
            # 易错点识别统计
            if 'misconception_statistics' in summary_stats:
                misc_stats = summary_stats['misconception_statistics']
                report_lines.append("易错点识别统计:")
                report_lines.append(f"  平均精确率: {misc_stats.get('avg_precision', 0.0):.3f}")
                report_lines.append(f"  平均召回率: {misc_stats.get('avg_recall', 0.0):.3f}")
                report_lines.append(f"  平均F1分数: {misc_stats.get('avg_f1_score', 0.0):.3f}")
            
            report_lines.append("=" * 60)
            
            return "\n".join(report_lines)
            
        except Exception as e:
            self.logger.error(f"生成评测报告失败: {e}")
            return f"报告生成失败: {str(e)}"
