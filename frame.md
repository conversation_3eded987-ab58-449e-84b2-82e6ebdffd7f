flowchart TD

 subgraph s1["用户端"]

        A["学习者界面"]

  end

 subgraph s2["多智能体系统"]

        B["辅导智能体"]

        C["诊断智能体"]

        D["规划智能体"]

  end

 subgraph s3["核心驱动"]

       

        E["动态知识图谱Graphiti"]

  end

 subgraph s4["数据存储"]

        F["Neo4j数据库"]

  end

    A <-- 交互/反馈 --> B

    B -- 诊断请求 --> C

   

    C -- 查询 --> E

    C -- 诊断结果 --> B

    B -- 规划请求 --> D

   

    D -- 查询/更新 --> E

    D -- 学习路径 --> B

    B -- 呈现路径/内容 --> A

    E --- F