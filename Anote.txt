cd graphiti/mcp_server/
uv run graphiti_mcp_server.py --model doubao-seed-1.6-250615 --transport sse
默认模型text-embedding-3-small改doubao-embedding-text-240715


知识图谱导入数据
python "scripts/bulk_import_knowledge_graph.py" --help
usage: bulk_import_knowledge_graph copy.py [-h] [--relationships RELATIONSHIPS] [--problems PROBLEMS] [--clear]
python "scripts/bulk_import_knowledge_graph copy.py" --relationships data\physics_knowledge_relationships.json --problems data\start_0_end_200_new_data.json
批量导入知识图谱数据

options:
  -h, --help            show this help message and exit
  --relationships RELATIONSHIPS
                        知识点关系数据文件路径
  --problems PROBLEMS   题目数据文件路径
  --clear               清除现有数据重新导入


知识图谱预览
http://locdata/lhost:7474
创建学生信息
{
studentId: 字符串类型，作为唯一标识
name: 字符串类型，学生姓名
}

构建学生答题记录json
{
    "student_id": "S105",
    "problem_id": 10200942,
    "selected_options": ["C"],
    "is_fully_correct": true
  }

学生信息导入
python scripts/import_student_data.py  --file F:\AgentProgram\cq02graphiti\graphmasal\data\generated_student_data.json

python -m graphmasal.main --demo planning --no-sample-data





