"""
主辅导智能体模块
集成诊断、规划、查询工具的LangGraph智能体
"""
import asyncio
import json
import logging
from typing import Annotated, Dict, Any, Optional
from datetime import datetime, timezone

from langchain_core.messages import AIMessage, SystemMessage, HumanMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.prebuilt import ToolNode

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType

from graphmasal.models.state import TutorState, StudentInfo
from graphmasal.config.settings import settings
from graphmasal.agents.tools.diagnostic_tool import diagnose_student_performance, analyze_student_from_database
from graphmasal.agents.tools.planning_tool import generate_learning_path
from graphmasal.agents.tools.query_tool import search_knowledge, recommend_problems

logger = logging.getLogger(__name__)

# 导入客户端管理功能
from graphmasal.agents.client_manager import (
    set_graphiti_client,
    get_graphiti_client,
    is_graphiti_client_initialized
)

class TutorAgent:
    """主辅导智能体类"""
    
    def __init__(self, client: Graphiti):
        self.client = client
        set_graphiti_client(client)  # 设置全局客户端供工具使用
        logger.info(f"TutorAgent 初始化完成，客户端已设置: {client is not None}")
        
        # 初始化LLM
        llm_kwargs = {
            "model": settings.OPENAI_MODEL,
            "temperature": settings.TEMPERATURE,
            "api_key": settings.OPENAI_API_KEY  # 显式传递API密钥
        }

        # 如果设置了自定义base_url，则使用它（豆包等第三方API需要）
        if settings.OPENAI_BASE_URL:
            llm_kwargs["base_url"] = settings.OPENAI_BASE_URL

        # 对于豆包等第三方API，可能需要额外的配置
        try:
            self.llm = ChatOpenAI(**llm_kwargs)
        except Exception as e:
            # 如果初始化失败，尝试设置环境变量
            import os
            os.environ["OPENAI_API_KEY"] = settings.OPENAI_API_KEY
            if settings.OPENAI_BASE_URL:
                os.environ["OPENAI_BASE_URL"] = settings.OPENAI_BASE_URL
            self.llm = ChatOpenAI(**llm_kwargs)
        
        # 定义工具列表
        self.tools = [
            analyze_student_from_database,
            diagnose_student_performance,
            generate_learning_path,
            search_knowledge,
            recommend_problems
        ]
        
        # 绑定工具到LLM，并传递客户端引用
        self.llm_with_tools = self.llm.bind_tools(self.tools)
        
        # 创建工具节点
        self.tool_node = ToolNode(self.tools)
        
        # 创建内存保存器
        self.memory = MemorySaver()
        
        # 构建图
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph图"""
        
        # 创建状态图
        graph_builder = StateGraph(TutorState)
        
        # 添加节点
        graph_builder.add_node("agent", self._agent_node)
        graph_builder.add_node("tools", self.tool_node)
        
        # 添加边
        graph_builder.add_edge(START, "agent")
        graph_builder.add_conditional_edges(
            "agent",
            self._should_continue,
            {"continue": "tools", "end": END}
        )
        graph_builder.add_edge("tools", "agent")
        
        # 编译图
        return graph_builder.compile(checkpointer=self.memory)
    
    async def _agent_node(self, state: TutorState) -> Dict[str, Any]:
        """智能体主节点"""
        
        # 获取相关上下文信息
        context_info = await self._get_context_info(state)
        
        # 构建系统消息
        system_message = self._build_system_message(state, context_info)
        
        # 准备消息列表
        messages = [system_message] + state["messages"]
        
        # 调用LLM
        response = await self.llm_with_tools.ainvoke(messages)
        
        # 异步保存对话到Graphiti
        asyncio.create_task(self._save_conversation(state, response))
        
        return {"messages": [response]}
    
    async def _should_continue(self, state: TutorState, config) -> str:
        """判断是否继续执行工具"""
        messages = state["messages"]
        last_message = messages[-1]
        
        # 如果有工具调用，继续执行工具
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        else:
            return "end"
    
    def _build_system_message(self, state: TutorState, context_info: Dict[str, Any]) -> SystemMessage:
        """构建系统消息"""
        
        student_info = state.get("student_info")
        current_mode = state.get("current_mode", "chat")
        
        # 基础系统提示
        base_prompt = """你是一个专业的AI教育辅导老师，专门帮助学生学习物理知识。你的目标是：

1. **个性化辅导**: 根据学生的学习情况提供针对性指导
2. **错误诊断**: 分析学生的答题错误，识别易错点
3. **路径规划**: 为学生制定个性化的学习路径
4. **知识查询**: 帮助学生查找相关知识点和练习题

你有以下工具可以使用：
- analyze_student_from_database: 从数据库中分析学生的答题表现（推荐使用，当学生提到其数据已在系统中时）
- diagnose_student_performance: 诊断学生答题表现（需要学生提供具体题目和答案）
- generate_learning_path: 生成个性化学习路径
- search_knowledge: 搜索知识图谱信息
- recommend_problems: 推荐练习题目

**重要优化提示**：
- 如果学生要求分析答题情况后制定学习计划，先使用 analyze_student_from_database，然后直接使用 generate_learning_path
- 系统会自动缓存已获取的数据，避免重复查询
- 优先使用已缓存的数据，提高响应速度

请保持友好、耐心、专业的态度，用中文与学生交流。"""
        
        # 添加学生信息
        if student_info:
            base_prompt += f"\n\n当前学生信息：\n- 学生ID: {student_info.student_id}\n- 姓名: {student_info.name}"
            if student_info.current_subject:
                base_prompt += f"\n- 当前学科: {student_info.current_subject}"
        
        # 添加上下文信息
        if context_info.get("recent_facts"):
            base_prompt += f"\n\n相关背景信息：\n{context_info['recent_facts']}"
        
        # 添加当前模式信息
        mode_prompts = {
            "chat": "当前处于对话模式，可以回答学生的问题并提供学习建议。",
            "problem_solving": "当前处于答题模式，重点关注学生的答题情况和错误分析。",
            "path_planning": "当前处于路径规划模式，重点为学生制定学习计划。",
            "diagnostic": "当前处于诊断模式，重点分析学生的学习问题。"
        }
        
        if current_mode in mode_prompts:
            base_prompt += f"\n\n{mode_prompts[current_mode]}"
        
        return SystemMessage(content=base_prompt)
    
    async def _get_context_info(self, state: TutorState) -> Dict[str, Any]:
        """获取上下文信息"""
        
        context_info = {}
        
        # 如果有学生信息，获取相关的Graphiti事实
        if state.get("student_info") and len(state["messages"]) > 0:
            try:
                last_message = state["messages"][-1]
                if isinstance(last_message, HumanMessage):
                    # 搜索相关事实
                    search_results = await self.client.search(
                        query=last_message.content,
                        num_results=5
                    )
                    
                    if search_results:
                        facts = [edge.fact for edge in search_results]
                        context_info["recent_facts"] = "\n".join(f"- {fact}" for fact in facts)
                        
            except Exception as e:
                logger.error(f"获取上下文信息失败: {e}")
        
        return context_info
    
    async def _save_conversation(self, state: TutorState, response: AIMessage):
        """保存对话到Graphiti"""
        
        try:
            if len(state["messages"]) > 0:
                last_user_message = state["messages"][-1]
                student_info = state.get("student_info")
                
                conversation_text = f"学生: {last_user_message.content}\n辅导老师: {response.content}"
                
                await self.client.add_episode(
                    name="Tutoring Conversation",
                    episode_body=conversation_text,
                    source=EpisodeType.message,
                    source_description=f"Tutor conversation with {student_info.name if student_info else 'student'}",
                    reference_time=datetime.now(timezone.utc)
                )
                
                logger.info("对话已保存到Graphiti")
                
        except Exception as e:
            logger.error(f"保存对话失败: {e}")
    
    async def chat(
        self,
        message: str,
        student_info: StudentInfo,
        thread_id: str = "default"
    ) -> str:
        """与智能体对话"""

        # 如果是新的线程，重置会话状态
        if not hasattr(self, '_current_thread_id') or self._current_thread_id != thread_id:
            from graphmasal.agents.session_state import reset_session_state
            reset_session_state()
            self._current_thread_id = thread_id
            logger.info(f"新会话开始，线程ID: {thread_id}")

        # 构建初始状态
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "student_info": student_info,
            "current_mode": "chat",
            "current_problem": None,
            "problem_attempts": [],
            "learning_paths": [],
            "latest_diagnostic": None,
            "last_tool_used": None,
            "context_info": {},
            "graphiti_facts": []
        }
        
        # 配置
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # 执行图
            result = await self.graph.ainvoke(initial_state, config=config)
            
            # 返回最后的AI消息
            if result["messages"]:
                last_message = result["messages"][-1]
                if isinstance(last_message, AIMessage):
                    return last_message.content
            
            return "抱歉，我现在无法回应。请稍后再试。"
            
        except Exception as e:
            logger.error(f"对话执行失败: {e}")
            return f"对话过程中出现错误: {str(e)}"
    
    async def diagnose_problem(
        self,
        student_info: StudentInfo,
        problem_data: Dict[str, Any],
        thread_id: str = "default"
    ) -> Dict[str, Any]:
        """诊断学生答题情况"""
        
        # 构建诊断请求消息
        message = f"请帮我分析这道题的答题情况：{json.dumps(problem_data, ensure_ascii=False)}"
        
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "student_info": student_info,
            "current_mode": "diagnostic",
            "current_problem": problem_data,
            "problem_attempts": [],
            "learning_paths": [],
            "latest_diagnostic": None,
            "last_tool_used": None,
            "context_info": {},
            "graphiti_facts": []
        }
        
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            result = await self.graph.ainvoke(initial_state, config=config)
            return result
            
        except Exception as e:
            logger.error(f"诊断执行失败: {e}")
            return {"error": str(e)}
    
    async def plan_learning_path(
        self,
        student_info: StudentInfo,
        target_concept: str,
        thread_id: str = "default"
    ) -> Dict[str, Any]:
        """规划学习路径"""
        
        message = f"请为我制定学习 '{target_concept}' 的个性化路径"
        
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "student_info": student_info,
            "current_mode": "path_planning",
            "current_problem": None,
            "problem_attempts": [],
            "learning_paths": [],
            "latest_diagnostic": None,
            "last_tool_used": None,
            "context_info": {"target_concept": target_concept},
            "graphiti_facts": []
        }
        
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            result = await self.graph.ainvoke(initial_state, config=config)
            return result
            
        except Exception as e:
            logger.error(f"路径规划执行失败: {e}")
            return {"error": str(e)}
