"""
多智能体教育辅导系统主程序
"""
import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from config.settings import settings
from knowledge_graph.schema_init import init_knowledge_graph
from knowledge_graph.data_loader import load_sample_data
from agents.tutor_agent import TutorAgent
from models.state import StudentInfo

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('tutor_system.log')
    ]
)

logger = logging.getLogger(__name__)

class TutorSystem:
    """教育辅导系统主类"""
    
    def __init__(self):
        self.client = None
        self.tutor_agent = None

    def _create_graphiti_client(self) -> Graphiti:
        """创建配置好的Graphiti客户端"""

        # 确定使用的API密钥和基础URL
        embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
        embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

        # 配置LLM客户端
        llm_config = LLMConfig(
            api_key=settings.OPENAI_API_KEY,
            model=settings.OPENAI_MODEL,
            small_model=settings.OPENAI_MODEL,  # 使用同一个模型
            base_url=settings.OPENAI_BASE_URL,
        )

        # 配置embedding客户端
        embedder_config = OpenAIEmbedderConfig(
            api_key=embedding_api_key,
            embedding_model=settings.EMBEDDING_MODEL,
            base_url=embedding_base_url,
        )

        # 创建客户端组件
        llm_client = OpenAIGenericClient(config=llm_config)
        embedder = OpenAIEmbedder(config=embedder_config)
        cross_encoder = OpenAIRerankerClient(config=llm_config)

        # 创建Graphiti客户端
        return Graphiti(
            settings.NEO4J_URI,
            settings.NEO4J_USER,
            settings.NEO4J_PASSWORD,
            llm_client=llm_client,
            embedder=embedder,
            cross_encoder=cross_encoder
        )
    
    async def initialize(self, clear_existing: bool = False, load_sample_data_flag: bool = True):
        """初始化系统"""

        try:
            # 验证配置
            settings.validate()
            logger.info("配置验证通过")

            # 初始化Graphiti客户端
            self.client = self._create_graphiti_client()
            logger.info("Graphiti客户端初始化成功")

            # 初始化知识图谱Schema
            await init_knowledge_graph(self.client, clear_existing)
            logger.info("知识图谱Schema初始化完成")

            # 检查是否需要加载示例数据
            if load_sample_data_flag:
                # 检查是否已有数据
                existing_data = await self._check_existing_data()
                if existing_data['has_problems'] or existing_data['has_students']:
                    logger.info(f"检测到现有数据: {existing_data['problems_count']} 个题目, {existing_data['students_count']} 个学生")
                    logger.info("跳过示例数据加载")
                else:
                    # 加载示例数据
                    await load_sample_data(self.client)
                    logger.info("示例数据加载完成")
            else:
                logger.info("跳过示例数据加载")

            # 初始化辅导智能体
            self.tutor_agent = TutorAgent(self.client)
            logger.info("辅导智能体初始化完成")

            logger.info("系统初始化完成！")

        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            raise

    async def _check_existing_data(self) -> Dict[str, Any]:
        """检查现有数据"""
        try:
            # 检查题目数量
            problem_query = "MATCH (p:Problem) RETURN count(p) as count"
            problem_result = await self.client.driver.execute_query(problem_query)
            problems_count = problem_result.records[0]['count']

            # 检查学生数量
            student_query = "MATCH (s:Student) RETURN count(s) as count"
            student_result = await self.client.driver.execute_query(student_query)
            students_count = student_result.records[0]['count']

            return {
                'has_problems': problems_count > 0,
                'has_students': students_count > 0,
                'problems_count': problems_count,
                'students_count': students_count
            }
        except Exception as e:
            logger.error(f"检查现有数据失败: {e}")
            return {
                'has_problems': False,
                'has_students': False,
                'problems_count': 0,
                'students_count': 0
            }
    
    async def chat_demo(self):
        """聊天演示"""
        
        if not self.tutor_agent:
            raise RuntimeError("系统未初始化")
        
        # 创建示例学生信息
        student = StudentInfo(
            student_id="student_001",
            name="陈秀兰",
            current_subject="物理"
        )
        
        print("\n=== 多智能体教育辅导系统演示 ===")
        print(f"欢迎 {student.name}！我是你的AI辅导老师。")
        print("你可以：")
        print("1. 问我物理问题")
        print("2. 让我分析你的答题情况")
        print("3. 请我为你制定学习计划")
        print("4. 输入 'quit' 退出")
        print("-" * 50)
        
        while True:
            try:
                # 使用同步方式获取用户输入
                import sys
                sys.stdout.write("\n你: ")
                sys.stdout.flush()
                user_input = sys.stdin.readline().strip()

                # 检查是否为退出命令（必须是完整的命令，不能是响应中的片段）
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("再见！祝你学习进步！")
                    break

                if not user_input:
                    continue

                print("AI辅导老师: ", end="", flush=True)

                # 调用智能体
                response = await self.tutor_agent.chat(
                    message=user_input,
                    student_info=student,
                    thread_id="demo_session"
                )

                print(response)

            except KeyboardInterrupt:
                print("\n\n再见！")
                break
            except Exception as e:
                print(f"发生错误: {e}")
                logger.error(f"聊天过程中发生错误: {e}")
    
    async def diagnostic_demo(self):
        """诊断演示"""
        
        if not self.tutor_agent:
            raise RuntimeError("系统未初始化")
        
        # 示例答题数据
        problem_data = {
            "problem_id": 10200942,
            "selected_options": ["A"],  # 学生选择了错误答案A
            "correct_answer": ["C"],
            "misconception_map": {
                "A": "未考虑容器加速度对等效重力的影响（参见力学/运动学/相对运动概念）",
                "B": "混淆加速度方向对有效重力的增强/减弱作用（参见力学/相互作用与牛顿定律/牛顿第二定律应用）",
                "D": "忽略向下加速度可能导致液面失压的临界条件（参见力学/动量与能量/动能定理与机械能守恒定律）"
            }
        }
        
        student = StudentInfo(
            student_id="student_001",
            name="张三",
            current_subject="物理"
        )
        
        print("\n=== 答题诊断演示 ===")
        print("正在分析学生答题情况...")
        
        try:
            result = await self.tutor_agent.diagnose_problem(
                student_info=student,
                problem_data=problem_data,
                thread_id="diagnostic_demo"
            )
            
            print("诊断完成！")
            print(f"诊断结果: {result}")
            
        except Exception as e:
            print(f"诊断过程中发生错误: {e}")
            logger.error(f"诊断演示失败: {e}")
    
    async def planning_demo(self):
        """学习路径规划演示"""
        
        if not self.tutor_agent:
            raise RuntimeError("系统未初始化")
        
        student = StudentInfo(
            student_id="student_001",
            name="陈秀兰",
            current_subject="物理"
        )
        
        target_concept = "力学/相互作用与牛顿定律/牛顿第二定律应用"
        
        print("\n=== 学习路径规划演示 ===")
        print(f"正在为学生制定学习 '{target_concept}' 的路径...")
        
        try:
            result = await self.tutor_agent.plan_learning_path(
                student_info=student,
                target_concept=target_concept,
                thread_id="planning_demo"
            )
            
            print("路径规划完成！")
            print(f"规划结果: {result}")
            
        except Exception as e:
            print(f"路径规划过程中发生错误: {e}")
            logger.error(f"路径规划演示失败: {e}")
    
    async def run_demo(self, demo_type: str = "chat", clear_existing: bool = False, load_sample_data_flag: bool = False):
        """运行演示"""

        await self.initialize(clear_existing=clear_existing, load_sample_data_flag=load_sample_data_flag)

        if demo_type == "chat":
            await self.chat_demo()
        elif demo_type == "diagnostic":
            await self.diagnostic_demo()
        elif demo_type == "planning":
            await self.planning_demo()
        elif demo_type == "all":
            await self.diagnostic_demo()
            await self.planning_demo()
            await self.chat_demo()
        else:
            print(f"未知的演示类型: {demo_type}")
    
    async def close(self):
        """关闭系统"""
        if self.client:
            await self.client.close()
            logger.info("系统已关闭")

def main():
    """主函数 - 同步包装器"""
    asyncio.run(async_main())

async def async_main():
    """异步主函数"""

    import argparse

    parser = argparse.ArgumentParser(description="多智能体教育辅导系统")
    parser.add_argument(
        "--demo",
        choices=["chat", "diagnostic", "planning", "all"],
        default="chat",
        help="演示类型"
    )
    parser.add_argument(
        "--clear",
        action="store_true",
        help="清除现有数据重新初始化"
    )
    parser.add_argument(
        "--load-sample-data",
        action="store_true",
        help="加载示例数据（默认不加载）"
    )

    args = parser.parse_args()

    system = TutorSystem()

    try:
        if args.clear:
            print("警告：将清除现有数据！")
            confirm = input("确认继续？(y/N): ")
            if confirm.lower() != 'y':
                print("操作已取消")
                return

        await system.run_demo(
            demo_type=args.demo,
            clear_existing=args.clear,
            load_sample_data_flag=args.load_sample_data
        )

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"程序执行失败: {e}")
    finally:
        await system.close()

if __name__ == "__main__":
    main()
