#!/usr/bin/env python3
"""
测试独立概念路径修复
验证生成的学习路径中不再包含 independent_concepts 路径
"""

import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_independent_path_removal():
    """测试独立概念路径是否被正确移除"""
    logger.info("测试独立概念路径移除...")
    
    # 模拟 _generate_final_learning_plan 的修改后逻辑
    def simulate_generate_final_learning_plan(optimized_plan, independent_weak_concepts):
        """模拟修改后的 _generate_final_learning_plan 方法"""
        
        # 构建详细的学习路径
        detailed_paths = []
        
        # 处理路径型学习计划
        for i, path_info in enumerate(optimized_plan.get('paths', []), 1):
            # 模拟路径步骤
            path_steps = []
            for node in path_info.get('path_nodes', []):
                path_steps.append({
                    'concept_id': node['concept_id'],
                    'concept_name': node['concept_name'],
                    'level': node.get('level', 1),
                    'current_mastery': 0.3,
                    'is_mastered': False,
                    'is_weak': True,
                    'priority': 'high'
                })
            
            detailed_paths.append({
                'path_id': f"path_{i}",
                'path_type': 'sequential',
                'start_concept': path_info.get('start_concept'),
                'target_concept': path_info.get('target_concept'),
                'steps': path_steps,
                'estimated_time': len(path_steps) * 25
            })
        
        # 独立知识点不作为路径处理，而是单独返回在 independent_weak_concepts 字段中
        
        # 构建最终学习计划
        learning_plan = {
            'student_id': 'test_student',
            'plan_type': 'multi_source_multi_sink_optimal',
            'message': '已为您生成最优的多路径学习计划，总学习量最小化。',
            'learning_paths': detailed_paths,
            'total_concepts_to_learn': optimized_plan.get('total_concepts', 0),
            'estimated_time': optimized_plan.get('estimated_time', 0),
            'independent_weak_concepts': independent_weak_concepts,
            'optimization_info': {
                'algorithm': 'greedy_path_selection',
                'total_paths': len(detailed_paths),
                'independent_concepts': len(independent_weak_concepts)
            }
        }
        
        return learning_plan
    
    # 创建测试数据
    optimized_plan = {
        'paths': [
            {
                'start_concept': '概念A',
                'target_concept': '概念B',
                'path_nodes': [
                    {'concept_id': '概念A', 'concept_name': '概念A', 'level': 1},
                    {'concept_id': '概念B', 'concept_name': '概念B', 'level': 2}
                ]
            },
            {
                'start_concept': '概念C',
                'target_concept': '概念D',
                'path_nodes': [
                    {'concept_id': '概念C', 'concept_name': '概念C', 'level': 1},
                    {'concept_id': '概念D', 'concept_name': '概念D', 'level': 2}
                ]
            }
        ],
        'total_concepts': 6,
        'estimated_time': 150
    }
    
    independent_weak_concepts = [
        {
            'concept_id': '独立概念1',
            'concept_name': '独立概念1',
            'level': 1,
            'path_type': 'independent'
        },
        {
            'concept_id': '独立概念2',
            'concept_name': '独立概念2',
            'level': 1,
            'path_type': 'independent'
        }
    ]
    
    # 执行测试
    result = simulate_generate_final_learning_plan(optimized_plan, independent_weak_concepts)
    
    logger.info("生成的学习计划:")
    logger.info(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 验证结果
    learning_paths = result.get('learning_paths', [])
    
    # 检查是否还有 independent_concepts 路径
    independent_path_found = False
    for path in learning_paths:
        if path.get('path_id') == 'independent_concepts':
            independent_path_found = True
            break
    
    if independent_path_found:
        logger.error("❌ 发现 independent_concepts 路径仍在 learning_paths 中")
        return False
    else:
        logger.info("✅ independent_concepts 路径已被正确移除")
    
    # 检查独立概念是否正确返回
    returned_independent = result.get('independent_weak_concepts', [])
    if len(returned_independent) != len(independent_weak_concepts):
        logger.error(f"❌ 独立概念数量不匹配: 期望 {len(independent_weak_concepts)}, 实际 {len(returned_independent)}")
        return False
    
    logger.info("✅ 独立概念正确返回在 independent_weak_concepts 字段中")
    
    # 检查学习路径数量
    expected_paths = len(optimized_plan['paths'])
    actual_paths = len(learning_paths)
    
    if actual_paths != expected_paths:
        logger.error(f"❌ 学习路径数量不匹配: 期望 {expected_paths}, 实际 {actual_paths}")
        return False
    
    logger.info(f"✅ 学习路径数量正确: {actual_paths} 条")
    
    # 检查路径ID
    path_ids = [path.get('path_id') for path in learning_paths]
    expected_ids = [f'path_{i}' for i in range(1, expected_paths + 1)]
    
    if path_ids != expected_ids:
        logger.error(f"❌ 路径ID不匹配: 期望 {expected_ids}, 实际 {path_ids}")
        return False
    
    logger.info(f"✅ 路径ID正确: {path_ids}")
    
    # 检查优化信息
    opt_info = result.get('optimization_info', {})
    if opt_info.get('total_paths') != expected_paths:
        logger.error(f"❌ 优化信息中的路径数量不正确")
        return False
    
    if opt_info.get('independent_concepts') != len(independent_weak_concepts):
        logger.error(f"❌ 优化信息中的独立概念数量不正确")
        return False
    
    logger.info("✅ 优化信息正确")
    
    logger.info(f"✅ 最终结果: {actual_paths} 条学习路径, {len(returned_independent)} 个独立概念")
    logger.info(f"✅ 学习路径: {path_ids}")
    logger.info(f"✅ 独立概念: {[c['concept_id'] for c in returned_independent]}")
    
    return True

def main():
    """主函数"""
    try:
        success = test_independent_path_removal()
        if success:
            print("\n✅ 独立概念路径移除测试通过")
            print("learning_paths 中不再包含 independent_concepts 路径")
            print("独立概念正确返回在 independent_weak_concepts 字段中")
        else:
            print("\n❌ 独立概念路径移除测试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        print("\n❌ 独立概念路径移除测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
