# 学生做题记录导入系统

## 概述

本系统提供了完整的学生做题记录导入功能，支持从题目数据自动生成学生记录并导入到Neo4j数据库中。

## 系统组件

### 1. 数据生成器 (`generate_student_records.py`)
- 从题目数据文件生成符合格式的学生做题记录
- 使用IRT模型模拟真实的学生答题行为
- 支持自定义学生数量和答题数量

### 2. 数据导入器 (`import_student_data.py`)
- 将学生记录导入到Neo4j数据库
- 支持批量导入优化
- 支持直接从题目数据生成并导入

## 数据库配置

系统使用以下Neo4j连接配置：
```
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=12345678
```

## 使用方法

### 方法一：直接从题目数据生成并导入

这是最简单的方法，一步完成数据生成和导入：

```bash
python scripts/import_student_data.py --generate-from-problems data/start_0_end_200_new_data.json --num-students 50
```

参数说明：
- `--generate-from-problems`: 题目数据文件路径
- `--num-students`: 生成的学生数量（默认50）

### 方法二：分步操作

#### 步骤1：生成学生记录文件

```bash
python scripts/generate_student_records.py \
  --problems-file data/start_0_end_200_new_data.json \
  --output-file data/my_student_data.json \
  --num-students 100 \
  --min-attempts 15 \
  --max-attempts 25
```

参数说明：
- `--problems-file`: 题目数据文件路径
- `--output-file`: 输出的学生数据文件路径
- `--num-students`: 生成的学生数量
- `--min-attempts`: 每个学生最少答题数
- `--max-attempts`: 每个学生最多答题数

#### 步骤2：导入到数据库

```bash
python scripts/import_student_data.py --file data/my_student_data.json
```

### 方法三：使用现有学生数据文件

如果你已经有符合格式的学生数据文件：

```bash
python scripts/import_student_data.py --file data/student_data.json
```

## 数据格式

### 输入：题目数据格式 (`start_0_end_200_new_data.json`)

```json
[
  {
    "question_id": 10200942,
    "content": "题目内容...",
    "correct_answer": "C",
    "difficulty": 7.3,
    "linked_kp_ids": ["力学/运动学/相对运动概念"],
    "misconception_map": {
      "A": "易错点描述",
      "B": "易错点描述"
    }
  }
]
```

### 输出：学生数据格式 (`student_data.json`)

```json
[
  {
    "student_id": "student_001",
    "name": "张三",
    "problem_attempts": [
      {
        "problem_id": 10200942,
        "selected_options": ["A"],
        "is_fully_correct": false,
        "timestamp": "2025-01-15T10:30:00Z"
      }
    ]
  }
]
```

## 数据库结构

导入后在Neo4j中创建以下结构：

### 节点类型
- `:Student` - 学生节点
  - `studentId`: 学生唯一标识
  - `name`: 学生姓名
  - `createdAt`: 创建时间

- `:Problem` - 题目节点（如果不存在会自动创建）
  - `problemId`: 题目ID

### 关系类型
- `(Student)-[:ATTEMPTED]->(Problem)` - 答题记录
  - `selectedOptions`: 选择的选项
  - `isFullyCorrect`: 是否完全正确
  - `timestamp`: 答题时间
  - `difficulty`: 题目难度

## 生成算法说明

### 学生能力模型
- 使用正态分布生成学生能力值（均值0.6，标准差0.2）
- 能力值范围：0.1-0.9

### 答题概率模型
- 使用简化的IRT模型计算答对概率
- 公式：`P = 1 / (1 + exp(-3 * (ability - difficulty)))`
- 难度从0-10映射到0-1

### 答案选择策略
- 答对时选择正确答案
- 答错时优先从易错点选项中选择
- 如果没有易错点，随机选择错误选项

## 示例用法

### 生成小规模测试数据
```bash
python scripts/import_student_data.py \
  --generate-from-problems data/start_0_end_200_new_data.json \
  --num-students 10
```

### 生成大规模数据集
```bash
python scripts/generate_student_records.py \
  --problems-file data/start_0_end_200_new_data.json \
  --output-file data/large_student_dataset.json \
  --num-students 1000 \
  --min-attempts 20 \
  --max-attempts 50

python scripts/import_student_data.py --file data/large_student_dataset.json
```

### 生成示例数据文件
```bash
python scripts/import_student_data.py --sample
```

## 性能优化

- 使用批量导入减少数据库连接开销
- 支持大规模数据集导入
- 自动处理重复数据（使用MERGE操作）

## 错误处理

- 自动验证输入文件格式
- 详细的错误日志记录
- 数据库连接失败自动重试
- 部分失败时继续处理其他数据

## 注意事项

1. **数据库连接**：确保Neo4j服务正在运行且连接参数正确
2. **题目数据**：确保题目数据文件中的题目ID在知识图谱中存在
3. **内存使用**：大规模数据生成时注意内存使用情况
4. **时间戳格式**：使用ISO 8601格式的UTC时间

## 故障排除

### 常见问题

1. **连接失败**
   ```
   错误：无法连接到Neo4j数据库
   解决：检查Neo4j服务状态和连接参数
   ```

2. **题目不存在**
   ```
   错误：题目ID在数据库中不存在
   解决：先导入题目数据，再导入学生记录
   ```

3. **内存不足**
   ```
   错误：生成大量数据时内存不足
   解决：减少学生数量或分批处理
   ```

### 日志查看

系统会输出详细的日志信息，包括：
- 数据加载进度
- 生成统计信息
- 导入成功/失败状态
- 性能统计

## 扩展功能

系统支持以下扩展：
- 自定义学生能力分布
- 不同的答题行为模型
- 时间序列分析
- 学习进度模拟
