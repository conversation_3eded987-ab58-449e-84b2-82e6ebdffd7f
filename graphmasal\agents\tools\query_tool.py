"""
查询工具模块
检索相关知识点、题目和学习资源
"""
from typing import List, Dict, Any, Optional
from langchain_core.tools import tool
from graphiti_core import Graphiti
import logging
import json

logger = logging.getLogger(__name__)

class QueryTool:
    """知识查询工具类"""
    
    def __init__(self, client: Graphiti):
        self.client = client
    
    async def search_concepts(
        self, 
        query: str, 
        student_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索相关知识点"""
        
        # 使用Graphiti的搜索功能
        try:
            search_results = await self.client.search(
                query=query,
                num_results=limit
            )
            
            concepts = []
            for edge in search_results:
                # 解析搜索结果，提取概念信息
                if 'Concept' in edge.fact:
                    concepts.append({
                        'fact': edge.fact,
                        'relevance_score': edge.score if hasattr(edge, 'score') else 1.0
                    })
            
            # 如果提供了学生ID，添加个性化信息
            if student_id:
                concepts = await self._add_personalization(concepts, student_id)
            
            return concepts
            
        except Exception as e:
            logger.error(f"搜索概念失败: {e}")
            return []
    
    async def get_concept_details(self, concept_id: str) -> Dict[str, Any]:
        """获取概念详细信息"""
        
        query = """
        MATCH (concept:Concept {kpId: $concept_id})
        OPTIONAL MATCH (concept)<-[:HAS_SUB_CONCEPT]-(parent:Concept)
        OPTIONAL MATCH (concept)-[:HAS_SUB_CONCEPT]->(child:Concept)
        OPTIONAL MATCH (concept)<-[:IS_PREREQUISITE_FOR]-(prerequisite:Concept)
        OPTIONAL MATCH (concept)-[:IS_PREREQUISITE_FOR]->(dependent:Concept)
        RETURN concept,
               collect(DISTINCT parent.kpId) as parents,
               collect(DISTINCT child.kpId) as children,
               collect(DISTINCT prerequisite.kpId) as prerequisites,
               collect(DISTINCT dependent.kpId) as dependents
        """
        
        try:
            result = await self.client.driver.execute_query(
                query, concept_id=concept_id
            )
            
            if result.records:
                record = result.records[0]
                concept = record['concept']
                
                return {
                    'concept_id': concept['kpId'],
                    'name': concept['name'],
                    'level': concept.get('level', 0),
                    'parents': [p for p in record['parents'] if p],
                    'children': [c for c in record['children'] if c],
                    'prerequisites': [p for p in record['prerequisites'] if p],
                    'dependents': [d for d in record['dependents'] if d]
                }
            else:
                return {}
                
        except Exception as e:
            logger.error(f"获取概念详情失败: {e}")
            return {}
    
    async def find_problems_by_concept(
        self, 
        concept_id: str, 
        difficulty_range: Optional[tuple] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """根据概念查找相关题目"""
        
        query = """
        MATCH (concept:Concept {kpId: $concept_id})<-[:TESTS_CONCEPT]-(problem:Problem)
        """
        
        # 添加难度筛选条件
        if difficulty_range:
            query += " WHERE problem.difficulty >= $min_difficulty AND problem.difficulty <= $max_difficulty"
        
        query += """
        RETURN problem.problemId as problem_id,
               problem.content as content,
               problem.difficulty as difficulty,
               problem.correctAnswer as correct_answer
        ORDER BY problem.difficulty ASC
        LIMIT $limit
        """
        
        try:
            params = {
                'concept_id': concept_id,
                'limit': limit
            }
            
            if difficulty_range:
                params['min_difficulty'] = difficulty_range[0]
                params['max_difficulty'] = difficulty_range[1]
            
            result = await self.client.driver.execute_query(query, **params)
            
            problems = []
            for record in result.records:
                problems.append({
                    'problem_id': record['problem_id'],
                    'content': record['content'],
                    'difficulty': record['difficulty'],
                    'correct_answer': record['correct_answer']
                })
            
            return problems
            
        except Exception as e:
            logger.error(f"查找题目失败: {e}")
            return []
    
    async def get_student_progress(self, student_id: str) -> Dict[str, Any]:
        """获取学生学习进度"""
        
        # 获取掌握度信息
        mastery_query = """
        MATCH (student:Student {studentId: $student_id})-[mastery:HAS_MASTERY_OF]->(concept:Concept)
        RETURN concept.kpId as concept_id,
               concept.name as concept_name,
               mastery.proficiency as proficiency
        ORDER BY mastery.proficiency DESC
        """
        
        # 获取易错点信息
        misconception_query = """
        MATCH (student:Student {studentId: $student_id})-[exhibits:EXHIBITS_MISCONCEPTION]->(misconception:Misconception)
        RETURN misconception.misconceptionId as misconception_id,
               misconception.description as description,
               exhibits.strength as strength
        ORDER BY exhibits.strength DESC
        LIMIT 5
        """
        
        # 获取答题历史
        attempt_query = """
        MATCH (student:Student {studentId: $student_id})-[attempt:ATTEMPTED]->(problem:Problem)
        RETURN count(*) as total_attempts,
               sum(CASE WHEN attempt.isFullyCorrect THEN 1 ELSE 0 END) as correct_attempts
        """
        
        try:
            # 执行查询
            mastery_result = await self.client.driver.execute_query(
                mastery_query, student_id=student_id
            )
            misconception_result = await self.client.driver.execute_query(
                misconception_query, student_id=student_id
            )
            attempt_result = await self.client.driver.execute_query(
                attempt_query, student_id=student_id
            )
            
            # 处理掌握度数据
            mastery_data = []
            for record in mastery_result.records:
                mastery_data.append({
                    'concept_id': record['concept_id'],
                    'concept_name': record['concept_name'],
                    'proficiency': record['proficiency']
                })
            
            # 处理易错点数据
            misconception_data = []
            for record in misconception_result.records:
                misconception_data.append({
                    'misconception_id': record['misconception_id'],
                    'description': record['description'],
                    'strength': record['strength']
                })
            
            # 处理答题统计
            attempt_stats = {}
            if attempt_result.records:
                record = attempt_result.records[0]
                total = record['total_attempts'] or 0
                correct = record['correct_attempts'] or 0
                attempt_stats = {
                    'total_attempts': total,
                    'correct_attempts': correct,
                    'accuracy_rate': correct / total if total > 0 else 0.0
                }
            
            return {
                'student_id': student_id,
                'mastery_data': mastery_data,
                'misconceptions': misconception_data,
                'attempt_statistics': attempt_stats,
                'overall_progress': self._calculate_overall_progress(mastery_data)
            }
            
        except Exception as e:
            logger.error(f"获取学生进度失败: {e}")
            return {}
    
    async def recommend_next_problems(
        self, 
        student_id: str, 
        limit: int = 3
    ) -> List[Dict[str, Any]]:
        """为学生推荐下一批练习题目"""
        
        # 获取学生薄弱的概念
        weak_concepts_query = """
        MATCH (student:Student {studentId: $student_id})-[mastery:HAS_MASTERY_OF]->(concept:Concept)
        WHERE mastery.proficiency < 0.7
        RETURN concept.kpId as concept_id, mastery.proficiency as proficiency
        ORDER BY mastery.proficiency ASC
        LIMIT 3
        """
        
        try:
            result = await self.client.driver.execute_query(
                weak_concepts_query, student_id=student_id
            )
            
            recommendations = []
            
            for record in result.records:
                concept_id = record['concept_id']
                proficiency = record['proficiency']
                
                # 根据掌握度选择合适难度的题目
                if proficiency < 0.3:
                    difficulty_range = (1.0, 4.0)  # 简单题目
                elif proficiency < 0.6:
                    difficulty_range = (3.0, 6.0)  # 中等题目
                else:
                    difficulty_range = (5.0, 8.0)  # 较难题目
                
                # 查找相关题目
                problems = await self.find_problems_by_concept(
                    concept_id, difficulty_range, limit=2
                )
                
                for problem in problems:
                    problem['recommended_reason'] = f"针对薄弱概念: {concept_id}"
                    problem['target_concept'] = concept_id
                    recommendations.append(problem)
            
            return recommendations[:limit]
            
        except Exception as e:
            logger.error(f"推荐题目失败: {e}")
            return []
    
    async def _add_personalization(
        self, 
        concepts: List[Dict[str, Any]], 
        student_id: str
    ) -> List[Dict[str, Any]]:
        """为概念搜索结果添加个性化信息"""
        
        # 获取学生对这些概念的掌握情况
        # 这里简化实现，实际可以更复杂
        for concept in concepts:
            # 从fact中提取概念ID（简化处理）
            concept['personalized'] = True
            concept['student_mastery'] = 'unknown'
        
        return concepts
    
    def _calculate_overall_progress(self, mastery_data: List[Dict[str, Any]]) -> float:
        """计算整体学习进度"""
        
        if not mastery_data:
            return 0.0
        
        total_proficiency = sum(item['proficiency'] for item in mastery_data)
        return round(total_proficiency / len(mastery_data), 2)

# LangChain工具装饰器
@tool
async def search_knowledge(
    query: str,
    student_id: Optional[str] = None,
    search_type: str = "concepts"  # "concepts", "problems", "progress"
) -> str:
    """
    搜索知识图谱中的相关信息
    
    Args:
        query: 搜索查询
        student_id: 学生ID（可选，用于个性化）
        search_type: 搜索类型
    
    Returns:
        搜索结果的JSON字符串
    """
    try:
        # 从客户端管理器获取Graphiti客户端
        from graphmasal.agents.client_manager import get_graphiti_client, is_graphiti_client_initialized

        logger.debug(f"尝试获取Graphiti客户端，客户端初始化状态: {is_graphiti_client_initialized()}")

        if not is_graphiti_client_initialized():
            logger.error("Graphiti客户端未初始化")
            raise RuntimeError("Graphiti客户端未初始化")

        try:
            client = get_graphiti_client()
            logger.debug(f"成功获取Graphiti客户端: {client is not None}")
        except RuntimeError as e:
            logger.error(f"获取Graphiti客户端失败: {e}")
            raise RuntimeError("Graphiti客户端未初始化")
        
        query_tool = QueryTool(client)
        
        if search_type == "concepts":
            result = await query_tool.search_concepts(query, student_id)
        elif search_type == "problems":
            # 假设query是概念ID
            result = await query_tool.find_problems_by_concept(query)
        elif search_type == "progress" and student_id:
            result = await query_tool.get_student_progress(student_id)
        else:
            result = {"error": "不支持的搜索类型"}
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"知识搜索工具执行失败: {e}")
        return json.dumps({
            'error': f'搜索失败: {str(e)}',
            'results': []
        }, ensure_ascii=False)

@tool
async def recommend_problems(student_id: str, limit: int = 3) -> str:
    """
    为学生推荐练习题目
    
    Args:
        student_id: 学生ID
        limit: 推荐题目数量限制
    
    Returns:
        推荐题目的JSON字符串
    """
    try:
        # 从客户端管理器获取Graphiti客户端
        from graphmasal.agents.client_manager import get_graphiti_client, is_graphiti_client_initialized

        logger.debug(f"尝试获取Graphiti客户端，客户端初始化状态: {is_graphiti_client_initialized()}")

        if not is_graphiti_client_initialized():
            logger.error("Graphiti客户端未初始化")
            raise RuntimeError("Graphiti客户端未初始化")

        try:
            client = get_graphiti_client()
            logger.debug(f"成功获取Graphiti客户端: {client is not None}")
        except RuntimeError as e:
            logger.error(f"获取Graphiti客户端失败: {e}")
            raise RuntimeError("Graphiti客户端未初始化")
        
        query_tool = QueryTool(client)
        result = await query_tool.recommend_next_problems(student_id, limit)
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"题目推荐工具执行失败: {e}")
        return json.dumps({
            'error': f'推荐失败: {str(e)}',
            'recommendations': []
        }, ensure_ascii=False)
