# 多智能体教育辅导系统

基于 Graphiti 和 LangGraph 构建的智能教育辅导系统，采用层次化多智能体架构，提供个性化学习诊断、路径规划和智能辅导功能。

## 🏗️ 系统架构

```
用户端 ←→ 辅导智能体 ←→ 诊断智能体
                ↓           规划智能体
        动态知识图谱(Graphiti) ←→ Neo4j数据库
```

### 核心组件

- **辅导智能体**: 主智能体，负责与用户交互和协调其他智能体
- **诊断智能体**: 分析学生答题错误，识别易错点
- **规划智能体**: 基于学生状态生成个性化学习路径
- **知识图谱**: 使用 Graphiti 管理教育知识和学生数据

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Neo4j 数据库
- OpenAI API Key

### 安装依赖

```bash
pip install graphiti-core langchain-openai langgraph python-dotenv
```

### 配置环境

1. 复制环境配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入必要的配置：

**OpenAI 官方 API：**
```env
# Neo4j 数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini
OPENAI_BASE_URL=https://api.openai.com/v1/
```

**豆包 API：**
```env
# Neo4j 数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# 豆包 API 配置
OPENAI_API_KEY=your_doubao_api_key
OPENAI_MODEL=doubao-seed-1.6-250615
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/
```

### 运行系统

```bash
# 运行聊天演示
graphmasal --demo chat

# 运行诊断演示  
graphmasal --demo diagnostic

# 运行学习路径规划演示
graphmasal --demo planning

# 运行系统测试
graphmasal-test

# 运行所有演示
python main.py --demo all

# 清除数据重新初始化
python main.py --clear
```

### 运行测试

```bash
python test_system.py
```

## 📚 功能特性

### 1. 智能对话辅导
- 自然语言交互
- 上下文感知回复
- 个性化学习建议

### 2. 答题诊断分析
- 错误选项分析
- 易错点识别
- 知识点掌握度评估

### 3. 学习路径规划
- 基于前置依赖的路径生成
- 个性化难度调整
- 优先纠错任务安排

### 4. 知识图谱管理
- 动态知识结构
- 学生学习状态跟踪
- 智能内容推荐

## 🗂️ 项目结构

```
graphmasal/
├── main.py                    # 主程序入口
├── test_system.py            # 系统测试
├── config/
│   ├── __init__.py
│   └── settings.py           # 配置管理
├── agents/
│   ├── __init__.py
│   ├── tutor_agent.py        # 主辅导智能体
│   └── tools/
│       ├── __init__.py
│       ├── diagnostic_tool.py # 诊断工具
│       ├── planning_tool.py   # 规划工具
│       └── query_tool.py      # 查询工具
├── knowledge_graph/
│   ├── __init__.py
│   ├── schema_init.py        # 知识图谱初始化
│   └── data_loader.py        # 数据加载器
├── models/
│   ├── __init__.py
│   └── state.py              # 状态定义
└── utils/
    ├── __init__.py
    └── helpers.py            # 辅助函数
```

## 🔧 API 使用示例

### 基本对话

```python
from agents.tutor_agent import TutorAgent
from models.state import StudentInfo

# 创建学生信息
student = StudentInfo(
    student_id="student_001",
    name="张三",
    current_subject="物理"
)

# 初始化智能体
tutor = TutorAgent(graphiti_client)

# 进行对话
response = await tutor.chat(
    message="什么是牛顿第二定律？",
    student_info=student,
    thread_id="session_1"
)
```

### 答题诊断

```python
# 题目数据
problem_data = {
    "problem_id": 10200942,
    "selected_options": ["A"],
    "correct_answer": ["C"],
    "misconception_map": {
        "A": "未考虑容器加速度对等效重力的影响"
    }
}

# 执行诊断
result = await tutor.diagnose_problem(
    student_info=student,
    problem_data=problem_data,
    thread_id="diagnostic_session"
)
```

### 学习路径规划

```python
# 生成学习路径
result = await tutor.plan_learning_path(
    student_info=student,
    target_concept="力学/相互作用与牛顿定律/牛顿第二定律应用",
    thread_id="planning_session"
)
```

## 📊 知识图谱结构

### 节点类型

- **Subject**: 学科节点 (如：物理、数学)
- **Concept**: 知识点节点 (如：牛顿第二定律)
- **Problem**: 题目节点
- **Misconception**: 易错点节点
- **Student**: 学生节点

### 关系类型

- **HAS_SUB_CONCEPT**: 包含子概念
- **IS_PREREQUISITE_FOR**: 前置依赖关系
- **TESTS_CONCEPT**: 题目考察概念
- **OPTION_TARGETS**: 选项对应易错点
- **ATTEMPTED**: 学生答题记录
- **HAS_MASTERY_OF**: 学生掌握度
- **EXHIBITS_MISCONCEPTION**: 学生易错点

## 🛠️ 开发指南

### 添加新工具

1. 在 `agents/tools/` 目录下创建新工具文件
2. 实现工具类和 LangChain 工具装饰器
3. 在 `tutor_agent.py` 中注册新工具

### 扩展知识图谱

1. 在 `schema_init.py` 中添加新的节点类型和关系
2. 在 `data_loader.py` 中实现数据加载逻辑
3. 更新相关工具以支持新的数据结构

### 自定义学习算法

1. 在 `planning_tool.py` 中修改路径规划算法
2. 在 `diagnostic_tool.py` 中调整诊断逻辑
3. 根据需要添加新的评估指标

## 🔍 故障排除

### 常见问题

1. **Neo4j 连接失败**
   - 检查 Neo4j 服务是否运行
   - 验证连接配置是否正确

2. **OpenAI API 错误**
   - 确认 API Key 有效
   - 检查网络连接和配额

3. **内存不足**
   - 调整批处理大小
   - 优化查询复杂度

### 日志查看

系统日志保存在 `tutor_system.log` 文件中，可以通过以下方式查看：

```bash
tail -f tutor_system.log
```

## 📈 性能优化

- 使用索引优化图数据库查询
- 实现查询结果缓存
- 批量处理数据操作
- 异步执行非关键任务

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 参与讨论

---

**注意**: 本系统仍在开发中，部分功能可能需要进一步完善。建议在生产环境使用前进行充分测试。
