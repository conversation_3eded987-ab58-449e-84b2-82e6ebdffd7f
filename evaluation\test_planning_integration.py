#!/usr/bin/env python3
"""
测试路径规划集成
验证重写后的 run_agent_planning 方法是否正常工作
"""

import asyncio
import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent))

# 直接测试相似度计算，避免复杂的导入
def create_simple_evaluator():
    """创建简单的评测器，只测试相似度计算部分"""
    try:
        from evaluators.path_similarity_evaluator import PathSimilarityEvaluator
        return PathSimilarityEvaluator(
            node_weight=0.4,
            edge_weight=0.3,
            sequence_weight=0.3,
            paths_weight=0.7,
            points_weight=0.3
        )
    except ImportError:
        from evaluation.evaluators.path_similarity_evaluator import PathSimilarityEvaluator
        return PathSimilarityEvaluator(
            node_weight=0.4,
            edge_weight=0.3,
            sequence_weight=0.3,
            paths_weight=0.7,
            points_weight=0.3
        )

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_mock_diagnostic_result():
    """创建模拟的诊断结果"""
    return {
        'evaluation_metadata': {
            'student_id': 'test_student_001',
            'timestamp': '2024-01-01T10:00:00',
            'generator_version': 'test_1.0.0'
        },
        'diagnostic_result': {
            'mastered_concepts': [
                {
                    'concept_id': 'concept_001',
                    'concept_name': '基础概念1',
                    'mastery_level': 0.8,
                    'problem_count': 2
                },
                {
                    'concept_id': 'concept_002', 
                    'concept_name': '基础概念2',
                    'mastery_level': 0.5,  # 薄弱概念
                    'problem_count': 1
                }
            ],
            'not_mastered_concepts': [
                {
                    'concept_id': 'concept_003',
                    'concept_name': '未掌握概念1',
                    'mastery_level': 0.2,
                    'problem_count': 3
                },
                {
                    'concept_id': 'concept_004',
                    'concept_name': '未掌握概念2', 
                    'mastery_level': 0.1,
                    'problem_count': 2
                }
            ]
        }
    }

def create_mock_ground_truth():
    """创建模拟的标准答案"""
    return {
        'evaluation_metadata': {
            'student_id': 'test_student_001',
            'timestamp': '2024-01-01T10:00:00'
        },
        'planning_result': {
            'paths_subset': [
                {
                    'path_id': 'path_1',
                    'path_type': 'sequential',
                    'concepts': ['concept_001', 'concept_003'],
                    'steps': [
                        {'concept_id': 'concept_001', 'concept_name': '基础概念1'},
                        {'concept_id': 'concept_003', 'concept_name': '未掌握概念1'}
                    ]
                }
            ],
            'points_subset': ['concept_004']
        }
    }

async def test_planning_integration():
    """测试路径规划集成"""
    logger.info("开始测试路径规划集成...")

    try:
        # 创建评测器实例
        evaluator = create_simple_evaluator()
        
        # 创建模拟数据
        diagnostic_result = create_mock_diagnostic_result()
        ground_truth = create_mock_ground_truth()
        
        logger.info("创建的诊断结果:")
        logger.info(json.dumps(diagnostic_result, ensure_ascii=False, indent=2))
        
        # 测试路径规划方法（不实际调用AI，因为没有完整的环境）
        logger.info("测试路径相似度计算...")
        
        # 创建模拟的规划结果
        mock_planning_result = {
            'evaluation_metadata': {
                'student_id': 'test_student_001',
                'timestamp': '2024-01-01T10:00:00',
                'generator_version': 'ai_planning_2.0.0'
            },
            'planning_result': {
                'paths_subset': [
                    {
                        'path_id': 'path_1',
                        'path_type': 'sequential',
                        'concepts': ['concept_002', 'concept_003'],  # 略有不同
                        'steps': [
                            {'concept_id': 'concept_002', 'concept_name': '基础概念2'},
                            {'concept_id': 'concept_003', 'concept_name': '未掌握概念1'}
                        ]
                    }
                ],
                'points_subset': ['concept_004']  # 相同
            }
        }
        
        # 测试相似度计算
        result = evaluator._compare_single_planning(mock_planning_result, ground_truth, 0)
        similarity = result.get('total_similarity', 0.0)
        logger.info(f"路径相似度: {similarity:.3f}")
        
        # 验证数据结构
        logger.info("验证数据结构...")
        
        predicted_paths = mock_planning_result['planning_result']['paths_subset']
        predicted_points = mock_planning_result['planning_result']['points_subset']
        
        truth_paths = ground_truth['planning_result']['paths_subset']
        truth_points = ground_truth['planning_result']['points_subset']
        
        logger.info(f"预测路径数: {len(predicted_paths)}")
        logger.info(f"预测独立点数: {len(predicted_points)}")
        logger.info(f"真实路径数: {len(truth_paths)}")
        logger.info(f"真实独立点数: {len(truth_points)}")
        
        # 检查路径结构
        for i, path in enumerate(predicted_paths):
            logger.info(f"预测路径 {i+1}: {path.get('concepts', [])}")
        
        for i, path in enumerate(truth_paths):
            logger.info(f"真实路径 {i+1}: {path.get('concepts', [])}")
        
        logger.info("测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    success = await test_planning_integration()
    if success:
        print("\n✅ 路径规划集成测试通过")
    else:
        print("\n❌ 路径规划集成测试失败")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
