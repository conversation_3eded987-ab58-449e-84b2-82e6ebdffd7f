"""
知识图谱Schema初始化模块
根据知识图谱结构文档创建Neo4j约束和索引
"""
import asyncio
from typing import List, Dict, Any
from graphiti_core import Graphiti
from graphiti_core.utils.maintenance.graph_data_operations import clear_data
import logging

logger = logging.getLogger(__name__)

class KnowledgeGraphSchema:
    """知识图谱Schema管理类"""
    
    def __init__(self, client: Graphiti):
        self.client = client
        
    async def create_constraints_and_indices(self):
        """创建约束和索引"""
        
        # 节点唯一性约束
        constraints = [
            # Subject节点约束
            "CREATE CONSTRAINT subject_name_unique IF NOT EXISTS FOR (s:Subject) REQUIRE s.name IS UNIQUE",
            
            # Concept节点约束  
            "CREATE CONSTRAINT concept_kpid_unique IF NOT EXISTS FOR (c:Concept) REQUIRE c.kpId IS UNIQUE",
            
            # Problem节点约束
            "CREATE CONSTRAINT problem_id_unique IF NOT EXISTS FOR (p:Problem) REQUIRE p.problemId IS UNIQUE",
            
            # Misconception节点约束
            "CREATE CONSTRAINT misconception_id_unique IF NOT EXISTS FOR (m:Misconception) REQUIRE m.misconceptionId IS UNIQUE",
            
            # Student节点约束
            "CREATE CONSTRAINT student_id_unique IF NOT EXISTS FOR (s:Student) REQUIRE s.studentId IS UNIQUE"
        ]
        
        # 索引创建
        indices = [
            # 概念层级索引
            "CREATE INDEX concept_level_idx IF NOT EXISTS FOR (c:Concept) ON (c.level)",
            
            # 题目难度索引
            "CREATE INDEX problem_difficulty_idx IF NOT EXISTS FOR (p:Problem) ON (p.difficulty)",
            
            # 学生掌握度索引（用于关系属性）
            "CREATE INDEX mastery_proficiency_idx IF NOT EXISTS FOR ()-[r:HAS_MASTERY_OF]-() ON (r.proficiency)",
            
            # 易错点强度索引
            "CREATE INDEX misconception_strength_idx IF NOT EXISTS FOR ()-[r:EXHIBITS_MISCONCEPTION]-() ON (r.strength)"
        ]
        
        # 执行约束创建
        for constraint in constraints:
            try:
                await self.client.driver.execute_query(constraint)
                logger.info(f"约束创建成功: {constraint}")
            except Exception as e:
                logger.warning(f"约束创建失败或已存在: {constraint}, 错误: {e}")
        
        # 执行索引创建
        for index in indices:
            try:
                await self.client.driver.execute_query(index)
                logger.info(f"索引创建成功: {index}")
            except Exception as e:
                logger.warning(f"索引创建失败或已存在: {index}, 错误: {e}")
    
    async def create_sample_knowledge_structure(self):
        """创建示例知识结构"""
        
        # 创建学科节点
        physics_query = """
        MERGE (physics:Subject {name: "物理"})
        RETURN physics
        """
        
        # 创建知识点层级结构
        concepts_query = """
        // 创建力学主概念
        MERGE (mechanics:Concept {
            kpId: "力学", 
            name: "力学", 
            level: 1
        })
        
        // 创建运动学子概念
        MERGE (kinematics:Concept {
            kpId: "力学/运动学", 
            name: "运动学", 
            level: 2
        })
        
        // 创建相对运动概念
        MERGE (relative_motion:Concept {
            kpId: "力学/运动学/相对运动概念", 
            name: "相对运动概念", 
            level: 3
        })
        
        // 创建相互作用与牛顿定律
        MERGE (newton_laws:Concept {
            kpId: "力学/相互作用与牛顿定律", 
            name: "相互作用与牛顿定律", 
            level: 2
        })
        
        // 创建力的概念与合成
        MERGE (force_concept:Concept {
            kpId: "力学/相互作用与牛顿定律/力的概念与合成", 
            name: "力的概念与合成", 
            level: 3
        })
        
        // 创建牛顿第二定律应用
        MERGE (newton_second:Concept {
            kpId: "力学/相互作用与牛顿定律/牛顿第二定律应用", 
            name: "牛顿第二定律应用", 
            level: 3
        })
        
        // 创建加速度概念
        MERGE (acceleration:Concept {
            kpId: "力学/运动学/加速度的概念", 
            name: "加速度的概念", 
            level: 3
        })
        
        // 建立学科与概念关系
        MERGE (physics:Subject {name: "物理"})
        MERGE (physics)-[:HAS_SUB_CONCEPT]->(mechanics)
        
        // 建立概念层级关系
        MERGE (mechanics)-[:HAS_SUB_CONCEPT]->(kinematics)
        MERGE (mechanics)-[:HAS_SUB_CONCEPT]->(newton_laws)
        MERGE (kinematics)-[:HAS_SUB_CONCEPT]->(relative_motion)
        MERGE (kinematics)-[:HAS_SUB_CONCEPT]->(acceleration)
        MERGE (newton_laws)-[:HAS_SUB_CONCEPT]->(force_concept)
        MERGE (newton_laws)-[:HAS_SUB_CONCEPT]->(newton_second)
        
        // 建立前置依赖关系
        MERGE (force_concept)-[:IS_PREREQUISITE_FOR]->(newton_second)
        MERGE (acceleration)-[:IS_PREREQUISITE_FOR]->(newton_second)
        
        RETURN mechanics, kinematics, newton_laws
        """
        
        try:
            await self.client.driver.execute_query(physics_query)
            await self.client.driver.execute_query(concepts_query)
            logger.info("示例知识结构创建成功")
        except Exception as e:
            logger.error(f"示例知识结构创建失败: {e}")
    
    async def initialize_schema(self, clear_existing: bool = False):
        """初始化完整的知识图谱Schema"""
        
        if clear_existing:
            logger.warning("清除现有数据...")
            await clear_data(self.client.driver)
        
        # 创建Graphiti的基础约束和索引
        await self.client.build_indices_and_constraints()
        
        # 创建教育系统特定的约束和索引
        await self.create_constraints_and_indices()
        
        # 创建示例知识结构
        await self.create_sample_knowledge_structure()
        
        logger.info("知识图谱Schema初始化完成")

async def init_knowledge_graph(client: Graphiti, clear_existing: bool = False):
    """初始化知识图谱的便捷函数"""
    schema = KnowledgeGraphSchema(client)
    await schema.initialize_schema(clear_existing)
    return schema
