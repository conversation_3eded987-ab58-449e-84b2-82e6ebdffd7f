"""
数据处理工具
提供数据加载、预处理和格式转换功能
"""

import json
import logging
import os
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = logger
    
    def load_json_data(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        加载JSON数据文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            加载的数据，失败时返回None
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载数据文件: {filepath}")
            return data
            
        except FileNotFoundError:
            self.logger.error(f"文件不存在: {filepath}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {filepath}, 错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"加载数据文件失败: {filepath}, 错误: {e}")
            return None
    
    def save_json_data(self, data: Any, filepath: str) -> bool:
        """
        保存数据到JSON文件
        
        Args:
            data: 要保存的数据
            filepath: 文件路径
            
        Returns:
            保存是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"成功保存数据到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存数据失败: {filepath}, 错误: {e}")
            return False
    
    def load_test_cases(self, test_cases_dir: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        加载测试用例
        
        Args:
            test_cases_dir: 测试用例目录
            
        Returns:
            (诊断测试用例列表, 规划测试用例列表)
        """
        diagnostic_cases = []
        planning_cases = []
        
        try:
            # 加载诊断测试用例
            diagnostic_file = os.path.join(test_cases_dir, 'diagnostic_test_cases.json')
            if os.path.exists(diagnostic_file):
                diagnostic_data = self.load_json_data(diagnostic_file)
                if diagnostic_data:
                    diagnostic_cases = diagnostic_data.get('test_cases', [])
            
            # 加载规划测试用例
            planning_file = os.path.join(test_cases_dir, 'planning_test_cases.json')
            if os.path.exists(planning_file):
                planning_data = self.load_json_data(planning_file)
                if planning_data:
                    planning_cases = planning_data.get('test_cases', [])
            
            self.logger.info(f"加载测试用例完成: 诊断 {len(diagnostic_cases)} 个, 规划 {len(planning_cases)} 个")
            
        except Exception as e:
            self.logger.error(f"加载测试用例失败: {e}")
        
        return diagnostic_cases, planning_cases
    
    def validate_diagnostic_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证诊断数据格式
        
        Args:
            data: 诊断数据
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            # 检查必需字段
            required_fields = ['evaluation_metadata', 'diagnostic_result']
            for field in required_fields:
                if field not in data:
                    errors.append(f"缺少必需字段: {field}")
            
            # 检查元数据
            if 'evaluation_metadata' in data:
                metadata = data['evaluation_metadata']
                if 'student_id' not in metadata:
                    errors.append("元数据中缺少student_id")
                if 'problem_id' not in metadata:
                    errors.append("元数据中缺少problem_id")
            
            # 检查诊断结果
            if 'diagnostic_result' in data:
                result = data['diagnostic_result']
                if 'knowledge_point_mastery' not in result:
                    errors.append("诊断结果中缺少knowledge_point_mastery")
                if 'identified_misconceptions' not in result:
                    errors.append("诊断结果中缺少identified_misconceptions")
            
        except Exception as e:
            errors.append(f"数据验证过程中发生错误: {str(e)}")
        
        return len(errors) == 0, errors
    
    def validate_planning_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证规划数据格式
        
        Args:
            data: 规划数据
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            # 检查必需字段
            required_fields = ['evaluation_metadata', 'planning_result']
            for field in required_fields:
                if field not in data:
                    errors.append(f"缺少必需字段: {field}")
            
            # 检查元数据
            if 'evaluation_metadata' in data:
                metadata = data['evaluation_metadata']
                if 'student_id' not in metadata:
                    errors.append("元数据中缺少student_id")
            
            # 检查规划结果
            if 'planning_result' in data:
                result = data['planning_result']
                if 'paths_subset' not in result:
                    errors.append("规划结果中缺少paths_subset")
                if 'points_subset' not in result:
                    errors.append("规划结果中缺少points_subset")
                
                # 验证路径格式
                paths = result.get('paths_subset', [])
                for i, path in enumerate(paths):
                    if not isinstance(path, dict):
                        errors.append(f"路径 {i} 不是字典格式")
                        continue
                    
                    if 'concepts' not in path:
                        errors.append(f"路径 {i} 缺少concepts字段")
                    if 'nodes' not in path:
                        errors.append(f"路径 {i} 缺少nodes字段")
                    if 'edges' not in path:
                        errors.append(f"路径 {i} 缺少edges字段")
            
        except Exception as e:
            errors.append(f"数据验证过程中发生错误: {str(e)}")
        
        return len(errors) == 0, errors
    
    def batch_validate_data(
        self, 
        data_list: List[Dict[str, Any]], 
        data_type: str
    ) -> Tuple[List[Dict[str, Any]], List[Tuple[int, List[str]]]]:
        """
        批量验证数据
        
        Args:
            data_list: 数据列表
            data_type: 数据类型 ('diagnostic' 或 'planning')
            
        Returns:
            (有效数据列表, 错误信息列表)
        """
        valid_data = []
        errors = []
        
        validate_func = (
            self.validate_diagnostic_data if data_type == 'diagnostic' 
            else self.validate_planning_data
        )
        
        for i, data in enumerate(data_list):
            is_valid, error_list = validate_func(data)
            if is_valid:
                valid_data.append(data)
            else:
                errors.append((i, error_list))
        
        self.logger.info(f"批量验证完成: {len(valid_data)}/{len(data_list)} 个数据有效")
        
        return valid_data, errors
    
    def create_sample_diagnostic_case(
        self, 
        student_id: str, 
        problem_id: int, 
        is_correct: bool,
        knowledge_points: Dict[str, float],
        misconceptions: List[str] = None
    ) -> Dict[str, Any]:
        """
        创建示例诊断测试用例
        
        Args:
            student_id: 学生ID
            problem_id: 题目ID
            is_correct: 是否正确
            knowledge_points: 知识点掌握度字典
            misconceptions: 易错点列表
            
        Returns:
            诊断测试用例
        """
        if misconceptions is None:
            misconceptions = []
        
        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'problem_id': problem_id,
                'timestamp': '2025-01-15T10:00:00Z',
                'evaluator_version': '1.0.0'
            },
            'diagnostic_result': {
                'is_correct': is_correct,
                'overall_accuracy': 1.0 if is_correct else 0.0,
                'knowledge_point_mastery': {
                    kp: {
                        'mastery_level': level,
                        'confidence': 0.8,
                        'status': self._determine_mastery_status(level)
                    }
                    for kp, level in knowledge_points.items()
                },
                'identified_misconceptions': [
                    {
                        'misconception_id': f'misc_{i}',
                        'description': misc,
                        'option': 'A',
                        'strength': 1.0
                    }
                    for i, misc in enumerate(misconceptions)
                ],
                'diagnostic_confidence': 0.8
            }
        }
    
    def create_sample_planning_case(
        self, 
        student_id: str, 
        paths: List[List[str]], 
        independent_points: List[str]
    ) -> Dict[str, Any]:
        """
        创建示例规划测试用例
        
        Args:
            student_id: 学生ID
            paths: 路径列表，每个路径是概念列表
            independent_points: 独立点列表
            
        Returns:
            规划测试用例
        """
        paths_subset = []
        for i, path_concepts in enumerate(paths):
            path_dict = {
                'path_id': f'path_{i}',
                'concepts': path_concepts,
                'nodes': list(set(path_concepts)),
                'edges': [(path_concepts[j], path_concepts[j+1]) for j in range(len(path_concepts)-1)],
                'sequence': path_concepts,
                'estimated_time': len(path_concepts) * 30,
                'priority': 'medium'
            }
            paths_subset.append(path_dict)
        
        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'plan_type': 'optimal',
                'timestamp': '2025-01-15T10:00:00Z',
                'evaluator_version': '1.0.0'
            },
            'planning_result': {
                'paths_subset': paths_subset,
                'points_subset': independent_points,
                'total_concepts_to_learn': len(independent_points) + sum(len(p) for p in paths),
                'estimated_time': len(independent_points) * 20 + sum(len(p) * 30 for p in paths),
                'planning_confidence': 0.8
            }
        }
    
    def _determine_mastery_status(self, mastery_level: float) -> str:
        """根据掌握度确定掌握状态"""
        if mastery_level >= 0.8:
            return 'mastered'
        elif mastery_level >= 0.6:
            return 'partially_mastered'
        elif mastery_level >= 0.3:
            return 'weak'
        else:
            return 'not_mastered'
    
    def generate_sample_test_cases(self, output_dir: str) -> bool:
        """
        生成示例测试用例文件
        
        Args:
            output_dir: 输出目录
            
        Returns:
            生成是否成功
        """
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成诊断测试用例
            diagnostic_cases = []
            diagnostic_ground_truth = []

            for i in range(5):
                case = self.create_sample_diagnostic_case(
                    student_id=f'student_{i+1}',
                    problem_id=10200942 + i,
                    is_correct=i % 2 == 0,
                    knowledge_points={
                        '力学/运动学/相对运动概念': 0.3 + i * 0.15,
                        '力学/相互作用与牛顿定律/力的概念与合成': 0.5 + i * 0.1
                    },
                    misconceptions=['未考虑容器加速度对等效重力的影响'] if i % 2 == 1 else []
                )
                diagnostic_cases.append(case)

                # 创建对应的真实标签
                truth = {
                    'knowledge_point_mastery': {
                        '力学/运动学/相对运动概念': {
                            'mastery_level': 0.3 + i * 0.15,
                            'status': self._determine_mastery_status(0.3 + i * 0.15)
                        },
                        '力学/相互作用与牛顿定律/力的概念与合成': {
                            'mastery_level': 0.5 + i * 0.1,
                            'status': self._determine_mastery_status(0.5 + i * 0.1)
                        }
                    },
                    'identified_misconceptions': [
                        {
                            'misconception_id': 'misc_001',
                            'description': '未考虑容器加速度对等效重力的影响'
                        }
                    ] if i % 2 == 1 else []
                }
                diagnostic_ground_truth.append(truth)

            diagnostic_data = {
                'description': '诊断智能体测试用例',
                'version': '1.0.0',
                'test_cases': diagnostic_cases,
                'ground_truth': diagnostic_ground_truth
            }
            
            # 生成规划测试用例
            planning_cases = []
            planning_ground_truth = []

            for i in range(5):
                paths = [
                    ['概念A', '概念B', '概念C'],
                    ['概念D', '概念E']
                ] if i % 2 == 0 else [['概念X', '概念Y']]

                independent_points = ['独立概念1', '独立概念2'] if i % 2 == 0 else ['独立概念3']

                case = self.create_sample_planning_case(
                    student_id=f'student_{i+1}',
                    paths=paths,
                    independent_points=independent_points
                )
                planning_cases.append(case)

                # 创建对应的真实标签
                truth_paths = []
                for j, path_concepts in enumerate(paths):
                    truth_path = {
                        'path_id': f'truth_path_{j}',
                        'concepts': path_concepts,
                        'nodes': list(set(path_concepts)),
                        'edges': [(path_concepts[k], path_concepts[k+1]) for k in range(len(path_concepts)-1)],
                        'sequence': path_concepts
                    }
                    truth_paths.append(truth_path)

                truth = {
                    'paths_subset': truth_paths,
                    'points_subset': independent_points
                }
                planning_ground_truth.append(truth)

            planning_data = {
                'description': '规划智能体测试用例',
                'version': '1.0.0',
                'test_cases': planning_cases,
                'ground_truth': planning_ground_truth
            }
            
            # 保存文件
            diagnostic_file = os.path.join(output_dir, 'diagnostic_test_cases.json')
            planning_file = os.path.join(output_dir, 'planning_test_cases.json')
            
            success1 = self.save_json_data(diagnostic_data, diagnostic_file)
            success2 = self.save_json_data(planning_data, planning_file)
            
            if success1 and success2:
                self.logger.info(f"示例测试用例已生成到: {output_dir}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成示例测试用例失败: {e}")
            return False
