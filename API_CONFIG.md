# API 配置指南

## 支持的 API 服务

GraphMASAL 支持多种 LLM API 服务，包括：

- OpenAI 官方 API
- 豆包 (Doubao) API
- 其他兼容 OpenAI 格式的 API 服务

## 配置方法

### 1. OpenAI 官方 API

```env
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4o-mini
OPENAI_BASE_URL=https://api.openai.com/v1/
```

### 2. 豆包 (Doubao) API

```env
OPENAI_API_KEY=your-doubao-api-key
OPENAI_MODEL=doubao-seed-1.6-250615
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/

# 豆包 Embedding 配置
EMBEDDING_MODEL=doubao-embedding-text-240715
EMBEDDING_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/
EMBEDDING_API_KEY=your-doubao-api-key
```

### 3. 其他兼容服务

对于其他兼容 OpenAI 格式的 API 服务，只需要修改相应的配置：

```env
OPENAI_API_KEY=your-api-key
OPENAI_MODEL=your-model-name
OPENAI_BASE_URL=https://your-api-endpoint/
```

## 配置步骤

### 步骤 1: 复制配置文件

```bash
cp graphmasal/.env.example .env
```

### 步骤 2: 编辑配置文件

根据你使用的服务，编辑 `.env` 文件中的相应配置项。

**豆包 API 示例配置：**

```env
# Neo4j 数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=12345678

# 豆包 API 配置
OPENAI_API_KEY=ae8821d7-ac1e-486e-9376-557450671195
OPENAI_MODEL=doubao-seed-1.6-250615
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/

# 豆包 Embedding 配置
EMBEDDING_MODEL=doubao-embedding-text-240715
EMBEDDING_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/
EMBEDDING_API_KEY=ae8821d7-ac1e-486e-9376-557450671195

# 系统配置
TEMPERATURE=0.0
```

### 步骤 3: 验证配置

运行以下命令验证配置是否正确：

```bash
python -c "
from graphmasal.config.settings import settings
try:
    settings.validate()
    print('✅ 配置验证通过')
except Exception as e:
    print(f'❌ 配置验证失败: {e}')
"
```

## 常见问题

### 1. API Key 无效

**错误信息：**
```
Error code: 401 - {'error': {'message': 'Incorrect API key provided'}}
```

**解决方法：**
- 检查 API Key 是否正确
- 确认 API Key 是否有效且未过期
- 验证 API Key 是否有相应的权限

### 2. 模型不存在

**错误信息：**
```
Error code: 404 - {'error': {'message': 'The model does not exist'}}
```

**解决方法：**
- 检查模型名称是否正确
- 确认你的 API Key 是否有访问该模型的权限
- 查看服务提供商的模型列表

### 3. Base URL 配置错误

**错误信息：**
```
Connection error or timeout
```

**解决方法：**
- 检查 `OPENAI_BASE_URL` 是否正确
- 确认网络连接正常
- 验证 API 端点是否可访问

### 4. 豆包 API 特殊配置

豆包 API 使用时需要注意：

1. **API Key 格式**: 豆包的 API Key 通常是 UUID 格式
2. **Base URL**: 必须设置为 `https://ark.cn-beijing.volces.com/api/v3/`
3. **模型名称**: 使用豆包提供的具体模型名称，如 `doubao-seed-1.6-250615`

## 测试配置

创建一个简单的测试脚本来验证 API 配置：

```python
# test_api.py
import asyncio
from graphmasal.agents.tutor_agent import TutorAgent
from graphmasal.models.state import StudentInfo
from graphiti_core import Graphiti
from graphmasal.config.settings import settings

async def test_api():
    try:
        # 初始化 Graphiti 客户端
        client = Graphiti(
            settings.NEO4J_URI,
            settings.NEO4J_USER,
            settings.NEO4J_PASSWORD
        )
        
        # 初始化辅导智能体
        tutor = TutorAgent(client)
        
        # 创建测试学生
        student = StudentInfo(
            student_id="test_student",
            name="测试学生"
        )
        
        # 发送测试消息
        response = await tutor.chat(
            message="你好",
            student_info=student,
            thread_id="test"
        )
        
        print(f"✅ API 测试成功！")
        print(f"回复: {response}")
        
        await client.close()
        
    except Exception as e:
        print(f"❌ API 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_api())
```

运行测试：

```bash
python test_api.py
```

## 性能优化建议

### 1. 温度设置

- **创意任务**: `TEMPERATURE=0.7-1.0`
- **分析任务**: `TEMPERATURE=0.1-0.3`
- **精确任务**: `TEMPERATURE=0.0`

### 2. 模型选择

根据任务复杂度选择合适的模型：

- **简单对话**: 使用较小的模型以节省成本
- **复杂推理**: 使用更强大的模型以获得更好的效果

### 3. 请求频率控制

如果遇到频率限制，可以在代码中添加重试机制或降低请求频率。

## 支持

如果在配置过程中遇到问题：

1. 检查本文档的常见问题部分
2. 查看系统日志文件 `tutor_system.log`
3. 提交 Issue 到项目仓库，包含详细的错误信息和配置（注意隐藏敏感信息）
