#!/usr/bin/env python3
"""
测试规划结果格式是否与 plan1.json 一致
"""

import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_plan1_format():
    """加载 plan1.json 的格式作为参考"""
    plan1_path = Path(__file__).parent / "ground_truth" / "plan1.json"
    with open(plan1_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def test_planning_format():
    """测试规划格式是否与 plan1.json 一致"""
    logger.info("测试规划格式...")
    
    # 加载参考格式
    reference_plans = load_plan1_format()
    logger.info(f"参考格式包含 {len(reference_plans)} 个规划结果")
    
    # 模拟 AI 规划工具的返回结果
    mock_ai_planning = {
        'student_id': 'test_student_001',
        'plan_type': 'multi_source_multi_sink_optimal',
        'message': '已为您生成最优的多路径学习计划',
        'learning_paths': [
            {
                'path_id': 'path_1',
                'path_type': 'sequential',
                'start_concept': '力学/运动学/位移、速度、加速度的概念与计算（瞬时与平均）',
                'target_concept': '力学/运动学/相对运动概念（速度合成与分解）',
                'steps': [
                    {
                        'concept_id': '力学/运动学/位移、速度、加速度的概念与计算（瞬时与平均）',
                        'concept_name': '位移、速度、加速度的概念与计算',
                        'level': 1,
                        'current_mastery': 0.8,
                        'is_mastered': True,
                        'is_weak': False,
                        'priority': 'medium'
                    },
                    {
                        'concept_id': '力学/运动学/相对运动概念（速度合成与分解）',
                        'concept_name': '相对运动概念',
                        'level': 2,
                        'current_mastery': 0.2,
                        'is_mastered': False,
                        'is_weak': True,
                        'priority': 'high'
                    }
                ],
                'estimated_time': 50
            }
        ],
        'independent_weak_concepts': [],
        'total_concepts_to_learn': 2,
        'estimated_time': 50
    }
    
    # 模拟诊断结果
    diagnostic_result = {
        'evaluation_metadata': {
            'student_id': 'test_student_001',
            'timestamp': '2024-01-01T10:00:00',
            'generator_version': 'test_1.0.0'
        },
        'diagnostic_result': {
            'mastered_concepts': [
                {
                    'concept_id': '力学/运动学/位移、速度、加速度的概念与计算（瞬时与平均）',
                    'concept_name': '位移、速度、加速度的概念与计算',
                    'mastery_level': 0.8,
                    'problem_count': 2
                }
            ],
            'not_mastered_concepts': [
                {
                    'concept_id': '力学/运动学/相对运动概念（速度合成与分解）',
                    'concept_name': '相对运动概念',
                    'mastery_level': 0.2,
                    'problem_count': 3
                }
            ]
        }
    }
    
    # 模拟新的路径构建逻辑
    def simulate_new_path_building(ai_planning):
        """模拟新的路径构建逻辑"""
        from datetime import datetime
        
        paths_subset = []
        points_subset = []
        
        # 处理学习路径
        learning_paths = ai_planning.get('learning_paths', [])
        for i, path in enumerate(learning_paths):
            if isinstance(path, dict) and 'steps' in path:
                # 提取路径中的概念
                path_concepts = []
                for step in path.get('steps', []):
                    if isinstance(step, dict) and 'concept_id' in step:
                        path_concepts.append(step['concept_id'])
                
                # 构建边列表（相邻概念之间的连接）
                edges = []
                for j in range(len(path_concepts) - 1):
                    edges.append([path_concepts[j], path_concepts[j + 1]])
                
                # 构建符合plan1.json格式的路径结构
                paths_subset.append({
                    'path_id': path.get('path_id', f'optimal_path_{i}'),
                    'nodes': path_concepts,  # 路径中的节点列表
                    'edges': edges,  # 路径中的边列表
                    'sequence': path_concepts,  # 学习顺序序列
                    'concepts': path_concepts,  # 概念列表（用于相似度计算）
                    'estimated_time': path.get('estimated_time', 0),
                    'priority': 'medium'  # 默认优先级
                })
        
        # 处理独立薄弱概念
        independent_concepts = ai_planning.get('independent_weak_concepts', [])
        for concept in independent_concepts:
            if isinstance(concept, dict):
                points_subset.append(concept.get('concept_id', ''))
            else:
                points_subset.append(str(concept))
        
        # 构建最终结果
        planning_result = {
            'evaluation_metadata': {
                'student_id': 'test_student_001',
                'target_concept': None,
                'timestamp': datetime.now().isoformat(),
                'generator_version': '2.0.0',
                'plan_type': 'multi_source_multi_sink_optimal',
                'planning_type': 'multi_target_optimal'
            },
            'planning_result': {
                'paths_subset': paths_subset,
                'points_subset': points_subset,
                'total_concepts_to_learn': len(set([c for path in paths_subset for c in path.get('concepts', [])] + points_subset)),
                'estimated_time': ai_planning.get('estimated_time', 0),
                'planning_confidence': 0.95
            }
        }
        
        return planning_result
    
    # 执行模拟
    result = simulate_new_path_building(mock_ai_planning)
    
    # 显示生成的格式
    logger.info("生成的规划结果格式:")
    logger.info(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 与参考格式对比
    reference_path_format = reference_plans[1]['planning_result']['paths_subset'][0]
    generated_path_format = result['planning_result']['paths_subset'][0]
    
    logger.info("\n=== 格式对比 ===")
    logger.info("参考格式的路径字段:")
    for key in reference_path_format.keys():
        logger.info(f"  - {key}: {type(reference_path_format[key]).__name__}")
    
    logger.info("生成格式的路径字段:")
    for key in generated_path_format.keys():
        logger.info(f"  - {key}: {type(generated_path_format[key]).__name__}")
    
    # 验证关键字段
    required_fields = ['path_id', 'nodes', 'edges', 'sequence', 'concepts', 'estimated_time', 'priority']
    missing_fields = []
    
    for field in required_fields:
        if field not in generated_path_format:
            missing_fields.append(field)
    
    if missing_fields:
        logger.error(f"缺少字段: {missing_fields}")
        return False
    
    logger.info("✅ 所有必需字段都存在")
    
    # 验证字段类型
    if not isinstance(generated_path_format['nodes'], list):
        logger.error("nodes 字段应该是列表")
        return False
    
    if not isinstance(generated_path_format['edges'], list):
        logger.error("edges 字段应该是列表")
        return False
    
    if not isinstance(generated_path_format['sequence'], list):
        logger.error("sequence 字段应该是列表")
        return False
    
    logger.info("✅ 字段类型正确")
    logger.info("✅ 格式与 plan1.json 一致")
    
    return True

def main():
    """主函数"""
    try:
        success = test_planning_format()
        if success:
            print("\n✅ 规划格式测试通过")
            print("生成的格式与 plan1.json 一致")
        else:
            print("\n❌ 规划格式测试失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        print("\n❌ 规划格式测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
