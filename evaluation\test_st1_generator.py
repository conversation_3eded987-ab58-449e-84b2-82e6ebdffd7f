#!/usr/bin/env python3
"""
测试ST1标准答案生成器
验证基于真实答题记录的标准答案生成功能
"""

import json
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_st1_data_loading():
    """测试ST1数据加载"""
    logger.info("=== 测试ST1数据加载 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator
        
        # 创建生成器实例（不需要真实的Graphiti客户端进行数据加载测试）
        generator = ST1GroundTruthGenerator(None)
        
        # 测试数据加载
        st1_file = Path("data/st1_data.json")
        if not st1_file.exists():
            logger.error(f"ST1数据文件不存在: {st1_file}")
            return False
        
        st1_data = generator.load_st1_data(st1_file)
        
        if not st1_data:
            logger.error("ST1数据加载失败")
            return False
        
        logger.info(f"✅ 成功加载 {len(st1_data)} 个学生的数据")
        
        # 检查数据结构
        first_student = st1_data[0]
        logger.info(f"第一个学生ID: {first_student.get('student_id', 'N/A')}")
        logger.info(f"第一个学生姓名: {first_student.get('name', 'N/A')}")
        logger.info(f"第一个学生答题数: {len(first_student.get('problem_attempts', []))}")
        
        # 检查答题记录结构
        if first_student.get('problem_attempts'):
            first_attempt = first_student['problem_attempts'][0]
            logger.info(f"第一道题目ID: {first_attempt.get('problem_id', 'N/A')}")
            logger.info(f"选择答案: {first_attempt.get('selected_options', [])}")
            logger.info(f"是否正确: {first_attempt.get('is_fully_correct', False)}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_data_conversion():
    """测试数据格式转换"""
    logger.info("=== 测试数据格式转换 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator
        
        generator = ST1GroundTruthGenerator(None)
        
        # 模拟ST1学生数据
        student_data = {
            'student_id': 'student_001',
            'name': '测试学生',
            'problem_attempts': [
                {
                    'problem_id': 10201107,
                    'selected_options': ['C'],
                    'is_fully_correct': True,
                    'timestamp': '2025-01-27T14:01:00Z'
                },
                {
                    'problem_id': 10200972,
                    'selected_options': ['A'],
                    'is_fully_correct': False,
                    'timestamp': '2025-01-13T16:08:00Z'
                }
            ]
        }
        
        # 模拟题目数据
        problems_data = [
            {
                'problem_id': 10201107,
                'correct_answer': 'C',
                'linked_kp_ids': ['力学/运动学/位移概念', '力学/运动学/速度概念'],
                'difficulty': 3.0,
                'misconception_map': {'A': '误解1', 'B': '误解2', 'C': '正确', 'D': '误解3'}
            },
            {
                'problem_id': 10200972,
                'correct_answer': 'B',
                'linked_kp_ids': ['力学/运动学/加速度概念'],
                'difficulty': 4.0,
                'misconception_map': {'A': '误解1', 'B': '正确', 'C': '误解2', 'D': '误解3'}
            }
        ]
        
        # 测试转换
        converted_problems = generator.convert_st1_to_problem_format(student_data, problems_data)
        
        logger.info(f"✅ 成功转换 {len(converted_problems)} 道题目")
        
        for i, problem in enumerate(converted_problems):
            logger.info(f"题目 {i+1}:")
            logger.info(f"  - 题目ID: {problem['problem_id']}")
            logger.info(f"  - 学生答案: {problem['student_answer']}")
            logger.info(f"  - 是否正确: {problem['is_correct']}")
            logger.info(f"  - 正确答案: {problem['correct_answer']}")
            logger.info(f"  - 关联知识点: {problem['linked_kp_ids']}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_diagnostic_generation():
    """测试诊断生成逻辑"""
    logger.info("=== 测试诊断生成逻辑 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator
        import asyncio
        
        generator = ST1GroundTruthGenerator(None)
        
        # 模拟学生题目数据
        student_problems = [
            {
                'problem_id': 1,
                'is_correct': True,
                'linked_kp_ids': ['概念A', '概念B']
            },
            {
                'problem_id': 2,
                'is_correct': True,
                'linked_kp_ids': ['概念A']
            },
            {
                'problem_id': 3,
                'is_correct': False,
                'linked_kp_ids': ['概念B', '概念C']
            },
            {
                'problem_id': 4,
                'is_correct': False,
                'linked_kp_ids': ['概念C']
            }
        ]
        
        # 模拟知识点验证结果
        knowledge_verification = {
            '概念A': {'exists': True, 'name': '概念A'},
            '概念B': {'exists': True, 'name': '概念B'},
            '概念C': {'exists': True, 'name': '概念C'}
        }
        
        # 测试诊断生成
        async def run_test():
            diagnostic_result = await generator.generate_student_diagnostic_ground_truth(
                'test_student', student_problems, knowledge_verification
            )
            return diagnostic_result
        
        diagnostic_result = asyncio.run(run_test())
        
        logger.info("✅ 诊断结果生成成功")
        logger.info(f"学生ID: {diagnostic_result['evaluation_metadata']['student_id']}")
        
        diagnostic_data = diagnostic_result['diagnostic_result']
        logger.info(f"已掌握概念数: {len(diagnostic_data['mastered_concepts'])}")
        logger.info(f"未掌握概念数: {len(diagnostic_data['not_mastered_concepts'])}")
        logger.info(f"掌握率: {diagnostic_data['mastery_rate']:.3f}")
        
        # 显示详细结果
        for concept in diagnostic_data['mastered_concepts']:
            logger.info(f"  已掌握: {concept['concept_id']} (掌握度: {concept['mastery_level']:.3f})")
        
        for concept in diagnostic_data['not_mastered_concepts']:
            logger.info(f"  未掌握: {concept['concept_id']} (掌握度: {concept['mastery_level']:.3f})")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("开始测试ST1标准答案生成器...")
    
    tests = [
        ("ST1数据加载", test_st1_data_loading),
        ("数据格式转换", test_data_conversion),
        ("诊断生成逻辑", test_diagnostic_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试总结: {passed}/{total} 个测试通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        print("\n🎉 所有测试通过！ST1标准答案生成器可以正常工作")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，请检查相关功能")
        sys.exit(1)

if __name__ == "__main__":
    main()
