#!/usr/bin/env python3
"""
知识图谱查看工具
提供多种方式查看和分析知识图谱数据
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Any
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.config import settings
from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    # 确定使用的API密钥和基础URL
    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    # 配置LLM客户端
    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
    )

    # 配置embedding客户端
    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL,
        base_url=embedding_base_url,
    )

    # 创建客户端组件
    llm_client = OpenAIGenericClient(config=llm_config)
    embedder = OpenAIEmbedder(config=embedder_config)
    cross_encoder = OpenAIRerankerClient(config=llm_config)

    return Graphiti(
        settings.NEO4J_URI,
        settings.NEO4J_USER,
        settings.NEO4J_PASSWORD,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

class KnowledgeGraphViewer:
    """知识图谱查看器"""
    
    def __init__(self, client: Graphiti):
        self.client = client
    
    async def get_node_statistics(self) -> Dict[str, int]:
        """获取节点统计信息"""
        query = """
        MATCH (n) 
        RETURN labels(n) as NodeType, count(n) as Count 
        ORDER BY Count DESC
        """
        
        result = await self.client.driver.execute_query(query)
        stats = {}
        for record in result.records:
            node_type = record['NodeType'][0] if record['NodeType'] else 'Unknown'
            stats[node_type] = record['Count']
        
        return stats
    
    async def get_relationship_statistics(self) -> Dict[str, int]:
        """获取关系统计信息"""
        query = """
        MATCH ()-[r]->() 
        RETURN type(r) as RelationType, count(r) as Count 
        ORDER BY Count DESC
        """
        
        result = await self.client.driver.execute_query(query)
        stats = {}
        for record in result.records:
            stats[record['RelationType']] = record['Count']
        
        return stats
    
    async def get_subject_hierarchy(self) -> List[Dict]:
        """获取学科层级结构"""
        query = """
        MATCH (s:Subject)
        OPTIONAL MATCH (s)-[:HAS_SUB_CONCEPT]->(c1:Concept)
        OPTIONAL MATCH (c1)-[:HAS_SUB_CONCEPT]->(c2:Concept)
        OPTIONAL MATCH (c2)-[:HAS_SUB_CONCEPT]->(c3:Concept)
        RETURN s.name as subject, 
               collect(DISTINCT c1.name) as level1_concepts,
               collect(DISTINCT c2.name) as level2_concepts,
               collect(DISTINCT c3.name) as level3_concepts
        ORDER BY s.name
        """
        
        result = await self.client.driver.execute_query(query)
        hierarchy = []
        for record in result.records:
            hierarchy.append({
                'subject': record['subject'],
                'level1_concepts': [c for c in record['level1_concepts'] if c],
                'level2_concepts': [c for c in record['level2_concepts'] if c],
                'level3_concepts': [c for c in record['level3_concepts'] if c]
            })
        
        return hierarchy
    
    async def get_sample_problems(self, limit: int = 5) -> List[Dict]:
        """获取示例题目"""
        query = """
        MATCH (p:Problem)
        OPTIONAL MATCH (p)-[:TESTS_CONCEPT]->(c:Concept)
        OPTIONAL MATCH (p)-[:OPTION_TARGETS]->(m:Misconception)
        RETURN p.problemId as problem_id,
               p.content as content,
               p.difficulty as difficulty,
               p.correctAnswer as correct_answer,
               collect(DISTINCT c.kpId) as tested_concepts,
               collect(DISTINCT m.misconceptionId) as misconceptions
        ORDER BY p.problemId
        LIMIT $limit
        """
        
        result = await self.client.driver.execute_query(query, limit=limit)
        problems = []
        for record in result.records:
            problems.append({
                'problem_id': record['problem_id'],
                'content': record['content'][:100] + '...' if len(record['content']) > 100 else record['content'],
                'difficulty': record['difficulty'],
                'correct_answer': record['correct_answer'],
                'tested_concepts': record['tested_concepts'],
                'misconceptions': record['misconceptions']
            })
        
        return problems
    
    async def get_prerequisite_chains(self, limit: int = 10) -> List[Dict]:
        """获取前置依赖链"""
        query = """
        MATCH path = (c1:Concept)-[:IS_PREREQUISITE_FOR*1..3]->(c2:Concept)
        RETURN [node in nodes(path) | node.kpId] as prerequisite_chain
        ORDER BY length(path) DESC
        LIMIT $limit
        """
        
        result = await self.client.driver.execute_query(query, limit=limit)
        chains = []
        for record in result.records:
            chains.append({
                'chain': record['prerequisite_chain'],
                'length': len(record['prerequisite_chain'])
            })
        
        return chains
    
    async def get_misconception_analysis(self, limit: int = 10) -> List[Dict]:
        """获取易错点分析"""
        query = """
        MATCH (m:Misconception)-[:IS_CONFUSION_OF]->(c:Concept)
        OPTIONAL MATCH (p:Problem)-[:OPTION_TARGETS]->(m)
        RETURN m.misconceptionId as misconception_id,
               m.description as description,
               c.kpId as confused_concept,
               count(DISTINCT p) as problem_count
        ORDER BY problem_count DESC
        LIMIT $limit
        """
        
        result = await self.client.driver.execute_query(query, limit=limit)
        misconceptions = []
        for record in result.records:
            misconceptions.append({
                'misconception_id': record['misconception_id'],
                'description': record['description'][:80] + '...' if len(record['description']) > 80 else record['description'],
                'confused_concept': record['confused_concept'],
                'problem_count': record['problem_count']
            })
        
        return misconceptions
    
    async def print_comprehensive_report(self):
        """打印综合报告"""
        print("=" * 80)
        print("🔍 知识图谱综合分析报告")
        print("=" * 80)
        
        # 节点统计
        print("\n📊 节点统计:")
        node_stats = await self.get_node_statistics()
        for node_type, count in node_stats.items():
            emoji = {
                'Subject': '📚',
                'Concept': '🧠', 
                'Problem': '📝',
                'Misconception': '❌',
                'Student': '👤',
                'Entity': '🔗',
                'Episodic': '📖',
                'Community': '👥'
            }.get(node_type, '📄')
            print(f"  {emoji} {node_type}: {count}")
        
        # 关系统计
        print("\n🔗 关系统计:")
        rel_stats = await self.get_relationship_statistics()
        for rel_type, count in rel_stats.items():
            emoji = {
                'HAS_SUB_CONCEPT': '🌳',
                'IS_PREREQUISITE_FOR': '⬅️',
                'TESTS_CONCEPT': '🎯',
                'OPTION_TARGETS': '🔴',
                'IS_CONFUSION_OF': '🤔',
                'ATTEMPTED': '✍️',
                'HAS_MASTERY_OF': '💪',
                'EXHIBITS_MISCONCEPTION': '😵'
            }.get(rel_type, '🔗')
            print(f"  {emoji} {rel_type}: {count}")
        
        # 学科层级
        print("\n🌳 学科层级结构:")
        hierarchy = await self.get_subject_hierarchy()
        for item in hierarchy:
            print(f"\n📚 {item['subject']}:")
            if item['level1_concepts']:
                print(f"  └─ 一级概念 ({len(item['level1_concepts'])}): {', '.join(item['level1_concepts'][:3])}{'...' if len(item['level1_concepts']) > 3 else ''}")
            if item['level2_concepts']:
                print(f"  └─ 二级概念 ({len(item['level2_concepts'])}): {', '.join(item['level2_concepts'][:3])}{'...' if len(item['level2_concepts']) > 3 else ''}")
            if item['level3_concepts']:
                print(f"  └─ 三级概念 ({len(item['level3_concepts'])}): {', '.join(item['level3_concepts'][:3])}{'...' if len(item['level3_concepts']) > 3 else ''}")
        
        # 示例题目
        print("\n📝 示例题目:")
        problems = await self.get_sample_problems(3)
        for i, problem in enumerate(problems, 1):
            print(f"\n  {i}. 题目ID: {problem['problem_id']}")
            print(f"     内容: {problem['content']}")
            print(f"     难度: {problem['difficulty']}")
            print(f"     正确答案: {problem['correct_answer']}")
            print(f"     考察概念: {', '.join(problem['tested_concepts'][:2])}{'...' if len(problem['tested_concepts']) > 2 else ''}")
        
        # 前置依赖链
        print("\n⬅️ 前置依赖链示例:")
        chains = await self.get_prerequisite_chains(5)
        for i, chain in enumerate(chains, 1):
            print(f"  {i}. {' → '.join(chain['chain'])}")
        
        # 易错点分析
        print("\n🤔 高频易错点:")
        misconceptions = await self.get_misconception_analysis(5)
        for i, misc in enumerate(misconceptions, 1):
            print(f"  {i}. {misc['description']}")
            print(f"     混淆概念: {misc['confused_concept']}")
            print(f"     相关题目数: {misc['problem_count']}")
        
        print("\n" + "=" * 80)

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='查看知识图谱数据')
    parser.add_argument('--stats', action='store_true', help='显示统计信息')
    parser.add_argument('--hierarchy', action='store_true', help='显示层级结构')
    parser.add_argument('--problems', action='store_true', help='显示示例题目')
    parser.add_argument('--misconceptions', action='store_true', help='显示易错点分析')
    parser.add_argument('--all', action='store_true', help='显示完整报告')
    
    args = parser.parse_args()
    
    try:
        # 验证配置
        settings.validate()
        
        # 创建客户端
        client = create_graphiti_client()
        viewer = KnowledgeGraphViewer(client)
        
        if args.all or not any([args.stats, args.hierarchy, args.problems, args.misconceptions]):
            # 显示完整报告
            await viewer.print_comprehensive_report()
        else:
            if args.stats:
                print("📊 节点统计:")
                node_stats = await viewer.get_node_statistics()
                for node_type, count in node_stats.items():
                    print(f"  {node_type}: {count}")
                
                print("\n🔗 关系统计:")
                rel_stats = await viewer.get_relationship_statistics()
                for rel_type, count in rel_stats.items():
                    print(f"  {rel_type}: {count}")
            
            if args.hierarchy:
                print("🌳 学科层级结构:")
                hierarchy = await viewer.get_subject_hierarchy()
                for item in hierarchy:
                    print(f"\n📚 {item['subject']}:")
                    print(f"  一级概念: {item['level1_concepts']}")
                    print(f"  二级概念: {item['level2_concepts']}")
                    print(f"  三级概念: {item['level3_concepts']}")
            
            if args.problems:
                print("📝 示例题目:")
                problems = await viewer.get_sample_problems(5)
                for problem in problems:
                    print(f"\n题目ID: {problem['problem_id']}")
                    print(f"内容: {problem['content']}")
                    print(f"考察概念: {problem['tested_concepts']}")
            
            if args.misconceptions:
                print("🤔 易错点分析:")
                misconceptions = await viewer.get_misconception_analysis(10)
                for misc in misconceptions:
                    print(f"\n{misc['description']}")
                    print(f"混淆概念: {misc['confused_concept']}")
                    print(f"相关题目数: {misc['problem_count']}")
        
    except Exception as e:
        print(f"❌ 查看知识图谱失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
