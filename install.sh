#!/bin/bash

# GraphMASAL 安装脚本

set -e  # 遇到错误时退出

echo "🚀 开始安装 GraphMASAL 多智能体教育辅导系统..."

# 检查 Python 版本
echo "📋 检查 Python 版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 错误: 需要 Python 3.8 或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✅ Python 版本检查通过: $python_version"

# 检查 pip
echo "📋 检查 pip..."
if ! command -v pip3 &> /dev/null; then
    echo "❌ 错误: pip3 未找到，请先安装 pip"
    exit 1
fi

echo "✅ pip 检查通过"

# 创建虚拟环境（可选）
read -p "🤔 是否创建虚拟环境？(推荐) [y/N]: " create_venv
if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ 虚拟环境已激活"
fi

# 升级 pip
echo "⬆️ 升级 pip..."
pip3 install --upgrade pip

# 安装构建工具
echo "🔧 安装构建工具..."
pip3 install build wheel setuptools

# 选择安装模式
echo ""
echo "请选择安装模式:"
echo "1) 开发模式 (可编辑安装，推荐用于开发)"
echo "2) 标准模式 (普通安装)"
echo "3) 完整模式 (包含所有可选依赖)"

read -p "请输入选择 [1-3]: " install_mode

case $install_mode in
    1)
        echo "🔨 以开发模式安装..."
        pip3 install -e ".[dev]"
        ;;
    2)
        echo "📦 以标准模式安装..."
        pip3 install .
        ;;
    3)
        echo "🎯 以完整模式安装..."
        pip3 install -e ".[dev,test,jupyter]"
        ;;
    *)
        echo "⚠️ 无效选择，使用默认开发模式..."
        pip3 install -e ".[dev]"
        ;;
esac

# 验证安装
echo "🔍 验证安装..."

if python3 -c "import graphmasal; print('GraphMASAL 导入成功')" 2>/dev/null; then
    echo "✅ GraphMASAL 安装成功！"
else
    echo "❌ GraphMASAL 导入失败，请检查安装"
    exit 1
fi

# 检查命令行工具
if command -v graphmasal &> /dev/null; then
    echo "✅ 命令行工具 'graphmasal' 可用"
else
    echo "⚠️ 命令行工具可能需要重新加载 PATH"
fi

# 配置提示
echo ""
echo "🎉 安装完成！"
echo ""
echo "📝 下一步:"
echo "1. 配置环境变量:"
echo "   cp graphmasal/.env.example .env"
echo "   # 编辑 .env 文件，填入你的配置"
echo ""
echo "2. 启动 Neo4j 数据库"
echo ""
echo "3. 运行演示:"
echo "   graphmasal --demo chat"
echo ""
echo "4. 运行测试:"
echo "   graphmasal-test"
echo ""

if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "💡 提示: 下次使用前请激活虚拟环境:"
    echo "   source venv/bin/activate"
    echo ""
fi

echo "📚 更多信息请查看 INSTALL.md 和 README.md"
echo "🐛 如有问题，请提交 Issue 到项目仓库"
