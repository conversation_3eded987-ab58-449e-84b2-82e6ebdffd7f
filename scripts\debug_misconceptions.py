#!/usr/bin/env python3
"""
调试学生易错点查询问题
检查数据库中的易错点数据和关系
"""

import asyncio
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from graphmasal.config import settings
from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    neo4j_uri = "bolt://localhost:7687"
    neo4j_user = "neo4j"
    neo4j_password = "12345678"
    
    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
    )

    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL or "doubao-embedding-text-240715",
        base_url=embedding_base_url,
    )

    llm_client = OpenAIGenericClient(llm_config)
    embedder = OpenAIEmbedder(embedder_config)
    cross_encoder = OpenAIRerankerClient(llm_config)

    return Graphiti(
        neo4j_uri,
        neo4j_user,
        neo4j_password,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

async def debug_student_misconceptions(client: Graphiti, student_id: str = "student_020"):
    """调试学生易错点数据"""
    
    logger.info(f"=== 调试学生 {student_id} 的易错点数据 ===")
    
    try:
        # 1. 检查学生节点是否存在
        logger.info("1. 检查学生节点...")
        student_query = """
        MATCH (student:Student {studentId: $student_id})
        RETURN student.studentId as student_id, student.name as student_name
        """
        
        student_result = await client.driver.execute_query(
            student_query, student_id=student_id
        )
        
        if not student_result.records:
            logger.error(f"❌ 学生 {student_id} 不存在")
            return
        else:
            student_record = student_result.records[0]
            logger.info(f"✅ 找到学生: {student_record['student_id']} ({student_record.get('student_name', 'N/A')})")
        
        # 2. 检查EXHIBITS_MISCONCEPTION关系
        logger.info("2. 检查EXHIBITS_MISCONCEPTION关系...")
        exhibits_query = """
        MATCH (student:Student {studentId: $student_id})-[r:EXHIBITS_MISCONCEPTION]->()
        RETURN count(r) as relationship_count
        """
        
        exhibits_result = await client.driver.execute_query(
            exhibits_query, student_id=student_id
        )
        
        if exhibits_result.records:
            rel_count = exhibits_result.records[0]['relationship_count']
            logger.info(f"✅ 学生 {student_id} 有 {rel_count} 个EXHIBITS_MISCONCEPTION关系")
        else:
            logger.warning(f"⚠️ 无法查询学生 {student_id} 的EXHIBITS_MISCONCEPTION关系")
        
        # 3. 检查Misconception节点
        logger.info("3. 检查Misconception节点...")
        misconception_count_query = """
        MATCH (m:Misconception)
        RETURN count(m) as total_misconceptions
        """
        
        misc_count_result = await client.driver.execute_query(misconception_count_query)
        if misc_count_result.records:
            total_misc = misc_count_result.records[0]['total_misconceptions']
            logger.info(f"✅ 数据库中共有 {total_misc} 个Misconception节点")
        
        # 4. 尝试完整的易错点查询
        logger.info("4. 执行完整的易错点查询...")
        full_query = """
        MATCH (student:Student {studentId: $student_id})-[exhibits:EXHIBITS_MISCONCEPTION]->(misconception:Misconception)
        RETURN misconception.misconceptionId as misconception_id,
               misconception.description as description,
               exhibits.strength as strength
        ORDER BY exhibits.strength DESC
        LIMIT 10
        """
        
        full_result = await client.driver.execute_query(
            full_query, student_id=student_id
        )
        
        misconceptions = []
        for record in full_result.records:
            misconceptions.append({
                'misconception_id': record['misconception_id'],
                'description': record['description'],
                'strength': record['strength']
            })
        
        logger.info(f"✅ 查询结果: {len(misconceptions)} 个易错点")
        
        if misconceptions:
            logger.info("易错点详情:")
            for i, misc in enumerate(misconceptions, 1):
                logger.info(f"  {i}. {misc['description']} (强度: {misc['strength']})")
        else:
            logger.warning("❌ 没有找到任何易错点")
            
            # 5. 进一步调试 - 检查关系的目标节点
            logger.info("5. 检查关系的目标节点...")
            debug_query = """
            MATCH (student:Student {studentId: $student_id})-[r:EXHIBITS_MISCONCEPTION]->(target)
            RETURN labels(target) as target_labels, 
                   target.misconceptionId as target_id,
                   target.description as target_desc,
                   r.strength as strength
            LIMIT 5
            """
            
            debug_result = await client.driver.execute_query(
                debug_query, student_id=student_id
            )
            
            if debug_result.records:
                logger.info("关系目标节点详情:")
                for record in debug_result.records:
                    logger.info(f"  - 标签: {record['target_labels']}")
                    logger.info(f"    ID: {record.get('target_id', 'N/A')}")
                    logger.info(f"    描述: {record.get('target_desc', 'N/A')}")
                    logger.info(f"    强度: {record.get('strength', 'N/A')}")
            else:
                logger.warning("❌ 没有找到任何关系目标节点")
        
        # 6. 检查是否有其他学生有易错点数据
        logger.info("6. 检查其他学生的易错点数据...")
        other_students_query = """
        MATCH (student:Student)-[r:EXHIBITS_MISCONCEPTION]->(m:Misconception)
        RETURN student.studentId as student_id, count(r) as misconception_count
        ORDER BY misconception_count DESC
        LIMIT 5
        """
        
        other_result = await client.driver.execute_query(other_students_query)
        
        if other_result.records:
            logger.info("其他学生的易错点数据:")
            for record in other_result.records:
                logger.info(f"  - 学生 {record['student_id']}: {record['misconception_count']} 个易错点")
        else:
            logger.warning("❌ 没有找到任何学生的易错点数据")
        
    except Exception as e:
        logger.error(f"调试过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='调试学生易错点查询问题')
    parser.add_argument('--student-id', 
                       default='student_020',
                       help='要调试的学生ID')
    
    args = parser.parse_args()
    
    try:
        # 创建Graphiti客户端
        logger.info("初始化Graphiti客户端...")
        client = create_graphiti_client()
        
        # 调试易错点数据
        await debug_student_misconceptions(client, args.student_id)
        
        logger.info("🎉 调试完成")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
