#!/usr/bin/env python3
"""
批量导入知识图谱数据脚本
使用 Graphiti add_episode_bulk 和 Neo4j 直接查询进行高效批量导入
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime, timezone
import sys
import os

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from graphmasal.config import settings
from graphmasal.knowledge_graph.schema_init import init_knowledge_graph

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KnowledgeGraphBulkImporter:
    """知识图谱批量导入器"""
    
    def __init__(self, client: Graphiti):
        self.client = client
        self.stats = {
            'concepts_created': 0,
            'problems_created': 0,
            'misconceptions_created': 0,
            'prerequisites_created': 0,
            'test_relations_created': 0,
            'option_relations_created': 0,
            'episodes_created': 0
        }
    
    async def load_json_data(self, file_path: Path) -> List[Dict[str, Any]]:
        """加载 JSON 数据文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载数据文件: {file_path}, 记录数: {len(data)}")
            return data
        except Exception as e:
            logger.error(f"加载数据文件失败 {file_path}: {e}")
            return []
    
    async def extract_all_concepts_and_hierarchy(self, relationships_data: List[Dict], problems_data: List[Dict]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """从关系数据和题目数据中提取所有概念、学科和层级关系"""
        all_concept_paths = set()

        # 从关系数据中提取概念路径
        for item in relationships_data:
            target_concept = item.get('target_concept', '')
            if target_concept:
                all_concept_paths.add(target_concept)

            for prereq in item.get('direct_prerequisites', []):
                all_concept_paths.add(prereq)

        # 从题目数据中提取概念路径
        for item in problems_data:
            for kp_id in item.get('linked_kp_ids', []):
                all_concept_paths.add(kp_id)

        # 提取所有可能的路径片段（包括父级路径）
        expanded_paths = set()
        for path in all_concept_paths:
            parts = path.split('/')
            for i in range(1, len(parts) + 1):
                expanded_paths.add('/'.join(parts[:i]))

        # 分离学科和概念
        subjects = []
        concepts = []
        hierarchy_relations = []

        for path in expanded_paths:
            parts = path.split('/')
            level = len(parts)
            name = parts[-1]

            if level == 1:
                # 这是学科节点
                subjects.append({
                    'name': name
                })
            else:
                # 这是概念节点
                concepts.append({
                    'kpId': path,
                    'name': name,
                    'level': level
                })

            # 创建层级关系
            if level > 1:
                parent_path = '/'.join(parts[:-1])
                hierarchy_relations.append({
                    'parent': parent_path,
                    'child': path,
                    'parent_is_subject': len(parts) == 2  # 父级是否为学科
                })

        logger.info(f"提取到 {len(subjects)} 个学科, {len(concepts)} 个概念, {len(hierarchy_relations)} 个层级关系")
        return subjects, concepts, hierarchy_relations
    
    async def batch_create_subjects(self, subjects: List[Dict[str, Any]]) -> None:
        """批量创建学科节点"""
        if not subjects:
            return

        query = """
        UNWIND $subjects AS subject
        MERGE (s:Subject {name: subject.name})
        RETURN count(s) as created_count
        """

        try:
            result = await self.client.driver.execute_query(query, subjects=subjects)
            count = result.records[0]['created_count']
            self.stats['subjects_created'] = self.stats.get('subjects_created', 0) + count
            logger.info(f"批量创建学科节点完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建学科节点失败: {e}")

    async def batch_create_concepts(self, concepts: List[Dict[str, Any]]) -> None:
        """批量创建概念节点"""
        if not concepts:
            return

        query = """
        UNWIND $concepts AS concept
        MERGE (c:Concept {kpId: concept.kpId})
        SET c.name = concept.name,
            c.level = concept.level
        RETURN count(c) as created_count
        """

        try:
            result = await self.client.driver.execute_query(query, concepts=concepts)
            count = result.records[0]['created_count']
            self.stats['concepts_created'] += count
            logger.info(f"批量创建概念节点完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建概念节点失败: {e}")

    async def batch_create_hierarchy_relations(self, hierarchy_relations: List[Dict]) -> None:
        """批量创建知识点层级关系"""
        if not hierarchy_relations:
            return

        # 分别处理学科->概念和概念->概念的关系
        subject_to_concept = [r for r in hierarchy_relations if r['parent_is_subject']]
        concept_to_concept = [r for r in hierarchy_relations if not r['parent_is_subject']]

        # 创建学科->概念关系
        if subject_to_concept:
            query1 = """
            UNWIND $relations AS rel
            MERGE (s:Subject {name: rel.parent})
            MERGE (c:Concept {kpId: rel.child})
            MERGE (s)-[:HAS_SUB_CONCEPT]->(c)
            RETURN count(*) as created_count
            """

            try:
                result = await self.client.driver.execute_query(query1, relations=subject_to_concept)
                count = result.records[0]['created_count']
                self.stats['hierarchy_relations_created'] = self.stats.get('hierarchy_relations_created', 0) + count
                logger.info(f"批量创建学科->概念关系完成: {count} 个")
            except Exception as e:
                logger.error(f"批量创建学科->概念关系失败: {e}")

        # 创建概念->概念关系
        if concept_to_concept:
            query2 = """
            UNWIND $relations AS rel
            MERGE (parent:Concept {kpId: rel.parent})
            MERGE (child:Concept {kpId: rel.child})
            MERGE (parent)-[:HAS_SUB_CONCEPT]->(child)
            RETURN count(*) as created_count
            """

            try:
                result = await self.client.driver.execute_query(query2, relations=concept_to_concept)
                count = result.records[0]['created_count']
                self.stats['hierarchy_relations_created'] = self.stats.get('hierarchy_relations_created', 0) + count
                logger.info(f"批量创建概念->概念关系完成: {count} 个")
            except Exception as e:
                logger.error(f"批量创建概念->概念关系失败: {e}")
    
    async def batch_create_prerequisite_relationships(self, relationships_data: List[Dict]) -> None:
        """批量创建前置依赖关系"""
        if not relationships_data:
            return
        
        # 准备关系数据
        relations = []
        for item in relationships_data:
            target = item.get('target_concept', '')
            for prereq in item.get('direct_prerequisites', []):
                relations.append({
                    'prerequisite': prereq,
                    'target': target
                })
        
        if not relations:
            return
        
        query = """
        UNWIND $relations AS rel
        MERGE (prereq:Concept {kpId: rel.prerequisite})
        MERGE (target:Concept {kpId: rel.target})
        MERGE (prereq)-[:IS_PREREQUISITE_FOR]->(target)
        RETURN count(*) as created_count
        """
        
        try:
            result = await self.client.driver.execute_query(query, relations=relations)
            count = result.records[0]['created_count']
            self.stats['prerequisites_created'] += count
            logger.info(f"批量创建前置依赖关系完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建前置依赖关系失败: {e}")
    
    async def extract_problems_and_misconceptions(self, problems_data: List[Dict]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """从题目数据中提取题目、易错点和易错点归属关系"""
        import re

        problems = []
        misconceptions = []
        confusion_relations = []

        for item in problems_data:
            # 提取题目数据
            problem = {
                'problemId': item.get('question_id'),
                'content': item.get('content', ''),
                'difficulty': item.get('difficulty', 0.0),
                'correctAnswer': [item.get('correct_answer', '')],
                'imageUrls': item.get('question_img_list', [])
            }
            problems.append(problem)

            # 提取易错点数据
            misconception_map = item.get('misconception_map', {})
            for option_id, description in misconception_map.items():
                misconception_id = f"{item.get('question_id')}_{option_id}"

                # 解析描述中的知识点引用（参见...）
                confused_kp_id = None
                match = re.search(r"（参见(.*?)）", description)
                if match:
                    confused_kp_id = match.group(1).strip()

                misconceptions.append({
                    'misconceptionId': misconception_id,
                    'description': description,
                    'problemId': item.get('question_id'),
                    'optionId': option_id
                })

                # 如果找到了相关的知识点，创建归属关系
                if confused_kp_id:
                    confusion_relations.append({
                        'misconceptionId': misconception_id,
                        'conceptId': confused_kp_id
                    })

        logger.info(f"提取到 {len(problems)} 个题目, {len(misconceptions)} 个易错点, {len(confusion_relations)} 个易错点归属关系")
        return problems, misconceptions, confusion_relations
    
    async def batch_create_problems(self, problems: List[Dict[str, Any]]) -> None:
        """批量创建题目节点"""
        if not problems:
            return
        
        query = """
        UNWIND $problems AS problem
        MERGE (p:Problem {problemId: problem.problemId})
        SET p.content = problem.content,
            p.difficulty = problem.difficulty,
            p.correctAnswer = problem.correctAnswer,
            p.imageUrls = problem.imageUrls
        RETURN count(p) as created_count
        """
        
        try:
            result = await self.client.driver.execute_query(query, problems=problems)
            count = result.records[0]['created_count']
            self.stats['problems_created'] += count
            logger.info(f"批量创建题目节点完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建题目节点失败: {e}")
    
    async def batch_create_misconceptions(self, misconceptions: List[Dict[str, Any]]) -> None:
        """批量创建易错点节点"""
        if not misconceptions:
            return
        
        query = """
        UNWIND $misconceptions AS misc
        MERGE (m:Misconception {misconceptionId: misc.misconceptionId})
        SET m.description = misc.description
        RETURN count(m) as created_count
        """
        
        try:
            result = await self.client.driver.execute_query(query, misconceptions=misconceptions)
            count = result.records[0]['created_count']
            self.stats['misconceptions_created'] += count
            logger.info(f"批量创建易错点节点完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建易错点节点失败: {e}")
    
    async def batch_create_problem_concept_relations(self, problems_data: List[Dict]) -> None:
        """批量创建题目-概念关系"""
        relations = []
        for item in problems_data:
            problem_id = item.get('question_id')
            for kp_id in item.get('linked_kp_ids', []):
                relations.append({
                    'problemId': problem_id,
                    'conceptId': kp_id
                })
        
        if not relations:
            return
        
        query = """
        UNWIND $relations AS rel
        MERGE (p:Problem {problemId: rel.problemId})
        MERGE (c:Concept {kpId: rel.conceptId})
        MERGE (p)-[:TESTS_CONCEPT]->(c)
        RETURN count(*) as created_count
        """
        
        try:
            result = await self.client.driver.execute_query(query, relations=relations)
            count = result.records[0]['created_count']
            self.stats['test_relations_created'] += count
            logger.info(f"批量创建题目-概念关系完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建题目-概念关系失败: {e}")
    
    async def batch_create_option_misconception_relations(self, misconceptions: List[Dict]) -> None:
        """批量创建选项-易错点关系"""
        relations = []
        for misc in misconceptions:
            relations.append({
                'problemId': misc['problemId'],
                'misconceptionId': misc['misconceptionId'],
                'optionId': misc['optionId']
            })

        if not relations:
            return

        query = """
        UNWIND $relations AS rel
        MERGE (p:Problem {problemId: rel.problemId})
        MERGE (m:Misconception {misconceptionId: rel.misconceptionId})
        MERGE (p)-[r:OPTION_TARGETS]->(m)
        SET r.option_id = rel.optionId
        RETURN count(*) as created_count
        """

        try:
            result = await self.client.driver.execute_query(query, relations=relations)
            count = result.records[0]['created_count']
            self.stats['option_relations_created'] += count
            logger.info(f"批量创建选项-易错点关系完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建选项-易错点关系失败: {e}")

    async def batch_create_confusion_relations(self, confusion_relations: List[Dict]) -> None:
        """批量创建易错点归属关系 (IS_CONFUSION_OF)"""
        if not confusion_relations:
            return

        query = """
        UNWIND $relations AS rel
        MERGE (m:Misconception {misconceptionId: rel.misconceptionId})
        MERGE (c:Concept {kpId: rel.conceptId})
        MERGE (m)-[:IS_CONFUSION_OF]->(c)
        RETURN count(*) as created_count
        """

        try:
            result = await self.client.driver.execute_query(query, relations=confusion_relations)
            count = result.records[0]['created_count']
            self.stats['confusion_relations_created'] = self.stats.get('confusion_relations_created', 0) + count
            logger.info(f"批量创建易错点归属关系完成: {count} 个")
        except Exception as e:
            logger.error(f"批量创建易错点归属关系失败: {e}")

    async def create_import_summary_episode(self, import_stats: Dict) -> None:
        """创建导入摘要 episode（可选）"""
        try:
            summary_content = {
                'import_type': 'knowledge_graph_bulk_import',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'statistics': import_stats,
                'description': '知识图谱批量导入完成'
            }

            await self.client.add_episode(
                name="Knowledge Graph Bulk Import Summary",
                episode_body=json.dumps(summary_content, ensure_ascii=False),
                source=EpisodeType.json,
                source_description='Bulk Import Summary',
                reference_time=datetime.now(timezone.utc)
            )

            logger.info("导入摘要 episode 已创建")
        except Exception as e:
            logger.error(f"创建导入摘要 episode 失败: {e}")

    async def import_knowledge_relationships(self, file_path: Path, problems_data: List[Dict] = None) -> List[Dict]:
        """导入知识点关系数据"""
        logger.info(f"开始导入知识点关系数据: {file_path}")

        # 加载数据
        relationships_data = await self.load_json_data(file_path)
        if not relationships_data:
            return []

        # 如果没有提供题目数据，使用空列表
        if problems_data is None:
            problems_data = []

        # 提取学科、概念和层级关系
        subjects, concepts, hierarchy_relations = await self.extract_all_concepts_and_hierarchy(
            relationships_data, problems_data
        )

        # 批量创建学科节点
        await self.batch_create_subjects(subjects)

        # 批量创建概念节点
        await self.batch_create_concepts(concepts)

        # 批量创建层级关系
        await self.batch_create_hierarchy_relations(hierarchy_relations)

        # 批量创建前置依赖关系
        await self.batch_create_prerequisite_relationships(relationships_data)

        return relationships_data

    async def import_problems_data(self, file_path: Path) -> List[Dict]:
        """导入题目数据"""
        logger.info(f"开始导入题目数据: {file_path}")

        # 加载数据
        problems_data = await self.load_json_data(file_path)
        if not problems_data:
            return []

        # 提取题目、易错点和归属关系
        problems, misconceptions, confusion_relations = await self.extract_problems_and_misconceptions(problems_data)

        # 批量创建题目节点
        await self.batch_create_problems(problems)

        # 批量创建易错点节点
        await self.batch_create_misconceptions(misconceptions)

        # 批量创建题目-概念关系
        await self.batch_create_problem_concept_relations(problems_data)

        # 批量创建选项-易错点关系
        await self.batch_create_option_misconception_relations(misconceptions)

        # 批量创建易错点归属关系
        await self.batch_create_confusion_relations(confusion_relations)

        return problems_data

    async def import_all_data(self, relationships_file: Path, problems_file: Path) -> None:
        """导入所有数据"""
        logger.info("开始批量导入知识图谱数据...")

        start_time = datetime.now()

        try:
            # 先加载题目数据，以便在导入知识点关系时能够提取完整的概念层级
            problems_data = await self.load_json_data(problems_file)

            # 导入知识点关系（包含完整的层级结构）
            await self.import_knowledge_relationships(relationships_file, problems_data)

            # 导入题目数据
            await self.import_problems_data(problems_file)

            # 创建导入摘要 episode（可选）
            await self.create_import_summary_episode(self.stats)

            # 打印统计信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info("=" * 80)
            logger.info("🎉 批量导入完成！统计信息:")
            logger.info(f"📚 学科节点: {self.stats.get('subjects_created', 0)}")
            logger.info(f"🧠 概念节点: {self.stats['concepts_created']}")
            logger.info(f"📝 题目节点: {self.stats['problems_created']}")
            logger.info(f"❌ 易错点节点: {self.stats['misconceptions_created']}")
            logger.info(f"🔗 层级关系: {self.stats.get('hierarchy_relations_created', 0)}")
            logger.info(f"⬅️ 前置依赖关系: {self.stats['prerequisites_created']}")
            logger.info(f"🎯 题目-概念关系: {self.stats['test_relations_created']}")
            logger.info(f"🔴 选项-易错点关系: {self.stats['option_relations_created']}")
            logger.info(f"🤔 易错点归属关系: {self.stats.get('confusion_relations_created', 0)}")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            logger.info("=" * 80)

        except Exception as e:
            logger.error(f"批量导入失败: {e}")
            raise

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    # 确定使用的API密钥和基础URL
    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    # 配置LLM客户端
    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,  # 使用同一个模型
        base_url=settings.OPENAI_BASE_URL,
    )

    # 配置embedding客户端
    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL,
        base_url=embedding_base_url,
    )

    # 创建客户端组件
    llm_client = OpenAIGenericClient(config=llm_config)
    embedder = OpenAIEmbedder(config=embedder_config)
    cross_encoder = OpenAIRerankerClient(config=llm_config)

    # 创建Graphiti客户端
    return Graphiti(
        settings.NEO4J_URI,
        settings.NEO4J_USER,
        settings.NEO4J_PASSWORD,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='批量导入知识图谱数据')
    parser.add_argument('--relationships',
                       default='data/physics_knowledge_relationships.json',
                       help='知识点关系数据文件路径')
    parser.add_argument('--problems',
                       default='data/start_0_end_200_new_data.json',
                       help='题目数据文件路径')
    parser.add_argument('--clear', action='store_true',
                       help='清除现有数据重新导入')

    args = parser.parse_args()

    # 验证文件路径
    relationships_file = Path(args.relationships)
    problems_file = Path(args.problems)

    if not relationships_file.exists():
        logger.error(f"知识点关系文件不存在: {relationships_file}")
        return

    if not problems_file.exists():
        logger.error(f"题目数据文件不存在: {problems_file}")
        return

    try:
        # 验证配置
        settings.validate()

        # 创建 Graphiti 客户端
        client = create_graphiti_client()

        # 初始化知识图谱 Schema（如果需要清除数据）
        if args.clear:
            logger.info("清除现有数据并重新初始化...")
            await init_knowledge_graph(client, clear_existing=True)

        # 创建批量导入器
        importer = KnowledgeGraphBulkImporter(client)

        # 执行批量导入
        await importer.import_all_data(relationships_file, problems_file)

        logger.info("批量导入任务完成！")

    except Exception as e:
        logger.error(f"批量导入失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
