在题库start_0_end_200_new_data.json里给每个学生随机选择20道题目及其做题记录，要包含做的和做错的，说明和格式如下，最终输出json即可
字段	             类型	            必需	            描述
student_id	        string		        ✅                学生唯一标识符，用于在知识图谱中创建Student节点
name	            string		        ✅                学生姓名
problem_id	        integer		        ✅                题目ID对应题目数据集的question_id
selected_options	array[string]	    ✅    	        学生选择的选项，如["A", "B"]或["A"]
is_fully_correct	boolean		        ✅                是否完全正确
[
  {
    "student_id": "0000000001",
    "name": "张三",
    "problem_attempts": [
      {
        "problem_id": 1,
        "selected_options": ["A", "C"],
        "is_fully_correct": false,
        "timestamp": "2025-01-15T10:30:00Z"

      },
      {
        "problem_id": 5,
        "selected_options": ["B"],
        "is_fully_correct": true,
        "timestamp": "2025-01-15T11:00:00Z"

      }
    ]
  },
  {
    "student_id": "0000000002",
    "name": "李四",
    "problem_attempts": [
      {
        "problem_id": 2,
        "selected_options": ["A", "B"],
        "is_fully_correct": true,
        "timestamp": "2025-01-15T14:20:00Z"

      },
      {
        "problem_id": 3,
        "selected_options": ["C"],
        "is_fully_correct": false,
        "timestamp": "2025-01-15T15:00:00Z"

      }
    ]
  },
  {
    "student_id": "0000000003",
    "name": "王五",
    "problem_attempts": [
      {
        "problem_id": 1,
        "selected_options": ["B"],
        "is_fully_correct": true,
        "timestamp": "2025-01-16T09:15:00Z"

      }
    ]
  }
]
