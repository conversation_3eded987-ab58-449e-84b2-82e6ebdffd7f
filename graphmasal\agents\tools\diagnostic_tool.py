"""
诊断工具模块
分析学生答题错误，识别易错点并更新知识图谱
"""
from typing import List, Dict, Any, Optional
from langchain_core.tools import tool
from graphiti_core import Graphiti
from datetime import datetime, timezone
import logging
import json

logger = logging.getLogger(__name__)

class DiagnosticTool:
    """诊断工具类"""
    
    def __init__(self, client: Graphiti):
        self.client = client
    
    async def analyze_problem_attempt(
        self,
        student_id: str,
        problem_id: int,
        selected_options: List[str],
        correct_answer: List[str],
        misconception_map: Dict[str, str],
        concept_id: str = None,  # 新增参数，用于盲测场景
        strict_mode: bool = False  # 严格模式：API失败时抛出错误而不回退
    ) -> Dict[str, Any]:
        """使用LLM API分析学生答题情况"""

        # 基本的对错判断（用于验证）
        is_fully_correct = set(selected_options) == set(correct_answer)

        # 🤖 使用LLM进行深度认知诊断
        llm_analysis = await self._llm_cognitive_diagnosis(
            student_id=student_id,
            problem_id=problem_id,
            selected_options=selected_options,
            correct_answer=correct_answer,
            misconception_map=misconception_map,
            concept_id=concept_id,
            strict_mode=strict_mode
        )

        # 更新学生答题记录
        await self._update_student_attempt(
            student_id, problem_id, selected_options, is_fully_correct
        )

        # 更新易错点关系（基于LLM分析结果）
        if llm_analysis.get('identified_misconceptions'):
            await self._update_misconception_relationships(
                student_id, llm_analysis['identified_misconceptions']
            )

        # 分析相关知识点掌握度（基于LLM推理）
        mastery_analysis = await self._llm_mastery_analysis(
            student_id, problem_id, llm_analysis, concept_id, strict_mode
        )

        return {
            'is_correct': llm_analysis.get('is_correct', is_fully_correct),
            'identified_misconceptions': llm_analysis.get('identified_misconceptions', []),
            'mastery_analysis': mastery_analysis,
            'recommendations': llm_analysis.get('recommendations', []),
            'cognitive_analysis': llm_analysis.get('cognitive_analysis', {}),
            'confidence_score': llm_analysis.get('confidence_score', 0.5)
        }

    async def _llm_cognitive_diagnosis(
        self,
        student_id: str,
        problem_id: int,
        selected_options: List[str],
        correct_answer: List[str],
        misconception_map: Dict[str, str],
        concept_id: str = None,
        strict_mode: bool = False
    ) -> Dict[str, Any]:
        """使用LLM进行认知诊断"""

        try:
            # 直接创建LLM客户端
            from graphmasal.config import settings
            from langchain_openai import ChatOpenAI

            llm_client = ChatOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL,
                model=settings.OPENAI_MODEL,
                temperature=settings.TEMPERATURE
            )

            # 构造诊断提示词
            prompt = self._build_diagnostic_prompt(
                student_id, problem_id, selected_options, correct_answer,
                misconception_map, concept_id
            )

            logger.info(f"🤖 开始LLM认知诊断 - 学生 {student_id}, 题目 {problem_id}")

            # 调用LLM API - 使用正确的消息格式
            from langchain_core.messages import HumanMessage
            messages = [HumanMessage(content=prompt)]
            response = await llm_client.agenerate([messages])
            llm_output = response.generations[0][0].text

            logger.info(f"✅ LLM诊断完成 - 学生 {student_id}")

            # 解析LLM输出
            analysis_result = self._parse_llm_diagnostic_output(llm_output)

            return analysis_result

        except Exception as e:
            logger.error(f"❌ LLM认知诊断失败: {e}")
            if strict_mode:
                # 严格模式下直接抛出错误
                raise RuntimeError(f"LLM认知诊断API调用失败: {e}") from e
            else:
                # 非严格模式下回退到基本分析
                return self._fallback_analysis(selected_options, correct_answer, misconception_map, concept_id)

    def _fallback_analysis(self, selected_options: List[str], correct_answer: List[str],
                          misconception_map: Dict[str, Any], concept_id: str) -> Dict[str, Any]:
        """
        回退分析方法：当LLM调用失败时使用基本规则进行分析
        """
        try:
            # 基本正确性判断
            is_correct = selected_options == correct_answer if selected_options and correct_answer else False

            # 基本掌握度评估
            mastery_level = 0.8 if is_correct else 0.2

            # 基本认知分析
            cognitive_analysis = "答题正确，显示对概念有基本理解" if is_correct else "答题错误，可能存在概念理解问题"

            # 基本易错点识别
            misconceptions = []
            if not is_correct and misconception_map:
                # 简单映射：如果答错了，可能存在相关易错点
                for misconception_id, misconception_info in misconception_map.items():
                    misconceptions.append({
                        "misconception_id": misconception_id,
                        "confidence": 0.5,
                        "description": misconception_info.get("description", "未知易错点")
                    })

            # 基本学习建议
            learning_suggestions = [
                "复习相关概念的基本定义" if not is_correct else "继续保持，可以尝试更难的题目",
                f"重点关注 {concept_id} 相关知识点"
            ]

            return {
                "is_correct": is_correct,
                "mastery_level": mastery_level,
                "confidence": 0.6,  # 回退分析的置信度较低
                "cognitive_analysis": cognitive_analysis,
                "misconceptions": misconceptions,
                "learning_suggestions": learning_suggestions,
                "analysis_method": "fallback_rule_based"
            }

        except Exception as e:
            logger.error(f"回退分析也失败了: {e}")
            # 最基本的返回
            return {
                "is_correct": False,
                "mastery_level": 0.5,
                "confidence": 0.3,
                "cognitive_analysis": "无法进行详细分析",
                "misconceptions": [],
                "learning_suggestions": ["建议寻求教师帮助"],
                "analysis_method": "minimal_fallback"
            }

    def _build_diagnostic_prompt(
        self,
        student_id: str,
        problem_id: int,
        selected_options: List[str],
        correct_answer: List[str],
        misconception_map: Dict[str, str],
        concept_id: str = None
    ) -> str:
        """构造认知诊断提示词"""

        # 基本信息
        prompt = f"""你是一位专业的教育心理学家和认知诊断专家。请分析学生的答题情况，进行深度认知诊断。

**题目信息：**
- 题目ID: {problem_id}
- 知识点: {concept_id or '未指定'}
- 正确答案: {correct_answer}
- 学生选择: {selected_options}

**易错点映射：**
"""

        # 添加易错点信息
        if misconception_map:
            for option, description in misconception_map.items():
                prompt += f"- 选项 {option}: {description}\n"
        else:
            prompt += "- 无预定义易错点\n"

        prompt += f"""
**诊断任务：**
请基于学生的选择进行深度认知诊断，分析以下方面：

1. **正确性判断**: 学生答案是否正确
2. **认知过程分析**: 学生可能的思维过程和推理路径
3. **易错点识别**: 如果答错，识别具体的认知误区
4. **知识掌握度评估**: 对相关知识点的掌握程度（0-1分）
5. **学习建议**: 针对性的学习建议

**输出格式（严格按照JSON格式）：**
```json
{{
    "is_correct": true/false,
    "confidence_score": 0.0-1.0,
    "cognitive_analysis": {{
        "reasoning_process": "学生可能的思维过程",
        "knowledge_gaps": ["知识缺陷1", "知识缺陷2"],
        "cognitive_level": "记忆/理解/应用/分析/综合/评价"
    }},
    "identified_misconceptions": [
        {{
            "problem_id": {problem_id},
            "option": "错误选项",
            "description": "易错点描述",
            "severity": "高/中/低"
        }}
    ],
    "mastery_level": 0.0-1.0,
    "recommendations": [
        "具体学习建议1",
        "具体学习建议2"
    ]
}}
```

请进行专业的认知诊断分析：
"""

        return prompt

    async def _update_student_attempt(
        self, 
        student_id: str, 
        problem_id: int, 
        selected_options: List[str], 
        is_fully_correct: bool
    ):
        """更新学生答题记录到知识图谱"""
        
        query = """
        MERGE (student:Student {studentId: $student_id})
        MERGE (problem:Problem {problemId: $problem_id})
        MERGE (student)-[attempt:ATTEMPTED]->(problem)
        SET attempt.selectedOptions = $selected_options,
            attempt.isFullyCorrect = $is_fully_correct,
            attempt.timestamp = datetime()
        RETURN attempt
        """
        
        try:
            await self.client.driver.execute_query(
                query,
                student_id=student_id,
                problem_id=problem_id,
                selected_options=selected_options,
                is_fully_correct=is_fully_correct
            )
            logger.info(f"学生 {student_id} 答题记录已更新")
        except Exception as e:
            logger.error(f"更新答题记录失败: {e}")
    
    async def _update_misconception_relationships(
        self, 
        student_id: str, 
        misconceptions: List[Dict[str, str]]
    ):
        """更新学生易错点关系"""
        
        for misconception in misconceptions:
            # 创建易错点节点和关系
            query = """
            MERGE (student:Student {studentId: $student_id})
            MERGE (misconception:Misconception {
                misconceptionId: $misconception_id,
                description: $description
            })
            MERGE (student)-[exhibits:EXHIBITS_MISCONCEPTION]->(misconception)
            SET exhibits.strength = COALESCE(exhibits.strength, 0) + 1,
                exhibits.lastOccurrence = datetime()
            RETURN exhibits
            """
            
            misconception_id = f"{student_id}_{misconception['option']}"
            
            try:
                await self.client.driver.execute_query(
                    query,
                    student_id=student_id,
                    misconception_id=misconception_id,
                    description=misconception['description']
                )
                logger.info(f"易错点关系已更新: {misconception_id}")
            except Exception as e:
                logger.error(f"更新易错点关系失败: {e}")
    
    async def _analyze_concept_mastery(
        self,
        student_id: str,
        problem_id: int,
        is_correct: bool,
        concept_id: str = None  # 新增参数，用于盲测场景
    ) -> Dict[str, float]:
        """分析学生对相关知识点的掌握度"""

        mastery_levels = {}

        # 如果直接提供了concept_id（盲测场景），直接使用
        if concept_id:
            logger.info(f"使用提供的概念ID进行掌握度分析: {concept_id}")

            # 计算该知识点的掌握度
            mastery_level = await self._calculate_concept_mastery(
                student_id, concept_id, is_correct
            )

            mastery_levels[concept_id] = mastery_level

            # 更新知识图谱中的掌握度关系
            await self._update_mastery_relationship(
                student_id, concept_id, mastery_level
            )

            return mastery_levels

        # 否则从数据库查询题目相关的知识点（正常场景）
        query = """
        MATCH (problem:Problem {problemId: $problem_id})-[:TESTS_CONCEPT]->(concept:Concept)
        RETURN concept.kpId as concept_id, concept.name as concept_name
        """

        try:
            result = await self.client.driver.execute_query(
                query, problem_id=problem_id
            )

            for record in result.records:
                db_concept_id = record['concept_id']

                # 计算该知识点的掌握度
                mastery_level = await self._calculate_concept_mastery(
                    student_id, db_concept_id, is_correct
                )

                mastery_levels[db_concept_id] = mastery_level

                # 更新知识图谱中的掌握度关系
                await self._update_mastery_relationship(
                    student_id, db_concept_id, mastery_level
                )

            return mastery_levels

        except Exception as e:
            logger.error(f"分析概念掌握度失败: {e}")
            return {}
    
    async def _calculate_concept_mastery(
        self, 
        student_id: str, 
        concept_id: str, 
        current_correct: bool
    ) -> float:
        """计算学生对特定概念的掌握度"""
        
        # 获取学生在该概念相关题目上的历史表现
        query = """
        MATCH (student:Student {studentId: $student_id})-[attempt:ATTEMPTED]->(problem:Problem)
        MATCH (problem)-[:TESTS_CONCEPT]->(concept:Concept {kpId: $concept_id})
        RETURN attempt.isFullyCorrect as is_correct, count(*) as attempt_count
        """
        
        try:
            result = await self.client.driver.execute_query(
                query, student_id=student_id, concept_id=concept_id
            )
            
            correct_count = 0
            total_count = 0
            
            for record in result.records:
                if record['is_correct']:
                    correct_count += record['attempt_count']
                total_count += record['attempt_count']
            
            # 包含当前答题结果
            if current_correct:
                correct_count += 1
            total_count += 1
            
            # 计算掌握度 (正确率)
            mastery_level = correct_count / total_count if total_count > 0 else 0.0
            
            return round(mastery_level, 2)
            
        except Exception as e:
            logger.error(f"计算概念掌握度失败: {e}")
            return 0.0
    
    async def _update_mastery_relationship(
        self, 
        student_id: str, 
        concept_id: str, 
        mastery_level: float
    ):
        """更新学生对概念的掌握度关系"""
        
        query = """
        MERGE (student:Student {studentId: $student_id})
        MERGE (concept:Concept {kpId: $concept_id})
        MERGE (student)-[mastery:HAS_MASTERY_OF]->(concept)
        SET mastery.proficiency = $mastery_level,
            mastery.lastUpdated = datetime()
        RETURN mastery
        """
        
        try:
            await self.client.driver.execute_query(
                query,
                student_id=student_id,
                concept_id=concept_id,
                mastery_level=mastery_level
            )
            logger.info(f"掌握度关系已更新: {student_id} -> {concept_id}: {mastery_level}")
        except Exception as e:
            logger.error(f"更新掌握度关系失败: {e}")
    
    async def _generate_recommendations(
        self, 
        student_id: str, 
        misconceptions: List[Dict[str, str]], 
        mastery_analysis: Dict[str, float]
    ) -> List[str]:
        """生成学习建议"""
        
        recommendations = []
        
        # 基于易错点的建议
        if misconceptions:
            recommendations.append("建议重点复习以下易错概念:")
            for misconception in misconceptions:
                recommendations.append(f"- {misconception['description']}")
        
        # 基于掌握度的建议
        weak_concepts = [
            concept_id for concept_id, mastery in mastery_analysis.items() 
            if mastery < 0.6
        ]
        
        if weak_concepts:
            recommendations.append("建议加强练习以下知识点:")
            for concept_id in weak_concepts:
                recommendations.append(f"- {concept_id}")
        
        return recommendations

    async def _get_student_attempts_from_db(self, student_identifier: str) -> Optional[Dict[str, Any]]:
        """从数据库中获取学生的答题记录"""
        try:
            # 首先尝试通过学生ID查找
            student_query = """
            MATCH (s:Student)
            WHERE s.studentId = $identifier OR s.name = $identifier
            RETURN s.studentId as student_id, s.name as student_name
            LIMIT 1
            """

            result = await self.client.driver.execute_query(student_query, identifier=student_identifier)

            if not result.records:
                logger.warning(f"未找到学生: {student_identifier}")
                return None

            student_record = result.records[0]
            student_id = student_record['student_id']
            student_name = student_record['student_name']

            # 查询学生的答题记录
            attempts_query = """
            MATCH (s:Student {studentId: $student_id})-[att:ATTEMPTED]->(p:Problem)
            OPTIONAL MATCH (p)-[:OPTION_TARGETS]->(m:Misconception)
            RETURN p.problemId as problem_id,
                   p.content as problem_content,
                   p.correctAnswer as correct_answer,
                   att.selectedOptions as selected_options,
                   att.isFullyCorrect as is_fully_correct,
                   att.timestamp as timestamp,
                   collect(DISTINCT {
                       option_id: split(m.misconceptionId, '_')[-1],
                       description: m.description
                   }) as misconceptions
            ORDER BY att.timestamp DESC
            """

            attempts_result = await self.client.driver.execute_query(attempts_query, student_id=student_id)

            attempts = []
            for record in attempts_result.records:
                # 构建易错点映射
                misconception_map = {}
                for misc in record['misconceptions']:
                    if misc['option_id'] and misc['description']:
                        misconception_map[misc['option_id']] = misc['description']

                attempts.append({
                    'problem_id': record['problem_id'],
                    'problem_content': record['problem_content'],
                    'correct_answer': record['correct_answer'],
                    'selected_options': record['selected_options'],
                    'is_fully_correct': record['is_fully_correct'],
                    'timestamp': record['timestamp'],
                    'misconception_map': misconception_map
                })

            return {
                'student_id': student_id,
                'student_name': student_name,
                'attempts': attempts
            }

        except Exception as e:
            logger.error(f"查询学生答题记录失败: {e}")
            return None

    def _parse_llm_diagnostic_output(self, llm_output: str) -> Dict[str, Any]:
        """解析LLM诊断输出"""
        try:
            import json
            import re

            # 尝试提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有代码块，尝试直接解析
                json_str = llm_output.strip()

            # 解析JSON
            result = json.loads(json_str)

            # 验证必要字段
            required_fields = ['is_correct', 'confidence_score', 'mastery_level']
            for field in required_fields:
                if field not in result:
                    logger.warning(f"LLM输出缺少字段: {field}")
                    result[field] = 0.5  # 默认值

            logger.info(f"✅ LLM输出解析成功")
            return result

        except Exception as e:
            logger.error(f"❌ LLM输出解析失败: {e}")
            logger.debug(f"原始输出: {llm_output}")

            # 返回默认结果
            return {
                'is_correct': False,
                'confidence_score': 0.3,
                'cognitive_analysis': {
                    'reasoning_process': '解析失败，无法分析',
                    'knowledge_gaps': ['LLM输出解析错误'],
                    'cognitive_level': '未知'
                },
                'identified_misconceptions': [],
                'mastery_level': 0.3,
                'recommendations': ['请重新尝试或联系技术支持']
            }

# LangChain工具装饰器
@tool
async def analyze_student_from_database(
    student_id: str
) -> str:
    """
    从知识图谱数据库中分析学生的答题表现

    Args:
        student_id: 学生ID或学生姓名

    Returns:
        学生答题分析结果的JSON字符串
    """
    try:
        # 从客户端管理器获取Graphiti客户端
        from graphmasal.agents.client_manager import get_graphiti_client, is_graphiti_client_initialized

        logger.debug(f"尝试从数据库分析学生 {student_id} 的答题情况")

        if not is_graphiti_client_initialized():
            logger.error("Graphiti客户端未初始化")
            raise RuntimeError("Graphiti客户端未初始化")

        client = get_graphiti_client()
        diagnostic_tool = DiagnosticTool(client)

        # 查询学生的答题记录
        student_data = await diagnostic_tool._get_student_attempts_from_db(student_id)

        if not student_data:
            return json.dumps({
                'error': f'未找到学生 {student_id} 的答题记录',
                'student_id': student_id,
                'attempts_found': 0,
                'recommendations': ['请确认学生ID是否正确，或者该学生尚未有答题记录']
            }, ensure_ascii=False, indent=2)

        # 分析所有答题记录
        analysis_results = []
        overall_stats = {
            'total_attempts': 0,
            'correct_attempts': 0,
            'identified_misconceptions': [],
            'weak_concepts': [],
            'strong_concepts': []
        }

        # 用于收集掌握情况和易错点数据
        mastery_data = {}
        misconceptions_data = []

        for attempt in student_data['attempts']:
            result = await diagnostic_tool.analyze_problem_attempt(
                student_id=student_data['student_id'],
                problem_id=attempt['problem_id'],
                selected_options=attempt['selected_options'],
                correct_answer=attempt['correct_answer'],
                misconception_map=attempt.get('misconception_map', {})
            )
            analysis_results.append(result)

            # 更新整体统计
            overall_stats['total_attempts'] += 1
            if result['is_correct']:
                overall_stats['correct_attempts'] += 1

            overall_stats['identified_misconceptions'].extend(result['identified_misconceptions'])

            # 收集掌握情况数据
            if 'mastery_analysis' in result:
                mastery_data.update(result['mastery_analysis'])

            # 收集易错点数据
            misconceptions_data.extend(result['identified_misconceptions'])

        # 计算正确率
        accuracy = overall_stats['correct_attempts'] / overall_stats['total_attempts'] if overall_stats['total_attempts'] > 0 else 0

        # 保存数据到会话状态
        from graphmasal.agents.session_state import get_session_state_manager
        session_state = get_session_state_manager()

        # 保存诊断结果
        diagnostic_result = {
            'student_id': student_data['student_id'],
            'student_name': student_data['student_name'],
            'overall_accuracy': accuracy,
            'total_attempts': overall_stats['total_attempts'],
            'correct_attempts': overall_stats['correct_attempts'],
            'detailed_analysis': analysis_results
        }
        session_state.set_student_diagnostic_result(student_data['student_id'], diagnostic_result)

        # 保存掌握情况
        if mastery_data:
            session_state.set_student_mastery(student_data['student_id'], mastery_data)

        # 保存易错点
        if misconceptions_data:
            session_state.set_student_misconceptions(student_data['student_id'], misconceptions_data)

        return json.dumps({
            'student_id': student_data['student_id'],
            'student_name': student_data['student_name'],
            'overall_accuracy': round(accuracy, 2),
            'total_attempts': overall_stats['total_attempts'],
            'correct_attempts': overall_stats['correct_attempts'],
            'detailed_analysis': analysis_results,
            'summary': {
                'performance_level': '优秀' if accuracy >= 0.8 else '良好' if accuracy >= 0.6 else '需要提高',
                'main_misconceptions': list(set([m['description'] for m in overall_stats['identified_misconceptions']])),
                'recommendations': [
                    f"总体正确率为 {accuracy:.1%}",
                    f"共完成 {overall_stats['total_attempts']} 道题目",
                    "建议重点关注易错概念的学习" if overall_stats['identified_misconceptions'] else "继续保持良好的学习状态"
                ]
            }
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"从数据库分析学生答题情况失败: {e}")
        return json.dumps({
            'error': f'分析失败: {str(e)}',
            'student_id': student_id,
            'recommendations': ['请检查学生ID是否正确，或联系技术支持']
        }, ensure_ascii=False, indent=2)

@tool
async def diagnose_student_performance(
    student_id: str,
    problem_data: str  # JSON字符串格式的题目数据
) -> str:
    """
    诊断学生答题表现的工具
    
    Args:
        student_id: 学生ID
        problem_data: 包含题目信息和学生答案的JSON数据
    
    Returns:
        诊断结果的JSON字符串
    """
    try:
        # 从客户端管理器获取Graphiti客户端
        from graphmasal.agents.client_manager import get_graphiti_client, is_graphiti_client_initialized

        logger.debug(f"尝试获取Graphiti客户端，客户端初始化状态: {is_graphiti_client_initialized()}")

        if not is_graphiti_client_initialized():
            logger.error("Graphiti客户端未初始化")
            raise RuntimeError("Graphiti客户端未初始化")

        try:
            client = get_graphiti_client()
            logger.debug(f"成功获取Graphiti客户端: {client is not None}")
        except RuntimeError as e:
            logger.error(f"获取Graphiti客户端失败: {e}")
            raise RuntimeError("Graphiti客户端未初始化")
        
        diagnostic_tool = DiagnosticTool(client)
        
        # 解析题目数据
        data = json.loads(problem_data)
        
        result = await diagnostic_tool.analyze_problem_attempt(
            student_id=student_id,
            problem_id=data['problem_id'],
            selected_options=data['selected_options'],
            correct_answer=data['correct_answer'],
            misconception_map=data.get('misconception_map', {}),
            concept_id=data.get('concept_id')  # 新增概念ID参数
        )
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"诊断工具执行失败: {e}")
        return json.dumps({
            'error': f'诊断失败: {str(e)}',
            'is_correct': False,
            'identified_misconceptions': [],
            'mastery_analysis': {},
            'recommendations': ['请重新尝试或联系技术支持']
        }, ensure_ascii=False)

    def _fallback_analysis(
        self,
        selected_options: List[str],
        correct_answer: List[str],
        misconception_map: Dict[str, str]
    ) -> Dict[str, Any]:
        """LLM失败时的回退分析"""

        is_correct = set(selected_options) == set(correct_answer)

        # 基本易错点识别
        identified_misconceptions = []
        if not is_correct:
            wrong_options = [opt for opt in selected_options if opt not in correct_answer]
            for wrong_option in wrong_options:
                if wrong_option in misconception_map:
                    identified_misconceptions.append({
                        'problem_id': 0,
                        'option': wrong_option,
                        'description': misconception_map[wrong_option],
                        'severity': '中'
                    })

        return {
            'is_correct': is_correct,
            'confidence_score': 0.8 if is_correct else 0.4,
            'cognitive_analysis': {
                'reasoning_process': '基于规则的基本分析',
                'knowledge_gaps': [desc['description'] for desc in identified_misconceptions],
                'cognitive_level': '应用'
            },
            'identified_misconceptions': identified_misconceptions,
            'mastery_level': 0.8 if is_correct else 0.3,
            'recommendations': ['建议加强相关知识点练习'] if not is_correct else ['继续保持']
        }

    async def _llm_mastery_analysis(
        self,
        student_id: str,
        problem_id: int,
        llm_analysis: Dict[str, Any],
        concept_id: str = None,
        strict_mode: bool = False
    ) -> Dict[str, float]:
        """基于LLM分析结果进行掌握度分析"""

        mastery_levels = {}

        # 从LLM分析中获取掌握度
        llm_mastery_level = llm_analysis.get('mastery_level', 0.5)

        if concept_id:
            # 从数据库查询concept信息
            concept_info = await self._get_concept_from_database(concept_id)

            if concept_info:
                mastery_levels[concept_id] = llm_mastery_level

                # 更新知识图谱中的掌握度关系
                await self._update_mastery_relationship(
                    student_id, concept_id, llm_mastery_level
                )

                logger.info(f"📊 LLM掌握度分析 - 学生 {student_id}, 概念 {concept_info['name']}: {llm_mastery_level:.3f}")
            else:
                logger.warning(f"⚠️ 概念 {concept_id} 在数据库中不存在")

        return mastery_levels

    async def _get_concept_from_database(self, concept_id: str) -> Dict[str, Any]:
        """从数据库查询concept信息"""
        query = """
        MATCH (c:Concept {kpId: $concept_id})
        RETURN c.kpId as concept_id, c.name as name, c.level as level
        """

        try:
            result = await self.client.driver.execute_query(query, concept_id=concept_id)

            if result.records:
                record = result.records[0]
                return {
                    'concept_id': record['concept_id'],
                    'name': record['name'],
                    'level': record['level']
                }
            else:
                return {}

        except Exception as e:
            logger.error(f"查询概念信息失败: {e}")
            return {}
