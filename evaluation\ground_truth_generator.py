#!/usr/bin/env python3
"""
标准答案生成器
使用完整的题目数据集生成标准的诊断和规划答案
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Set
from datetime import datetime
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.config import settings
from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# 导入planning_tool中的路径规划器
from graphmasal.agents.tools.planning_tool import OptimalPathPlanner

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_graphiti_client() -> Graphiti:
    """创建 Graphiti 客户端"""
    neo4j_uri = "bolt://localhost:7687"
    neo4j_user = "neo4j"
    neo4j_password = "12345678"
    
    embedding_api_key = settings.EMBEDDING_API_KEY or settings.OPENAI_API_KEY
    embedding_base_url = settings.EMBEDDING_BASE_URL or settings.OPENAI_BASE_URL

    llm_config = LLMConfig(
        api_key=settings.OPENAI_API_KEY,
        model=settings.OPENAI_MODEL,
        small_model=settings.OPENAI_MODEL,
        base_url=settings.OPENAI_BASE_URL,
    )

    embedder_config = OpenAIEmbedderConfig(
        api_key=embedding_api_key,
        embedding_model=settings.EMBEDDING_MODEL,
        base_url=embedding_base_url,
    )

    llm_client = OpenAIGenericClient(config=llm_config)
    embedder = OpenAIEmbedder(config=embedder_config)
    cross_encoder = OpenAIRerankerClient(config=llm_config)

    return Graphiti(
        neo4j_uri,
        neo4j_user,
        neo4j_password,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

class GroundTruthGenerator:
    """标准答案生成器"""
    
    def __init__(self, client: Graphiti):
        self.client = client
        self.logger = logger
        self.path_planner = OptimalPathPlanner(client)
        # 添加答题记录保存目录
        self.answer_records_dir = Path("evaluation/student_answer_records")
        self.answer_records_dir.mkdir(parents=True, exist_ok=True)
        # 用于收集所有学生的答题记录
        self.all_student_records = {}
    
    def load_problems_data(self, file_path: Path) -> List[Dict[str, Any]]:
        """加载完整的题目数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.logger.info(f"成功加载题目数据: {len(data)} 道题目")
            return data
        except Exception as e:
            self.logger.error(f"加载题目数据失败: {e}")
            return []
    
    async def verify_knowledge_points_in_graph(self, knowledge_points: List[str]) -> Dict[str, bool]:
        """验证知识点是否在知识图谱中存在"""
        if not knowledge_points:
            return {}
        
        query = """
        UNWIND $kp_ids as kp_id
        OPTIONAL MATCH (c:Concept {kpId: kp_id})
        RETURN kp_id, c IS NOT NULL as exists, c.name as name
        """
        
        try:
            result = await self.client.driver.execute_query(query, kp_ids=knowledge_points)
            verification = {}
            
            for record in result.records:
                kp_id = record['kp_id']
                exists = record['exists']
                name = record['name']
                verification[kp_id] = {
                    'exists': exists,
                    'name': name if exists else None
                }
            
            return verification
            
        except Exception as e:
            self.logger.error(f"验证知识点失败: {e}")
            return {kp: {'exists': False, 'name': None} for kp in knowledge_points}
    
    async def generate_student_diagnostic_ground_truth(
        self,
        student_id: str,
        student_problems: List[Dict[str, Any]],
        knowledge_verification: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        为单个学生生成认知诊断标准答案
        并行处理所有答题记录，汇总给出最终诊断

        Args:
            student_id: 学生ID
            student_problems: 学生的答题记录列表
            knowledge_verification: 知识点验证信息

        Returns:
            学生的认知诊断标准答案
        """
        try:
            # 收集学生答题记录供盲测使用
            self._collect_student_answer_records(student_id, student_problems)

            # 并行处理所有答题记录
            problem_results = await asyncio.gather(
                *[self._process_single_problem(problem, knowledge_verification)
                  for problem in student_problems],
                return_exceptions=True
            )

            # 过滤掉异常结果
            valid_results = [result for result in problem_results
                           if not isinstance(result, Exception)]

            if not valid_results:
                self.logger.warning(f"学生 {student_id} 没有有效的答题记录")
                return self._create_empty_diagnostic(student_id)

            # 汇总诊断结果
            diagnostic_summary = self._aggregate_diagnostic_results(
                student_id, valid_results, knowledge_verification
            )

            return diagnostic_summary

        except Exception as e:
            self.logger.error(f"生成学生 {student_id} 诊断标准答案失败: {e}")
            return self._create_empty_diagnostic(student_id)

    async def _process_single_problem(
        self,
        problem: Dict[str, Any],
        knowledge_verification: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        处理单个答题记录

        Args:
            problem: 答题记录
            knowledge_verification: 知识点验证信息

        Returns:
            单个问题的处理结果
        """
        problem_id = problem.get('question_id', 'unknown')
        student_answer = problem.get('student_answer', [])
        correct_answer = problem.get('correct_answer', 'A')
        linked_kp_ids = problem.get('linked_kp_ids', [])
        difficulty = problem.get('difficulty', 5.0)

        # 判断答题是否正确
        is_correct = set(student_answer) == {correct_answer}

        # 计算每个知识点的掌握度
        kp_mastery = {}
        for kp_id in linked_kp_ids:
            verification = knowledge_verification.get(kp_id, {})
            if verification.get('exists', False):
                # 基于答题正确性和题目难度计算掌握度
                base_mastery = 0.85 if is_correct else 0.25
                difficulty_factor = (10 - difficulty) / 10
                mastery_level = base_mastery + (difficulty_factor * 0.15 if is_correct else -difficulty_factor * 0.15)
                mastery_level = max(0.0, min(1.0, mastery_level))

                kp_mastery[kp_id] = {
                    'mastery_level': round(mastery_level, 3),
                    'concept_name': verification.get('name', kp_id),
                    'is_correct': is_correct,
                    'difficulty': difficulty
                }

        return {
            'problem_id': problem_id,
            'is_correct': is_correct,
            'difficulty': difficulty,
            'kp_mastery': kp_mastery,
            'linked_kp_ids': linked_kp_ids
        }

    def _aggregate_diagnostic_results(
        self,
        student_id: str,
        problem_results: List[Dict[str, Any]],
        knowledge_verification: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        汇总多个答题记录的诊断结果

        Args:
            student_id: 学生ID
            problem_results: 所有问题的处理结果
            knowledge_verification: 知识点验证信息

        Returns:
            汇总的诊断结果
        """
        # 收集所有知识点的掌握度数据
        kp_data = {}
        total_problems = len(problem_results)
        correct_problems = sum(1 for result in problem_results if result['is_correct'])

        # 汇总每个知识点的掌握度
        for result in problem_results:
            for kp_id, kp_info in result['kp_mastery'].items():
                if kp_id not in kp_data:
                    kp_data[kp_id] = {
                        'mastery_levels': [],
                        'concept_name': kp_info['concept_name'],
                        'correct_count': 0,
                        'total_count': 0
                    }

                kp_data[kp_id]['mastery_levels'].append(kp_info['mastery_level'])
                kp_data[kp_id]['total_count'] += 1
                if kp_info['is_correct']:
                    kp_data[kp_id]['correct_count'] += 1

        # 计算最终掌握度和分类
        mastered_concepts = []
        not_mastered_concepts = []

        for kp_id, data in kp_data.items():
            # 使用加权平均计算最终掌握度
            avg_mastery = sum(data['mastery_levels']) / len(data['mastery_levels'])
            accuracy_rate = data['correct_count'] / data['total_count']

            # 综合掌握度和正确率
            final_mastery = (avg_mastery * 0.7 + accuracy_rate * 0.3)
            final_mastery = round(final_mastery, 3)

            concept_info = {
                'concept_id': kp_id,
                'concept_name': data['concept_name'],
                'mastery_level': final_mastery,
                'accuracy_rate': round(accuracy_rate, 3),
                'problem_count': data['total_count']
            }

            # 分类：掌握度 >= 0.6 为掌握，< 0.6 为未掌握
            if final_mastery >= 0.6:
                mastered_concepts.append(concept_info)
            else:
                not_mastered_concepts.append(concept_info)

        # 按掌握度排序
        mastered_concepts.sort(key=lambda x: x['mastery_level'], reverse=True)
        not_mastered_concepts.sort(key=lambda x: x['mastery_level'])

        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': '2.0.0',
                'total_problems': total_problems,
                'correct_problems': correct_problems
            },
            'diagnostic_result': {
                'overall_accuracy': round(correct_problems / total_problems, 3) if total_problems > 0 else 0.0,
                'mastered_concepts': mastered_concepts,
                'not_mastered_concepts': not_mastered_concepts,
                'total_concepts': len(kp_data),
                'mastered_count': len(mastered_concepts),
                'not_mastered_count': len(not_mastered_concepts),
                'diagnostic_confidence': 0.95
            }
        }

    def _create_empty_diagnostic(self, student_id: str) -> Dict[str, Any]:
        """创建空的诊断结果"""
        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': '2.0.0',
                'total_problems': 0,
                'correct_problems': 0
            },
            'diagnostic_result': {
                'overall_accuracy': 0.0,
                'mastered_concepts': [],
                'not_mastered_concepts': [],
                'total_concepts': 0,
                'mastered_count': 0,
                'not_mastered_count': 0,
                'diagnostic_confidence': 0.0
            }
        }
    

    
    async def generate_planning_ground_truth(
        self,
        student_id: str,
        student_mastery: Dict[str, float],
        target_concept: str = None
    ) -> Dict[str, Any]:
        """
        使用planning_tool中的方法生成规划标准答案

        Args:
            student_id: 学生ID
            student_mastery: 学生掌握情况
            target_concept: 目标概念（可选，如果不提供则生成最优路径）
        """

        try:
            # 创建路径规划器
            planner = OptimalPathPlanner(self.client)

            # 模拟将学生掌握情况写入数据库（用于规划器获取）
            await self._simulate_student_mastery_in_db(student_id, student_mastery)

            if target_concept:
                # 单目标路径规划
                self.logger.info(f"为学生 {student_id} 生成到 '{target_concept}' 的学习路径标准答案")
                planning_result = await planner.generate_learning_path(
                    student_id=student_id,
                    target_concept=target_concept,
                    max_path_length=10
                )
            else:
                # 多目标最优路径规划
                self.logger.info(f"为学生 {student_id} 生成最优学习路径标准答案")
                planning_result = await planner.generate_optimal_learning_paths(
                    student_id=student_id,
                    max_path_length=10
                )

            # 转换为评测标准格式
            standardized_result = self._convert_planning_result_to_evaluation_format(
                planning_result, student_id, target_concept, student_mastery
            )

            return standardized_result

        except Exception as e:
            self.logger.error(f"生成规划标准答案失败: {e}")
            return {
                'evaluation_metadata': {
                    'student_id': student_id,
                    'target_concept': target_concept,
                    'timestamp': datetime.now().isoformat(),
                    'generator_version': '2.0.0',
                    'data_source': 'planning_tool_integration',
                    'error': str(e)
                },
                'planning_result': {
                    'paths_subset': [],
                    'points_subset': [target_concept] if target_concept else [],
                    'total_concepts_to_learn': 1 if target_concept else 0,
                    'estimated_time': 30 if target_concept else 0,
                    'planning_confidence': 0.1,
                    'plan_type': 'error'
                }
            }

    async def _simulate_student_mastery_in_db(self, student_id: str, student_mastery: Dict[str, float]) -> None:
        """
        模拟将学生掌握情况写入数据库，供规划器使用

        注意：这是为了生成标准答案而临时创建的数据，实际评测时不会有这些数据
        """
        try:
            # 创建或更新学生节点
            create_student_query = """
            MERGE (s:Student {studentId: $student_id})
            SET s.name = $student_name,
                s.createdAt = datetime(),
                s.isGroundTruthStudent = true
            """

            await self.client.driver.execute_query(
                create_student_query,
                student_id=student_id,
                student_name=f"标准答案学生_{student_id}"
            )

            # 为每个知识点创建掌握度关系 - 修复关系类型和属性名
            for concept_id, mastery_level in student_mastery.items():
                mastery_query = """
                MATCH (s:Student {studentId: $student_id})
                MATCH (c:Concept {kpId: $concept_id})
                MERGE (s)-[m:HAS_MASTERY_OF]->(c)
                SET m.proficiency = $mastery_level,
                    m.lastUpdated = datetime(),
                    m.isGroundTruth = true
                """

                await self.client.driver.execute_query(
                    mastery_query,
                    student_id=student_id,
                    concept_id=concept_id,
                    mastery_level=mastery_level
                )

            self.logger.debug(f"已为学生 {student_id} 模拟了 {len(student_mastery)} 个知识点的掌握情况")

        except Exception as e:
            self.logger.error(f"模拟学生掌握情况失败: {e}")
            # 不抛出异常，让规划器尝试其他方式获取数据

    def _convert_planning_result_to_evaluation_format(
        self,
        planning_result: Dict[str, Any],
        student_id: str,
        target_concept: str,
        student_mastery: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        将planning_tool的输出转换为评测标准格式
        参考 run_agent_planning 的处理逻辑，确保输出格式一致
        """
        try:
            # 提取路径信息
            paths_subset = []
            points_subset = []

            plan_type = planning_result.get('plan_type', 'unknown')

            # 统一处理所有类型的规划结果，参考 run_agent_planning 的逻辑
            if plan_type == 'multi_source_multi_sink_optimal':
                # 多目标优化路径
                learning_paths = planning_result.get('learning_paths', [])
                independent_weak_concepts = planning_result.get('independent_weak_concepts', [])

                # 处理学习路径 - 参考 run_agent_planning 的逻辑
                path_counter = 0
                for path in learning_paths:
                    if isinstance(path, dict):
                        path_id = path.get('path_id', '')

                        # 过滤掉独立概念路径，直接跳过
                        if path_id == 'independent_concepts':
                            continue

                        # 提取路径中的概念
                        path_concepts = []

                        # 尝试从不同字段获取概念
                        if 'steps' in path:
                            for step in path.get('steps', []):
                                if isinstance(step, dict) and 'concept_id' in step:
                                    path_concepts.append(step['concept_id'])
                        elif 'concepts' in path:
                            concepts = path.get('concepts', [])
                            for concept in concepts:
                                if isinstance(concept, dict):
                                    concept_id = concept.get('concept_id', concept.get('kpId', str(concept)))
                                else:
                                    concept_id = str(concept)
                                path_concepts.append(concept_id)

                        # 只有当路径包含多个概念时才作为真正的路径
                        if len(path_concepts) > 1:
                            # 构建边列表（相邻概念之间的连接）
                            edges = []
                            for j in range(len(path_concepts) - 1):
                                edges.append([path_concepts[j], path_concepts[j + 1]])

                            # 构建符合评测格式的路径结构
                            paths_subset.append({
                                'path_id': path_id if path_id and path_id != 'independent_concepts' else f'optimal_path_{path_counter}',
                                'nodes': path_concepts,  # 路径中的节点列表
                                'edges': edges,  # 路径中的边列表
                                'sequence': path_concepts,  # 学习顺序序列
                                'concepts': path_concepts,  # 概念列表（用于相似度计算）
                                'estimated_time': path.get('estimated_time', len(path_concepts) * 25),
                                'priority': 'medium'  # 默认优先级
                            })
                            path_counter += 1
                        elif len(path_concepts) == 1:
                            # 单个概念应该放在 points_subset 中
                            concept_id = path_concepts[0]
                            if concept_id not in points_subset:
                                points_subset.append(concept_id)

                # 处理独立薄弱概念
                for concept in independent_weak_concepts:
                    if isinstance(concept, dict):
                        concept_id = concept.get('concept_id', concept.get('kpId', str(concept)))
                    else:
                        concept_id = str(concept)
                    if concept_id and concept_id not in points_subset:
                        points_subset.append(concept_id)

            elif plan_type in ['direct_path', 'independent_concept']:
                # 单目标路径 - 将结果合并到统一格式中
                learning_path = planning_result.get('learning_path', [])

                if learning_path:
                    path_concepts = []
                    for step in learning_path:
                        if isinstance(step, dict):
                            concept_id = step.get('concept_id', step.get('kpId', str(step)))
                        else:
                            concept_id = str(step)
                        path_concepts.append(concept_id)

                    if plan_type == 'independent_concept' or len(path_concepts) == 1:
                        # 独立概念或单个概念作为点，合并到 points_subset
                        for concept_id in path_concepts:
                            if concept_id not in points_subset:
                                points_subset.append(concept_id)
                    else:
                        # 有依赖的路径（多个概念），添加到 paths_subset
                        edges = []
                        for j in range(len(path_concepts) - 1):
                            edges.append([path_concepts[j], path_concepts[j + 1]])

                        paths_subset.append({
                            'path_id': f'target_path_to_{target_concept}',
                            'nodes': path_concepts,
                            'edges': edges,
                            'sequence': path_concepts,
                            'concepts': path_concepts,  # 概念列表（用于相似度计算）
                            'estimated_time': planning_result.get('estimated_time', len(path_concepts) * 25),
                            'priority': 'high'
                        })

            # 计算总学习概念数 - 参考 run_agent_planning 的计算方式
            total_concepts = len(set([c for path in paths_subset for c in path.get('concepts', [])] + points_subset))

            # 统一返回格式，参考 run_agent_planning 的结构
            return {
                'evaluation_metadata': {
                    'student_id': student_id,
                    'target_concept': target_concept,
                    'timestamp': datetime.now().isoformat(),
                    'generator_version': '2.0.0',
                    'plan_type': 'multi_source_multi_sink_optimal',  # 统一使用此类型
                    'planning_type': 'multi_target_optimal'
                },
                'planning_result': {
                    'paths_subset': paths_subset,
                    'points_subset': points_subset,
                    'total_concepts_to_learn': total_concepts,
                    'estimated_time': planning_result.get('estimated_time', total_concepts * 25),
                    'planning_confidence': 0.95
                }
            }

        except Exception as e:
            self.logger.error(f"转换规划结果格式失败: {e}")
            return {
                'evaluation_metadata': {
                    'student_id': student_id,
                    'target_concept': target_concept,
                    'timestamp': datetime.now().isoformat(),
                    'generator_version': '2.0.0',
                    'conversion_error': str(e)
                },
                'planning_result': {
                    'paths_subset': [],
                    'points_subset': [target_concept] if target_concept else [],
                    'total_concepts_to_learn': 1 if target_concept else 0,
                    'estimated_time': 30,
                    'planning_confidence': 0.1,
                    'plan_type': 'conversion_error'
                }
            }
    
    async def generate_ground_truth_dataset(
        self, 
        problems_file: Path, 
        output_dir: Path,
        num_students: int = 20
    ) -> None:
        """生成完整的标准答案数据集"""
        
        self.logger.info("=" * 60)
        self.logger.info("🎯 开始生成标准答案数据集")
        self.logger.info("=" * 60)
        
        # 加载题目数据
        problems = self.load_problems_data(problems_file)
        if not problems:
            return
        
        # 验证所有知识点
        all_kp_ids = set()
        for problem in problems:
            all_kp_ids.update(problem.get('linked_kp_ids', []))
        
        self.logger.info(f"验证 {len(all_kp_ids)} 个知识点...")
        knowledge_verification = await self.verify_knowledge_points_in_graph(list(all_kp_ids))
        
        valid_kp_count = sum(1 for v in knowledge_verification.values() if v.get('exists', False))
        self.logger.info(f"有效知识点: {valid_kp_count}/{len(all_kp_ids)}")
        
        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成标准答案
        diagnostic_ground_truth = []
        planning_ground_truth = []

        # 模拟学生答题和生成标准答案
        import random
        random.seed(1)  # 确保可重现 #42 1 0.791
        #435753 1 0.511
        #433 1 0.791

        self.logger.info(f"开始为 {num_students} 个学生生成标准答案...")

        for student_idx in range(num_students):
            student_id = f"student_{student_idx+1:03d}"

            # 为每个学生选择一些题目并模拟答题
            selected_problems = random.sample(problems, min(15, len(problems)))
            student_problems = []

            for problem in selected_problems:
                # 模拟学生答案
                correct_answer = problem.get('correct_answer', 'A')
                difficulty = problem.get('difficulty', 5.0)

                # 基于难度决定答对概率
                correct_prob = max(0.1, 1.0 - difficulty / 10.0)
                is_correct = random.random() < correct_prob

                if is_correct:
                    student_answer = [correct_answer]
                else:
                    # 从错误选项中选择
                    misconception_map = problem.get('misconception_map', {})
                    wrong_options = [opt for opt in misconception_map.keys() if opt != correct_answer]
                    if wrong_options:
                        student_answer = [random.choice(wrong_options)]
                    else:
                        all_options = ['A', 'B', 'C', 'D']
                        wrong_options = [opt for opt in all_options if opt != correct_answer]
                        student_answer = [random.choice(wrong_options)]

                # 添加到学生答题记录
                student_problem = problem.copy()
                student_problem['student_answer'] = student_answer
                student_problems.append(student_problem)

            # 生成该学生的认知诊断标准答案（并行处理所有答题记录）
            diagnostic_gt = await self.generate_student_diagnostic_ground_truth(
                student_id, student_problems, knowledge_verification
            )
            diagnostic_ground_truth.append(diagnostic_gt)

            # 从诊断结果中提取掌握度用于规划
            mastered_concepts = diagnostic_gt['diagnostic_result']['mastered_concepts']
            not_mastered_concepts = diagnostic_gt['diagnostic_result']['not_mastered_concepts']

            # 构建掌握度字典
            avg_mastery = {}
            for concept in mastered_concepts:
                avg_mastery[concept['concept_id']] = concept['mastery_level']
            for concept in not_mastered_concepts:
                avg_mastery[concept['concept_id']] = concept['mastery_level']

            # 生成规划标准答案
            if avg_mastery:
                # 生成多目标最优规划标准答案（统一格式）
                optimal_planning_gt = await self.generate_planning_ground_truth(
                    student_id, avg_mastery, target_concept=None
                )
                optimal_planning_gt['evaluation_metadata']['planning_type'] = 'multi_target_optimal'
                planning_ground_truth.append(optimal_planning_gt)

            self.logger.info(f"完成学生 {student_id} 的标准答案生成")
        
        # 保存标准答案
        diagnostic_file = output_dir / 'diagnostic_ground_truth.json'
        planning_file = output_dir / 'planning_ground_truth.json'
        
        with open(diagnostic_file, 'w', encoding='utf-8') as f:
            json.dump(diagnostic_ground_truth, f, ensure_ascii=False, indent=2)
        
        with open(planning_file, 'w', encoding='utf-8') as f:
            json.dump(planning_ground_truth, f, ensure_ascii=False, indent=2)

        # 保存所有学生的答题记录供盲测使用
        self.save_all_student_records()

        # 清理临时的标准答案学生数据
        await self._cleanup_ground_truth_students()

        self.logger.info(f"✅ 标准答案生成完成:")
        self.logger.info(f"  诊断标准答案: {len(diagnostic_ground_truth)} 个")
        self.logger.info(f"  规划标准答案: {len(planning_ground_truth)} 个")
        self.logger.info(f"  学生答题记录: {len(self.all_student_records)} 个学生")
        self.logger.info(f"  保存位置: {output_dir}")
        self.logger.info("=" * 60)

    async def _cleanup_ground_truth_students(self) -> None:
        """清理临时创建的标准答案学生数据"""
        try:
            cleanup_query = """
            MATCH (s:Student {isGroundTruthStudent: true})
            DETACH DELETE s
            """

            await self.client.driver.execute_query(cleanup_query)
            self.logger.info("已清理临时的标准答案学生数据")

        except Exception as e:
            self.logger.warning(f"清理临时学生数据失败: {e}")
            # 不抛出异常，因为这不影响标准答案的生成

    def _collect_student_answer_records(self, student_id: str, student_problems: List[Dict[str, Any]]) -> None:
        """
        收集学生答题记录供盲测使用

        Args:
            student_id: 学生ID
            student_problems: 学生的答题记录列表
        """
        try:
            # 构造答题记录数据
            answer_records = {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'total_problems': len(student_problems),
                'problems': []
            }

            for problem in student_problems:
                # 提取关键信息，正确映射字段名
                linked_kp_ids = problem.get('linked_kp_ids', [])
                concept_id = linked_kp_ids[0] if linked_kp_ids else ''
                concept_hint = f"涉及知识点: {', '.join(linked_kp_ids[:2])}" if linked_kp_ids else ''

                problem_record = {
                    'problem_id': str(problem.get('question_id', '')),  # question_id -> problem_id
                    'concept_id': concept_id,  # 使用第一个知识点作为主要概念
                    'concept_hint': concept_hint,  # 生成概念提示
                    'student_answer': problem.get('student_answer', []),
                    'correct_answer': problem.get('correct_answer', ''),
                    'difficulty': problem.get('difficulty', 5.0),
                    'is_correct': None  # 这个将在盲测时计算
                }

                # 计算是否正确（用于验证，但盲测时不使用）
                student_answer = problem.get('student_answer', [])
                correct_answer = problem.get('correct_answer', '')
                if isinstance(student_answer, list) and len(student_answer) > 0:
                    problem_record['is_correct'] = student_answer[0] == correct_answer
                else:
                    problem_record['is_correct'] = False

                answer_records['problems'].append(problem_record)

            # 收集到内存中，稍后统一保存
            self.all_student_records[student_id] = answer_records

            self.logger.info(f"已收集学生 {student_id} 的答题记录 ({len(student_problems)} 道题目)")

        except Exception as e:
            self.logger.error(f"收集学生 {student_id} 答题记录失败: {e}")

    def save_all_student_records(self) -> None:
        """
        将所有学生的答题记录保存到单个文件
        """
        try:
            if not self.all_student_records:
                self.logger.warning("没有收集到任何学生答题记录")
                return

            # 构造完整的答题记录数据
            complete_records = {
                'metadata': {
                    'total_students': len(self.all_student_records),
                    'generation_timestamp': datetime.now().isoformat(),
                    'description': '所有学生的答题记录，用于盲测评估'
                },
                'students': self.all_student_records
            }

            # 保存到单个文件
            records_file = self.answer_records_dir / "all_students_answer_records.json"
            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(complete_records, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ 已保存所有 {len(self.all_student_records)} 个学生的答题记录到: {records_file}")

            # 清空内存中的记录
            self.all_student_records.clear()

        except Exception as e:
            self.logger.error(f"保存所有学生答题记录失败: {e}")

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='生成标准答案数据集')
    parser.add_argument('--problems-file', 
                       default='data/start_0_end_200_new_data.json',
                       help='完整题目数据文件路径')
    parser.add_argument('--output-dir',
                       default='evaluation/ground_truth',
                       help='标准答案输出目录')
    parser.add_argument('--num-students', type=int, default=20,
                       help='生成的学生数量')
    
    args = parser.parse_args()
    
    # 验证输入文件
    problems_file = Path(args.problems_file)
    if not problems_file.exists():
        logger.error(f"题目数据文件不存在: {problems_file}")
        return
    
    try:
        # 验证配置
        settings.validate()
        
        # 创建客户端
        client = create_graphiti_client()
        
        # 创建生成器
        generator = GroundTruthGenerator(client)
        
        # 生成标准答案
        output_dir = Path(args.output_dir)
        await generator.generate_ground_truth_dataset(
            problems_file, output_dir, args.num_students
        )
        
        print(f"\n✅ 标准答案数据集生成完成！")
        print(f"📁 输出目录: {output_dir}")
        
    except Exception as e:
        logger.error(f"生成标准答案失败: {e}")
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
