#!/usr/bin/env python3
"""
完整盲测评测器
使用指定文件夹的标准答案进行盲测，不提供linked_kp_ids和misconception_map
由智能体进行推断并给出认知诊断以及路径规划
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime
import numpy as np

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.config import settings
from graphmasal.main import TutorSystem
from graphmasal.models.state import StudentInfo
from graphmasal.agents.tutor_agent import TutorAgent
from evaluation.evaluators.path_similarity_evaluator import PathSimilarityEvaluator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteBlindTestEvaluator:
    """完整盲测评测器"""
    
    def __init__(self, results_dir: str = "evaluation/blind_test_results"):
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.tutor_system = None
        self.tutor_agent = None
        self.logger = logger
        # 初始化路径相似度评测器，使用评测方案.md中的权重设置
        self.path_similarity_evaluator = PathSimilarityEvaluator(
            node_weight=0.4,      # 节点重合度权重
            edge_weight=0.3,      # 边重合度权重
            sequence_weight=0.3,  # 顺序相似度权重
            paths_weight=0.7,     # 路径子集权重
            points_weight=0.3     # 独立点子集权重
        )
    
    async def setup_system(self):
        """设置智能体系统"""
        try:
            self.logger.info("开始设置智能体系统...")

            # 确保环境变量正确设置（对豆包等第三方API很重要）
            import os
            os.environ['OPENAI_API_KEY'] = settings.OPENAI_API_KEY
            if settings.OPENAI_BASE_URL:
                os.environ['OPENAI_BASE_URL'] = settings.OPENAI_BASE_URL
            self.logger.info("环境变量设置完成")

            self.tutor_system = TutorSystem()
            self.logger.info("TutorSystem创建成功")

            client = self.tutor_system._create_graphiti_client()
            self.logger.info("Graphiti客户端创建成功")

            self.tutor_agent = TutorAgent(client)
            self.logger.info("智能体系统设置完成 - 将使用真实AI模型")
        except Exception as e:
            self.logger.error(f"智能体系统设置失败: {e}")
            self.logger.info("将使用模拟模式运行盲测评估")
            self.tutor_agent = None

            # 打印详细的错误信息用于调试
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def load_ground_truth(self, ground_truth_dir: str) -> Tuple[List[Dict], List[Dict]]:
        """加载标准答案文件"""
        ground_truth_path = Path(ground_truth_dir)
        
        # 加载诊断标准答案
        diagnostic_file = ground_truth_path / "diagnostic_ground_truth.json"
        # diagnostic_file = ground_truth_path / "plan1.json"
        if not diagnostic_file.exists():
            raise FileNotFoundError(f"诊断标准答案文件不存在: {diagnostic_file}")
        
        with open(diagnostic_file, 'r', encoding='utf-8') as f:
            diagnostic_ground_truth = json.load(f)
        
        # 加载规划标准答案
        planning_file = ground_truth_path / "planning_ground_truth.json"
        # planning_file = ground_truth_path / "plan1.json"
        if not planning_file.exists():
            raise FileNotFoundError(f"规划标准答案文件不存在: {planning_file}")
        
        with open(planning_file, 'r', encoding='utf-8') as f:
            planning_ground_truth = json.load(f)
        
        self.logger.info(f"加载标准答案: 诊断 {len(diagnostic_ground_truth)} 个, 规划 {len(planning_ground_truth)} 个")
        return diagnostic_ground_truth, planning_ground_truth
    
    def create_blind_test_cases(self, diagnostic_ground_truth: List[Dict]) -> List[Dict]:
        """从诊断标准答案创建盲测用例（移除linked_kp_ids和misconception_map）"""
        blind_test_cases = []
        
        for diag_gt in diagnostic_ground_truth:
            student_id = diag_gt.get('evaluation_metadata', {}).get('student_id', 'unknown')
            
            # 创建盲测用例 - 只提供基本的学生答题信息
            test_case = {
                'student_id': student_id,
                'test_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'test_type': 'blind_test',
                    'original_total_problems': diag_gt.get('evaluation_metadata', {}).get('total_problems', 0),
                    'original_correct_problems': diag_gt.get('evaluation_metadata', {}).get('correct_problems', 0)
                },
                'simulated_problems': self._create_simulated_problems(diag_gt)
            }
            
            blind_test_cases.append(test_case)
        
        self.logger.info(f"创建盲测用例: {len(blind_test_cases)} 个")
        return blind_test_cases
    
    def _create_simulated_problems(self, diag_gt: Dict) -> List[Dict]:
        """基于诊断标准答案创建模拟的答题记录"""
        problems = []
        mastered_concepts = diag_gt.get('diagnostic_result', {}).get('mastered_concepts', [])
        not_mastered_concepts = diag_gt.get('diagnostic_result', {}).get('not_mastered_concepts', [])

        problem_id = 1

        # 为掌握的知识点创建答对的题目
        for concept in mastered_concepts:
            for _ in range(concept.get('problem_count', 1)):
                problem = {
                    'problem_id': f'blind_test_{problem_id}',
                    'student_answer': ['A'],
                    'correct_answer': 'A',
                    'concept_hint': concept.get('concept_name', ''),
                    'concept_id': concept.get('concept_id', ''),  # 保存完整的概念ID
                    'is_correct': True
                    # 故意不包含: linked_kp_ids, misconception_map
                }
                problems.append(problem)
                problem_id += 1

        # 为未掌握的知识点创建答错的题目
        for concept in not_mastered_concepts:
            for _ in range(concept.get('problem_count', 1)):
                problem = {
                    'problem_id': f'blind_test_{problem_id}',
                    'student_answer': ['B'],
                    'correct_answer': 'A',
                    'concept_hint': concept.get('concept_name', ''),
                    'concept_id': concept.get('concept_id', ''),  # 保存完整的概念ID
                    'is_correct': False
                    # 故意不包含: linked_kp_ids, misconception_map
                }
                problems.append(problem)
                problem_id += 1

        return problems
    
    async def run_agent_diagnostic(self, test_case: Dict) -> Dict:
        """运行智能体诊断"""
        try:
            student_id = test_case['student_id']
            problems = test_case['simulated_problems']

            if self.tutor_agent:
                self.logger.info(f"AI模型诊断开始 (学生 {student_id})")

                # 真正调用智能体诊断工具进行诊断
                try:
                    import json

                    # 手动确保.env文件被加载
                    from dotenv import load_dotenv
                    import pathlib
                    current_dir = pathlib.Path(__file__).parent.parent
                    env_path = current_dir / ".env"
                    if env_path.exists():
                        load_dotenv(env_path, override=True)

                    # 调用智能体诊断工具进行真正的AI诊断
                    diagnostic_results = []

                    # 为每个问题调用诊断工具
                    for i, problem in enumerate(problems):
                        problem_data = self._convert_problem_for_diagnosis(problem, i+1)

                        # 调用诊断工具
                        from graphmasal.agents.tools.diagnostic_tool import diagnose_student_performance

                        try:
                            result_json = await diagnose_student_performance.ainvoke({
                                "student_id": student_id,
                                "problem_data": json.dumps(problem_data, ensure_ascii=False)
                            })

                            # 解析诊断结果
                            result = json.loads(result_json)
                            diagnostic_results.append({
                                'problem': problem,
                                'diagnosis': result
                            })

                        except Exception as tool_error:
                            self.logger.warning(f"问题 {i+1} 诊断工具调用失败: {tool_error}")
                            # 单个问题失败时，使用基本结果
                            diagnostic_results.append({
                                'problem': problem,
                                'diagnosis': {
                                    'is_correct': problem.get('is_correct', False),
                                    'identified_misconceptions': [],
                                    'mastery_analysis': {},
                                    'recommendations': []
                                }
                            })

                    # 聚合所有问题的诊断结果
                    aggregated_result = self._aggregate_diagnostic_results(student_id, diagnostic_results)

                    self.logger.info(f"AI诊断成功 (学生 {student_id}): 掌握 {len(aggregated_result['diagnostic_result']['mastered_concepts'])} 个概念, 未掌握 {len(aggregated_result['diagnostic_result']['not_mastered_concepts'])} 个概念")

                    self.logger.info(f"AI诊断完成 (学生 {student_id}) - 处理了 {len(problems)} 个问题")
                    return aggregated_result

                except Exception as ai_error:
                    self.logger.warning(f"AI诊断失败，使用模拟结果: {ai_error}")
                    # 如果AI调用失败，回退到模拟结果
                    return self._generate_simulated_diagnostic(student_id, problems)
            else:
                # 没有智能体，使用模拟结果
                return self._generate_simulated_diagnostic(student_id, problems)

        except Exception as e:
            self.logger.error(f"智能体诊断失败 (学生 {test_case['student_id']}): {e}")
            return self._create_empty_diagnostic_result(test_case['student_id'])
    
    async def run_agent_planning(self, diagnostic_result: Dict) -> Dict:
        """运行智能体规划"""
        try:
            student_id = diagnostic_result.get('evaluation_metadata', {}).get('student_id', 'unknown')

            if self.tutor_agent:
                self.logger.info(f"AI模型路径规划开始 (学生 {student_id})")

                try:
                    # 1. 从诊断结果中提取掌握和未掌握的知识点
                    diagnostic_result_data = diagnostic_result.get('diagnostic_result', {})

                    # 提取已掌握的概念
                    mastered_concepts = []
                    for concept in diagnostic_result_data.get('mastered_concepts', []):
                        mastered_concepts.append({
                            'concept_id': concept.get('concept_id', concept.get('concept_name', '')),
                            'concept_name': concept.get('concept_name', ''),
                            'mastery_level': concept.get('mastery_level', 1.0)
                        })

                    # 提取未掌握的概念
                    not_mastered_concepts = []
                    for concept in diagnostic_result_data.get('not_mastered_concepts', []):
                        not_mastered_concepts.append({
                            'concept_id': concept.get('concept_id', concept.get('concept_name', '')),
                            'concept_name': concept.get('concept_name', ''),
                            'mastery_level': concept.get('mastery_level', 0.0)
                        })

                    # 提取掌握度较低的概念（<0.6）也视为需要学习的概念
                    weak_concepts = []
                    for concept in mastered_concepts:
                        if concept['mastery_level'] < 0.6:
                            weak_concepts.append(concept)

                    # 合并未掌握和薄弱概念
                    all_weak_concepts = not_mastered_concepts + weak_concepts

                    self.logger.info(f"学生 {student_id}: 已掌握 {len(mastered_concepts)} 个概念，需要学习 {len(all_weak_concepts)} 个概念")

                    # 2. 使用 planning_tool 进行路径规划
                    from graphmasal.agents.tools.planning_tool import generate_optimal_learning_paths

                    # 确保学生ID正确传递
                    actual_student_id = diagnostic_result.get('evaluation_metadata', {}).get('student_id', student_id)

                    # 在调用AI规划工具之前，先设置学生掌握度数据到会话状态
                    await self._prepare_student_mastery_for_planning(actual_student_id, diagnostic_result)

                    # 调用AI进行最优路径规划
                    ai_result = await generate_optimal_learning_paths.ainvoke({
                        "student_id": actual_student_id,
                        "max_path_length": 10
                    })

                    # 解析AI返回的结果
                    import json
                    ai_planning = json.loads(ai_result)

                    self.logger.debug(f"AI规划结果: {ai_planning}")

                    # 3. 按照评测方案分离路径子集和独立点子集
                    paths_subset = []
                    points_subset = []

                    # 处理学习路径
                    learning_paths = ai_planning.get('learning_paths', [])
                    path_counter = 0  # 用于生成路径ID的计数器

                    for path in learning_paths:
                        if isinstance(path, dict) and 'steps' in path:
                            path_id = path.get('path_id', '')

                            # 过滤掉独立概念路径，直接跳过
                            if path_id == 'independent_concepts':
                                continue  # 跳过这个路径，不添加到 paths_subset

                            # 提取路径中的概念
                            path_concepts = []
                            for step in path.get('steps', []):
                                if isinstance(step, dict) and 'concept_id' in step:
                                    path_concepts.append(step['concept_id'])

                            # 只有当路径包含多个概念时才作为真正的路径
                            if len(path_concepts) > 1:
                                # 构建边列表（相邻概念之间的连接）
                                edges = []
                                for j in range(len(path_concepts) - 1):
                                    edges.append([path_concepts[j], path_concepts[j + 1]])

                                # 构建符合plan1.json格式的路径结构
                                paths_subset.append({
                                    'path_id': path_id if path_id and path_id != 'independent_concepts' else f'optimal_path_{path_counter}',
                                    'nodes': path_concepts,  # 路径中的节点列表
                                    'edges': edges,  # 路径中的边列表
                                    'sequence': path_concepts,  # 学习顺序序列
                                    'concepts': path_concepts,  # 概念列表（用于相似度计算）
                                    'estimated_time': path.get('estimated_time', 0),
                                    'priority': 'medium'  # 默认优先级
                                })
                                path_counter += 1
                            elif len(path_concepts) == 1:
                                # 单个概念应该放在 points_subset 中
                                concept_id = path_concepts[0]
                                if concept_id not in points_subset:
                                    points_subset.append(concept_id)

                    # 处理独立薄弱概念
                    independent_concepts = ai_planning.get('independent_weak_concepts', [])
                    for concept in independent_concepts:
                        if isinstance(concept, dict):
                            concept_id = concept.get('concept_id', '')
                            if concept_id and concept_id not in points_subset:
                                points_subset.append(concept_id)
                        else:
                            concept_str = str(concept)
                            if concept_str and concept_str not in points_subset:
                                points_subset.append(concept_str)

                    # 如果没有独立概念，将所有薄弱概念作为独立点
                    if not points_subset:
                        for concept in all_weak_concepts:
                            concept_id = concept.get('concept_id', '')
                            if concept_id and concept_id not in [c for path in paths_subset for c in path.get('concepts', [])]:
                                if concept_id not in points_subset:
                                    points_subset.append(concept_id)

                    # 4. 构建最终的规划结果
                    planning_result = {
                        'evaluation_metadata': {
                            'student_id': student_id,
                            'target_concept': None,  # 多目标规划
                            'timestamp': datetime.now().isoformat(),
                            'generator_version': '2.0.0',
                            'plan_type': ai_planning.get('plan_type', 'multi_source_multi_sink_optimal'),
                            'planning_type': 'multi_target_optimal'
                        },
                        'planning_result': {
                            'paths_subset': paths_subset,
                            'points_subset': points_subset,
                            'total_concepts_to_learn': len(set([c for path in paths_subset for c in path.get('concepts', [])] + points_subset)),
                            'estimated_time': ai_planning.get('estimated_time', 0),
                            'planning_confidence': 0.95
                        }
                    }

                    self.logger.info(f"AI路径规划完成 (学生 {student_id}): {len(paths_subset)} 条路径, {len(points_subset)} 个独立点")
                    return planning_result

                except Exception as ai_error:
                    self.logger.warning(f"AI规划失败，使用模拟结果: {ai_error}")
                    # 如果AI调用失败，回退到模拟结果
                    return self._generate_simulated_planning(student_id, diagnostic_result)
            else:
                # 没有智能体，使用模拟结果
                return self._generate_simulated_planning(student_id, diagnostic_result)

        except Exception as e:
            self.logger.error(f"智能体规划失败 (学生 {student_id}): {e}")
            return self._create_empty_planning_result(student_id)

    async def _prepare_student_mastery_for_planning(self, student_id: str, diagnostic_result: Dict):
        """为路径规划准备学生掌握度数据"""
        try:
            # 从AI诊断结果中提取掌握度信息
            diagnostic_data = diagnostic_result.get('diagnostic_result', {})

            # 构建掌握度映射
            mastery_map = {}

            # 处理已掌握的概念
            for concept in diagnostic_data.get('mastered_concepts', []):
                concept_id = concept.get('concept_id', concept.get('concept_name', ''))
                mastery_level = concept.get('mastery_level', 1.0)
                if concept_id:
                    mastery_map[concept_id] = mastery_level

            # 处理未掌握的概念
            for concept in diagnostic_data.get('not_mastered_concepts', []):
                concept_id = concept.get('concept_id', concept.get('concept_name', ''))
                mastery_level = concept.get('mastery_level', 0.0)
                if concept_id:
                    mastery_map[concept_id] = mastery_level

            # 将掌握度数据设置到会话状态中，供planning_tool使用
            from graphmasal.agents.session_state import get_session_state_manager
            session_state = get_session_state_manager()
            session_state.set_student_mastery(student_id, mastery_map)

            self.logger.info(f"为学生 {student_id} 设置了 {len(mastery_map)} 个概念的掌握度数据")

        except Exception as e:
            self.logger.error(f"准备学生掌握度数据失败: {e}")
            # 即使失败也不影响主流程

    def _convert_problem_for_diagnosis(self, problem: Dict, problem_number: int) -> Dict:
        """将盲测问题格式转换为诊断工具期望的格式"""
        return {
            'problem_id': f"blind_test_{problem_number}",
            'selected_options': problem.get('student_answer', []),
            'correct_answer': [problem.get('correct_answer', '')],
            'misconception_map': {},  # 盲测中故意不提供misconception_map
            'concept_id': problem.get('concept_id')  # 传递概念ID用于掌握度分析
        }

    def _aggregate_diagnostic_results(self, student_id: str, diagnostic_results: List[Dict]) -> Dict:
        """聚合多个问题的诊断结果"""
        from datetime import datetime

        # 按概念分组统计
        concept_stats = {}
        total_problems = len(diagnostic_results)
        total_correct = 0
        all_misconceptions = []

        for result_item in diagnostic_results:
            problem = result_item['problem']
            diagnosis = result_item['diagnosis']

            concept_id = problem.get('concept_id', 'unknown_concept')
            concept_name = problem.get('concept_hint', concept_id)
            is_correct = diagnosis.get('is_correct', False)

            if is_correct:
                total_correct += 1

            # 收集易错点
            misconceptions = diagnosis.get('identified_misconceptions', [])
            all_misconceptions.extend(misconceptions)

            # 统计概念表现
            if concept_id not in concept_stats:
                concept_stats[concept_id] = {
                    'concept_name': concept_name,
                    'correct': 0,
                    'total': 0,
                    'mastery_levels': []
                }

            concept_stats[concept_id]['total'] += 1
            if is_correct:
                concept_stats[concept_id]['correct'] += 1

            # 从诊断结果中提取掌握度信息
            mastery_analysis = diagnosis.get('mastery_analysis', {})
            if concept_id in mastery_analysis:
                concept_stats[concept_id]['mastery_levels'].append(mastery_analysis[concept_id])
            else:
                # 如果没有掌握度分析，基于正确性估算
                estimated_mastery = 0.8 if is_correct else 0.3
                concept_stats[concept_id]['mastery_levels'].append(estimated_mastery)

        # 计算每个概念的最终掌握度
        mastered_concepts = []
        not_mastered_concepts = []

        for concept_id, stats in concept_stats.items():
            # 计算平均掌握度
            if stats['mastery_levels']:
                avg_mastery = sum(stats['mastery_levels']) / len(stats['mastery_levels'])
            else:
                avg_mastery = 0.0

            # 计算准确率
            accuracy_rate = stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0

            # 综合掌握度（70%掌握度 + 30%准确率）
            final_mastery = (avg_mastery * 0.7 + accuracy_rate * 0.3)
            final_mastery = round(final_mastery, 3)

            concept_info = {
                'concept_id': concept_id,
                'concept_name': stats['concept_name'],
                'mastery_level': final_mastery,
                'accuracy_rate': round(accuracy_rate, 3),
                'problem_count': stats['total']
            }

            # 分类：掌握度 >= 0.6 为掌握，< 0.6 为未掌握
            if final_mastery >= 0.6:
                mastered_concepts.append(concept_info)
            else:
                not_mastered_concepts.append(concept_info)

        # 按掌握度排序
        mastered_concepts.sort(key=lambda x: x['mastery_level'], reverse=True)
        not_mastered_concepts.sort(key=lambda x: x['mastery_level'])

        # 计算总体准确率
        overall_accuracy = total_correct / total_problems if total_problems > 0 else 0.0

        # 构造最终诊断结果
        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'ai_diagnostic_tool_1.0.0',
                'total_problems': total_problems,
                'correct_problems': total_correct
            },
            'diagnostic_result': {
                'overall_accuracy': round(overall_accuracy, 3),
                'mastered_concepts': mastered_concepts,
                'not_mastered_concepts': not_mastered_concepts,
                'total_concepts': len(mastered_concepts) + len(not_mastered_concepts),
                'mastered_count': len(mastered_concepts),
                'not_mastered_count': len(not_mastered_concepts),
                'diagnostic_confidence': 0.9,  # AI诊断的置信度
                'identified_misconceptions': all_misconceptions
            }
        }

    def _calculate_diagnostic_directly(self, student_id: str, problems: List[Dict]) -> Dict:
        """
        使用与ground_truth_generator相同的直接计算方法

        Args:
            student_id: 学生ID
            problems: 问题列表

        Returns:
            诊断结果字典
        """
        from datetime import datetime

        # 按概念分组统计
        concept_stats = {}

        for problem in problems:
            concept_id = problem.get('concept_id', 'unknown_concept')
            concept_name = problem.get('concept_hint', concept_id)
            is_correct = problem.get('is_correct', False)

            if concept_id not in concept_stats:
                concept_stats[concept_id] = {
                    'concept_name': concept_name,
                    'correct': 0,
                    'total': 0,
                    'problems': []
                }

            concept_stats[concept_id]['total'] += 1
            if is_correct:
                concept_stats[concept_id]['correct'] += 1
            concept_stats[concept_id]['problems'].append(problem)

        # 使用与ground_truth_generator相同的掌握度计算方式
        mastered_concepts = []
        not_mastered_concepts = []

        for concept_id, stats in concept_stats.items():
            # 1. 计算每个问题的掌握度（模拟单题掌握度计算）
            mastery_levels = []
            for i in range(stats['total']):
                is_correct = i < stats['correct']  # 前correct个问题正确
                # 假设平均难度为5（ground_truth_generator中的默认值）
                difficulty = 5

                # 基于答题正确性和题目难度计算掌握度
                base_mastery = 0.85 if is_correct else 0.25
                difficulty_factor = (10 - difficulty) / 10
                mastery_level = base_mastery + (difficulty_factor * 0.15 if is_correct else -difficulty_factor * 0.15)
                mastery_level = max(0.0, min(1.0, mastery_level))
                mastery_levels.append(mastery_level)

            # 2. 使用加权平均计算最终掌握度（与ground_truth_generator相同）
            avg_mastery = sum(mastery_levels) / len(mastery_levels) if mastery_levels else 0.0
            accuracy_rate = stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0
            final_mastery = (avg_mastery * 0.7 + accuracy_rate * 0.3)
            final_mastery = round(final_mastery, 3)

            concept_info = {
                'concept_id': concept_id,
                'concept_name': stats['concept_name'],
                'mastery_level': final_mastery,
                'accuracy_rate': round(accuracy_rate, 3),
                'problem_count': stats['total']
            }

            # 3. 分类：掌握度 >= 0.6 为掌握，< 0.6 为未掌握（与ground_truth_generator一致）
            if final_mastery >= 0.6:
                mastered_concepts.append(concept_info)
            else:
                not_mastered_concepts.append(concept_info)

        # 按掌握度排序
        mastered_concepts.sort(key=lambda x: x['mastery_level'], reverse=True)
        not_mastered_concepts.sort(key=lambda x: x['mastery_level'])

        # 计算总体准确率
        total_correct = sum(1 for p in problems if p.get('is_correct', False))
        overall_accuracy = total_correct / len(problems) if problems else 0.0

        # 构造诊断结果
        diagnostic_result = {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'direct_calculation_1.0.0',
                'total_problems': len(problems),
                'correct_problems': total_correct
            },
            'diagnostic_result': {
                'overall_accuracy': round(overall_accuracy, 3),
                'mastered_concepts': mastered_concepts,
                'not_mastered_concepts': not_mastered_concepts,
                'total_concepts': len(mastered_concepts) + len(not_mastered_concepts),
                'mastered_count': len(mastered_concepts),
                'not_mastered_count': len(not_mastered_concepts),
                'diagnostic_confidence': 1.0  # 直接计算，置信度为1.0
            }
        }

        return diagnostic_result
    
    def _generate_simulated_diagnostic(self, student_id: str, problems: List[Dict]) -> Dict:
        """生成模拟的诊断结果"""
        # 按概念ID分组统计答题情况
        concept_stats = {}
        for problem in problems:
            concept_id = problem.get('concept_id', 'unknown_concept')
            concept_name = problem.get('concept_hint', 'unknown_concept')
            is_correct = problem.get('is_correct', False)

            if concept_id not in concept_stats:
                concept_stats[concept_id] = {
                    'correct': 0,
                    'total': 0,
                    'concept_name': concept_name
                }

            concept_stats[concept_id]['total'] += 1
            if is_correct:
                concept_stats[concept_id]['correct'] += 1

        # 基于正确率判断掌握情况
        mastered_concepts = []
        not_mastered_concepts = []

        for concept_id, stats in concept_stats.items():
            accuracy_rate = stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0

            # 使用与ground_truth_generator相同的掌握度计算方式
            # 1. 计算每个问题的掌握度（模拟单题掌握度计算）
            mastery_levels = []
            for i in range(stats['total']):
                is_correct = i < stats['correct']  # 前correct个问题正确
                # 假设平均难度为5（ground_truth_generator中的默认值）
                difficulty = 5

                # 基于答题正确性和题目难度计算掌握度
                base_mastery = 0.85 if is_correct else 0.25
                difficulty_factor = (10 - difficulty) / 10
                mastery_level = base_mastery + (difficulty_factor * 0.15 if is_correct else -difficulty_factor * 0.15)
                mastery_level = max(0.0, min(1.0, mastery_level))
                mastery_levels.append(mastery_level)

            # 2. 使用加权平均计算最终掌握度（与ground_truth_generator相同）
            avg_mastery = sum(mastery_levels) / len(mastery_levels) if mastery_levels else 0.0
            final_mastery = (avg_mastery * 0.7 + accuracy_rate * 0.3)
            final_mastery = round(final_mastery, 3)

            concept_info = {
                'concept_id': concept_id,
                'concept_name': stats['concept_name'],
                'mastery_level': final_mastery,
                'accuracy_rate': round(accuracy_rate, 3),
                'problem_count': stats['total']
            }

            # 3. 分类：掌握度 >= 0.6 为掌握，< 0.6 为未掌握（与ground_truth_generator一致）
            if final_mastery >= 0.6:
                mastered_concepts.append(concept_info)
            else:
                not_mastered_concepts.append(concept_info)

        total_correct = sum(1 for p in problems if p.get('is_correct', False))
        overall_accuracy = total_correct / len(problems) if problems else 0.0

        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'blind_test_agent_1.0.0',
                'total_problems': len(problems),
                'correct_problems': total_correct
            },
            'diagnostic_result': {
                'overall_accuracy': round(overall_accuracy, 3),
                'mastered_concepts': mastered_concepts,
                'not_mastered_concepts': not_mastered_concepts,
                'total_concepts': len(concept_stats),
                'mastered_count': len(mastered_concepts),
                'not_mastered_count': len(not_mastered_concepts),
                'diagnostic_confidence': 0.8
            }
        }
    
    def _generate_simulated_planning(self, student_id: str, diagnostic_result: Dict) -> Dict:
        """生成模拟的规划结果"""
        diag_result = diagnostic_result.get('diagnostic_result', {})
        not_mastered_concepts = diag_result.get('not_mastered_concepts', [])
        
        paths_subset = []
        points_subset = []
        
        # 简单的规划逻辑：少量概念作为独立点，多个概念形成路径
        if len(not_mastered_concepts) <= 1:
            for concept in not_mastered_concepts:
                points_subset.append(concept['concept_id'])
        elif len(not_mastered_concepts) == 2:
            concepts = [c['concept_id'] for c in not_mastered_concepts]
            paths_subset.append({
                'path_id': f'path_0',
                'nodes': concepts,
                'edges': [[concepts[0], concepts[1]]],
                'sequence': concepts,
                'concepts': concepts,
                'estimated_time': len(concepts) * 25,
                'priority': 'high'
            })
        else:
            # 前两个概念形成路径，其余作为独立点
            path_concepts = [c['concept_id'] for c in not_mastered_concepts[:2]]
            independent_concepts = [c['concept_id'] for c in not_mastered_concepts[2:]]
            
            paths_subset.append({
                'path_id': f'path_0',
                'nodes': path_concepts,
                'edges': [[path_concepts[0], path_concepts[1]]],
                'sequence': path_concepts,
                'concepts': path_concepts,
                'estimated_time': len(path_concepts) * 25,
                'priority': 'high'
            })
            points_subset.extend(independent_concepts)
        
        total_concepts = len(points_subset) + sum(len(p.get('concepts', [])) for p in paths_subset)
        
        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'blind_test_planning_1.0.0',
                'plan_type': 'simulated'
            },
            'planning_result': {
                'paths_subset': paths_subset,
                'points_subset': points_subset,
                'total_concepts_to_learn': total_concepts,
                'estimated_time': total_concepts * 25,
                'planning_confidence': 0.8
            }
        }

    def calculate_diagnostic_accuracy(self, predicted: Dict, ground_truth: Dict) -> float:
        """
        计算认知诊断准确率
        认知诊断准确率 = (系统正确诊断的知识点掌握状态数量) / (总的知识点诊断数量)
        """
        try:
            # 提取标准答案中的知识点掌握状态
            gt_mastered = set()
            gt_not_mastered = set()

            gt_diag = ground_truth.get('diagnostic_result', {})
            for concept in gt_diag.get('mastered_concepts', []):
                gt_mastered.add(concept['concept_id'])
            for concept in gt_diag.get('not_mastered_concepts', []):
                gt_not_mastered.add(concept['concept_id'])

            # 提取预测结果中的知识点掌握状态
            pred_mastered = set()
            pred_not_mastered = set()

            pred_diag = predicted.get('diagnostic_result', {})
            for concept in pred_diag.get('mastered_concepts', []):
                pred_mastered.add(concept['concept_id'])
            for concept in pred_diag.get('not_mastered_concepts', []):
                pred_not_mastered.add(concept['concept_id'])

            # 计算正确诊断的知识点数量
            correct_mastered = len(gt_mastered & pred_mastered)
            correct_not_mastered = len(gt_not_mastered & pred_not_mastered)
            total_correct = correct_mastered + correct_not_mastered

            # 总的知识点诊断数量
            total_concepts = len(gt_mastered | gt_not_mastered)

            return total_correct / total_concepts if total_concepts > 0 else 0.0

        except Exception as e:
            self.logger.error(f"计算诊断准确率失败: {e}")
            return 0.0

    def calculate_path_similarity(self, predicted: Dict, ground_truth: Dict) -> float:
        """
        计算路径匹配度（按照评测方案.md的完整算法）
        """
        try:
            # 使用PathSimilarityEvaluator进行符合评测方案的相似度计算
            result = self.path_similarity_evaluator._compare_single_planning(
                predicted, ground_truth, 0
            )

            total_similarity = result.get('total_similarity', 0.0)

            self.logger.debug(f"路径相似度计算结果: {total_similarity}")
            self.logger.debug(f"路径子集相似度: {result.get('paths_similarity', 0.0)}")
            self.logger.debug(f"独立点相似度: {result.get('points_similarity', 0.0)}")

            return total_similarity

        except Exception as e:
            self.logger.error(f"计算路径相似度失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return 0.0

    async def run_complete_blind_test(self, ground_truth_dir: str) -> Dict:
        """运行完整的盲测评估"""
        try:
            self.logger.info(f"开始完整盲测评估，使用标准答案目录: {ground_truth_dir}")

            # 设置智能体系统
            await self.setup_system()

            # 加载标准答案
            diagnostic_gt, planning_gt = self.load_ground_truth(ground_truth_dir)

            # 创建盲测用例
            blind_test_cases = self.create_blind_test_cases(diagnostic_gt)

            # 运行盲测评估
            results = []

            for i, test_case in enumerate(blind_test_cases):
                student_id = test_case['student_id']
                self.logger.info(f"处理学生 {student_id} ({i+1}/{len(blind_test_cases)})")

                try:
                    # 1. 运行智能体诊断
                    predicted_diagnostic = await self.run_agent_diagnostic(test_case)

                    # 2. 运行智能体规划
                    predicted_planning = await self.run_agent_planning(predicted_diagnostic)

                    # 3. 找到对应的标准答案
                    gt_diagnostic = next((gt for gt in diagnostic_gt
                                        if gt.get('evaluation_metadata', {}).get('student_id') == student_id), None)
                    gt_planning = next((gt for gt in planning_gt
                                      if gt.get('evaluation_metadata', {}).get('student_id') == student_id), None)

                    if gt_diagnostic and gt_planning:
                        # 4. 计算评测指标
                        diagnostic_accuracy = self.calculate_diagnostic_accuracy(predicted_diagnostic, gt_diagnostic)
                        path_similarity = self.calculate_path_similarity(predicted_planning, gt_planning)

                        results.append({
                            'student_id': student_id,
                            'diagnostic_accuracy': diagnostic_accuracy,
                            'path_similarity': path_similarity,
                            'predicted_diagnostic': predicted_diagnostic,
                            'predicted_planning': predicted_planning
                        })

                        self.logger.info(f"  诊断准确率: {diagnostic_accuracy:.3f}, 路径相似度: {path_similarity:.3f}")

                except Exception as e:
                    self.logger.error(f"处理学生 {student_id} 失败: {e}")
                    continue

            # 计算总体统计
            if results:
                avg_diagnostic_accuracy = np.mean([r['diagnostic_accuracy'] for r in results])
                avg_path_similarity = np.mean([r['path_similarity'] for r in results])
            else:
                avg_diagnostic_accuracy = 0.0
                avg_path_similarity = 0.0

            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            final_results = {
                'test_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'ground_truth_dir': ground_truth_dir,
                    'total_students': len(blind_test_cases),
                    'successful_evaluations': len(results),
                    'test_type': 'complete_blind_test'
                },
                'summary_metrics': {
                    'average_diagnostic_accuracy': round(avg_diagnostic_accuracy, 3),
                    'average_path_similarity': round(avg_path_similarity, 3),
                    'diagnostic_accuracy_std': round(np.std([r['diagnostic_accuracy'] for r in results]), 3) if results else 0.0,
                    'path_similarity_std': round(np.std([r['path_similarity'] for r in results]), 3) if results else 0.0
                },
                'detailed_results': results
            }

            # 保存详细结果
            result_file = self.results_dir / f"complete_blind_test_{timestamp}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(final_results, f, ensure_ascii=False, indent=2)

            # 生成简要报告
            report = self._generate_report(final_results)
            report_file = self.results_dir / f"blind_test_report_{timestamp}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            self.logger.info(f"盲测评估完成！")
            self.logger.info(f"平均诊断准确率: {avg_diagnostic_accuracy:.3f}")
            self.logger.info(f"平均路径相似度: {avg_path_similarity:.3f}")
            self.logger.info(f"结果保存到: {result_file}")

            return final_results

        except Exception as e:
            self.logger.error(f"完整盲测评估失败: {e}")
            raise

    def _generate_report(self, results: Dict) -> str:
        """生成评估报告"""
        metadata = results.get('test_metadata', {})
        metrics = results.get('summary_metrics', {})

        report_lines = [
            "=" * 60,
            "完整盲测评估报告",
            "=" * 60,
            f"测试时间: {metadata.get('timestamp', 'Unknown')}",
            f"标准答案目录: {metadata.get('ground_truth_dir', 'Unknown')}",
            f"总学生数: {metadata.get('total_students', 0)}",
            f"成功评估数: {metadata.get('successful_evaluations', 0)}",
            "",
            "=== 核心指标 ===",
            f"认知诊断准确率: {metrics.get('average_diagnostic_accuracy', 0.0):.3f} ± {metrics.get('diagnostic_accuracy_std', 0.0):.3f}",
            f"路径匹配度: {metrics.get('average_path_similarity', 0.0):.3f} ± {metrics.get('path_similarity_std', 0.0):.3f}",
            "",
            "=== 性能评估 ===",
        ]

        diag_acc = metrics.get('average_diagnostic_accuracy', 0.0)
        path_sim = metrics.get('average_path_similarity', 0.0)

        if diag_acc >= 0.8:
            report_lines.append("[优秀] 认知诊断表现优秀")
        elif diag_acc >= 0.6:
            report_lines.append("[良好] 认知诊断表现良好")
        else:
            report_lines.append("[需改进] 认知诊断需要改进")

        if path_sim >= 0.8:
            report_lines.append("[优秀] 路径规划表现优秀")
        elif path_sim >= 0.6:
            report_lines.append("[良好] 路径规划表现良好")
        else:
            report_lines.append("[需改进] 路径规划需要改进")

        report_lines.extend([
            "",
            "=" * 60
        ])

        return "\n".join(report_lines)

    def _create_empty_diagnostic_result(self, student_id: str) -> Dict:
        """创建空的诊断结果"""
        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'blind_test_agent_1.0.0',
                'total_problems': 0,
                'correct_problems': 0
            },
            'diagnostic_result': {
                'overall_accuracy': 0.0,
                'mastered_concepts': [],
                'not_mastered_concepts': [],
                'total_concepts': 0,
                'mastered_count': 0,
                'not_mastered_count': 0,
                'diagnostic_confidence': 0.0
            }
        }

    def _create_empty_planning_result(self, student_id: str) -> Dict:
        """创建空的规划结果"""
        return {
            'evaluation_metadata': {
                'student_id': student_id,
                'timestamp': datetime.now().isoformat(),
                'generator_version': 'blind_test_planning_1.0.0',
                'plan_type': 'empty'
            },
            'planning_result': {
                'paths_subset': [],
                'points_subset': [],
                'total_concepts_to_learn': 0,
                'estimated_time': 0,
                'planning_confidence': 0.0
            }
        }


# 主函数
async def main():
    """主函数 - 运行完整盲测评估"""
    import argparse

    parser = argparse.ArgumentParser(description='运行完整盲测评估')
    parser.add_argument('--ground-truth-dir',
                       default='evaluation/ground_truth',
                       help='标准答案文件夹路径')
    parser.add_argument('--results-dir',
                       default='evaluation/blind_test_results',
                       help='结果保存目录')

    args = parser.parse_args()

    try:
        # 创建盲测评测器
        evaluator = CompleteBlindTestEvaluator(args.results_dir)

        # 运行完整盲测
        results = await evaluator.run_complete_blind_test(args.ground_truth_dir)

        # 输出结果
        metrics = results.get('summary_metrics', {})
        print(f"\n[完成] 完整盲测评估完成！")
        print(f"[指标] 认知诊断准确率: {metrics.get('average_diagnostic_accuracy', 0.0):.3f}")
        print(f"[指标] 路径匹配度: {metrics.get('average_path_similarity', 0.0):.3f}")
        print(f"[文件] 详细结果保存在: {args.results_dir}")

    except Exception as e:
        logger.error(f"完整盲测评估失败: {e}")
        print(f"[错误] 评估失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
