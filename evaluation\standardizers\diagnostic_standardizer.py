"""
诊断结果标准化器
将智能体诊断输出转换为标准JSON格式
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class DiagnosticStandardizer:
    """诊断结果标准化器"""
    
    def __init__(self):
        """初始化标准化器"""
        self.logger = logger
    
    def standardize_diagnostic_output(self, raw_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        将原始诊断输出标准化为评测格式
        
        Args:
            raw_output: 智能体原始诊断输出
            
        Returns:
            标准化的诊断结果JSON
        """
        try:
            # 解析原始输出（如果是字符串格式）
            if isinstance(raw_output, str):
                raw_output = json.loads(raw_output)
            
            # 提取基本信息
            student_id = raw_output.get('student_id', 'unknown')
            problem_id = raw_output.get('problem_id', 0)
            is_correct = raw_output.get('is_correct', False)
            
            # 提取知识点掌握情况
            mastery_analysis = raw_output.get('mastery_analysis', {})
            knowledge_point_mastery = {}
            
            for concept_id, analysis in mastery_analysis.items():
                if isinstance(analysis, dict):
                    mastery_level = analysis.get('mastery_level', 0.0)
                    confidence = analysis.get('confidence', 0.0)
                else:
                    mastery_level = float(analysis) if analysis else 0.0
                    confidence = 1.0
                
                knowledge_point_mastery[concept_id] = {
                    'mastery_level': mastery_level,
                    'confidence': confidence,
                    'status': self._determine_mastery_status(mastery_level)
                }
            
            # 提取易错点信息
            misconceptions = raw_output.get('identified_misconceptions', [])
            identified_misconceptions = []
            
            for misconception in misconceptions:
                if isinstance(misconception, dict):
                    identified_misconceptions.append({
                        'misconception_id': misconception.get('misconception_id', ''),
                        'description': misconception.get('description', ''),
                        'option': misconception.get('option', ''),
                        'strength': misconception.get('strength', 1.0)
                    })
                else:
                    identified_misconceptions.append({
                        'misconception_id': str(misconception),
                        'description': str(misconception),
                        'option': 'unknown',
                        'strength': 1.0
                    })
            
            # 构建标准化输出
            standardized_output = {
                'evaluation_metadata': {
                    'student_id': student_id,
                    'problem_id': problem_id,
                    'timestamp': datetime.now().isoformat(),
                    'evaluator_version': '1.0.0'
                },
                'diagnostic_result': {
                    'is_correct': is_correct,
                    'overall_accuracy': 1.0 if is_correct else 0.0,
                    'knowledge_point_mastery': knowledge_point_mastery,
                    'identified_misconceptions': identified_misconceptions,
                    'diagnostic_confidence': self._calculate_diagnostic_confidence(raw_output)
                },
                'raw_output': raw_output
            }
            
            self.logger.info(f"成功标准化诊断结果 - 学生: {student_id}, 题目: {problem_id}")
            return standardized_output
            
        except Exception as e:
            self.logger.error(f"诊断结果标准化失败: {e}")
            return self._create_error_output(raw_output, str(e))
    
    def _determine_mastery_status(self, mastery_level: float) -> str:
        """根据掌握度确定掌握状态"""
        if mastery_level >= 0.8:
            return 'mastered'
        elif mastery_level >= 0.6:
            return 'partially_mastered'
        elif mastery_level >= 0.3:
            return 'weak'
        else:
            return 'not_mastered'
    
    def _calculate_diagnostic_confidence(self, raw_output: Dict[str, Any]) -> float:
        """计算诊断置信度"""
        try:
            # 基于多个因素计算置信度
            factors = []
            
            # 因素1: 掌握度分析的完整性
            mastery_analysis = raw_output.get('mastery_analysis', {})
            if mastery_analysis:
                factors.append(min(len(mastery_analysis) / 5.0, 1.0))  # 假设5个概念为满分
            
            # 因素2: 易错点识别的准确性
            misconceptions = raw_output.get('identified_misconceptions', [])
            if misconceptions:
                factors.append(0.8)  # 识别到易错点给予较高置信度
            else:
                factors.append(0.6)  # 未识别到易错点给予中等置信度
            
            # 因素3: 推荐建议的质量
            recommendations = raw_output.get('recommendations', [])
            if recommendations:
                factors.append(min(len(recommendations) / 3.0, 1.0))
            
            return sum(factors) / len(factors) if factors else 0.5
            
        except Exception:
            return 0.5
    
    def _create_error_output(self, raw_output: Any, error_message: str) -> Dict[str, Any]:
        """创建错误输出格式"""
        return {
            'evaluation_metadata': {
                'student_id': 'unknown',
                'problem_id': 0,
                'timestamp': datetime.now().isoformat(),
                'evaluator_version': '1.0.0',
                'error': error_message
            },
            'diagnostic_result': {
                'is_correct': False,
                'overall_accuracy': 0.0,
                'knowledge_point_mastery': {},
                'identified_misconceptions': [],
                'diagnostic_confidence': 0.0
            },
            'raw_output': raw_output
        }
    
    def batch_standardize(self, raw_outputs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量标准化诊断输出"""
        standardized_outputs = []
        
        for i, raw_output in enumerate(raw_outputs):
            try:
                standardized = self.standardize_diagnostic_output(raw_output)
                standardized_outputs.append(standardized)
            except Exception as e:
                self.logger.error(f"批量标准化第{i+1}项失败: {e}")
                standardized_outputs.append(self._create_error_output(raw_output, str(e)))
        
        return standardized_outputs
    
    def save_standardized_results(self, results: List[Dict[str, Any]], filepath: str) -> bool:
        """保存标准化结果到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"标准化结果已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存标准化结果失败: {e}")
            return False
