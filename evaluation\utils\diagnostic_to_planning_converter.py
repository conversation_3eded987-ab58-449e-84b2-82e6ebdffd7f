"""
诊断结果到路径规划转换器
将重新设计的诊断标准答案转换为路径规划可用的格式
"""

import json
import logging
from typing import Dict, List, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class DiagnosticToPlanningConverter:
    """诊断结果到路径规划转换器"""
    
    def __init__(self):
        self.logger = logger
    
    def convert_diagnostic_to_mastery(
        self, 
        diagnostic_results: List[Dict[str, Any]]
    ) -> Dict[str, Dict[str, float]]:
        """
        将诊断结果转换为学生掌握度字典
        
        Args:
            diagnostic_results: 重新设计的诊断标准答案列表
            
        Returns:
            学生掌握度字典 {student_id: {concept_id: mastery_level}}
        """
        student_mastery = {}
        
        for diagnostic in diagnostic_results:
            try:
                # 提取学生ID
                student_id = diagnostic.get('evaluation_metadata', {}).get('student_id', 'unknown')
                
                if student_id == 'unknown':
                    self.logger.warning("诊断结果缺少学生ID，跳过")
                    continue
                
                # 提取诊断结果
                diag_result = diagnostic.get('diagnostic_result', {})
                mastered_concepts = diag_result.get('mastered_concepts', [])
                not_mastered_concepts = diag_result.get('not_mastered_concepts', [])
                
                # 构建该学生的掌握度字典
                student_mastery[student_id] = {}
                
                # 添加掌握的知识点
                for concept in mastered_concepts:
                    concept_id = concept.get('concept_id', '')
                    mastery_level = concept.get('mastery_level', 0.0)
                    if concept_id:
                        student_mastery[student_id][concept_id] = mastery_level
                
                # 添加未掌握的知识点
                for concept in not_mastered_concepts:
                    concept_id = concept.get('concept_id', '')
                    mastery_level = concept.get('mastery_level', 0.0)
                    if concept_id:
                        student_mastery[student_id][concept_id] = mastery_level
                
                self.logger.debug(f"学生 {student_id} 掌握度转换完成: {len(student_mastery[student_id])} 个知识点")
                
            except Exception as e:
                self.logger.error(f"转换诊断结果失败: {e}")
                continue
        
        self.logger.info(f"诊断结果转换完成: {len(student_mastery)} 个学生")
        return student_mastery
    
    def analyze_diagnostic_compatibility(
        self, 
        diagnostic_file: Path
    ) -> Dict[str, Any]:
        """
        分析诊断文件与路径相似度计算的兼容性
        
        Args:
            diagnostic_file: 诊断标准答案文件路径
            
        Returns:
            兼容性分析结果
        """
        try:
            with open(diagnostic_file, 'r', encoding='utf-8') as f:
                diagnostic_data = json.load(f)
            
            if not isinstance(diagnostic_data, list):
                return {
                    'compatible': False,
                    'reason': '诊断数据不是列表格式',
                    'suggestions': ['确保诊断数据是学生诊断结果的列表']
                }
            
            if not diagnostic_data:
                return {
                    'compatible': False,
                    'reason': '诊断数据为空',
                    'suggestions': ['生成诊断标准答案数据']
                }
            
            # 分析第一个记录的结构
            first_record = diagnostic_data[0]
            
            # 检查是否为新格式（按学生组织）
            if self._is_new_diagnostic_format(first_record):
                student_count = len(diagnostic_data)
                concept_counts = []
                
                for record in diagnostic_data:
                    diag_result = record.get('diagnostic_result', {})
                    mastered = len(diag_result.get('mastered_concepts', []))
                    not_mastered = len(diag_result.get('not_mastered_concepts', []))
                    concept_counts.append(mastered + not_mastered)
                
                return {
                    'compatible': True,
                    'format_type': 'new_student_based',
                    'student_count': student_count,
                    'avg_concepts_per_student': sum(concept_counts) / len(concept_counts) if concept_counts else 0,
                    'can_generate_paths': True,
                    'suggestions': ['可以直接用于生成路径规划标准答案']
                }
            
            # 检查是否为旧格式（按题目组织）
            elif self._is_old_diagnostic_format(first_record):
                problem_count = len(diagnostic_data)
                students = set()
                
                for record in diagnostic_data:
                    student_id = record.get('student_id', 'unknown')
                    if student_id != 'unknown':
                        students.add(student_id)
                
                return {
                    'compatible': False,
                    'format_type': 'old_problem_based',
                    'problem_count': problem_count,
                    'student_count': len(students),
                    'can_generate_paths': False,
                    'reason': '旧格式按题目组织，无法直接用于路径相似度计算',
                    'suggestions': [
                        '需要重新生成诊断标准答案，使用新的按学生组织的格式',
                        '或者先将旧格式聚合为按学生组织的格式'
                    ]
                }
            
            else:
                return {
                    'compatible': False,
                    'format_type': 'unknown',
                    'reason': '无法识别的诊断数据格式',
                    'suggestions': [
                        '检查诊断数据的结构是否正确',
                        '确保使用最新的诊断标准答案生成器'
                    ]
                }
                
        except Exception as e:
            return {
                'compatible': False,
                'reason': f'分析文件失败: {str(e)}',
                'suggestions': ['检查文件路径和格式是否正确']
            }
    
    def _is_new_diagnostic_format(self, record: Dict[str, Any]) -> bool:
        """检查是否为新的诊断格式（按学生组织）"""
        try:
            # 检查必需的字段结构
            metadata = record.get('evaluation_metadata', {})
            diag_result = record.get('diagnostic_result', {})
            
            # 新格式特征
            has_student_id = 'student_id' in metadata
            has_mastered_concepts = 'mastered_concepts' in diag_result
            has_not_mastered_concepts = 'not_mastered_concepts' in diag_result
            has_overall_accuracy = 'overall_accuracy' in diag_result
            
            return has_student_id and has_mastered_concepts and has_not_mastered_concepts and has_overall_accuracy
            
        except:
            return False
    
    def _is_old_diagnostic_format(self, record: Dict[str, Any]) -> bool:
        """检查是否为旧的诊断格式（按题目组织）"""
        try:
            # 检查旧格式特征
            metadata = record.get('evaluation_metadata', {})
            diag_result = record.get('diagnostic_result', {})
            
            # 旧格式特征
            has_problem_id = 'problem_id' in metadata
            has_knowledge_point_mastery = 'knowledge_point_mastery' in diag_result
            has_student_answer = 'student_answer' in metadata
            
            return has_problem_id and has_knowledge_point_mastery and has_student_answer
            
        except:
            return False
    
    def generate_compatibility_report(self, diagnostic_file: Path) -> str:
        """生成兼容性分析报告"""
        analysis = self.analyze_diagnostic_compatibility(diagnostic_file)
        
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("诊断标准答案兼容性分析报告")
        report_lines.append("=" * 60)
        
        report_lines.append(f"文件: {diagnostic_file}")
        report_lines.append(f"兼容性: {'✅ 兼容' if analysis.get('compatible', False) else '❌ 不兼容'}")
        
        if 'format_type' in analysis:
            report_lines.append(f"格式类型: {analysis['format_type']}")
        
        if 'student_count' in analysis:
            report_lines.append(f"学生数量: {analysis['student_count']}")
        
        if 'problem_count' in analysis:
            report_lines.append(f"题目数量: {analysis['problem_count']}")
        
        if 'avg_concepts_per_student' in analysis:
            report_lines.append(f"平均每学生知识点数: {analysis['avg_concepts_per_student']:.1f}")
        
        if 'reason' in analysis:
            report_lines.append(f"原因: {analysis['reason']}")
        
        if 'suggestions' in analysis:
            report_lines.append("\n建议:")
            for suggestion in analysis['suggestions']:
                report_lines.append(f"  - {suggestion}")
        
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)
