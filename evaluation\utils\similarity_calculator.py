"""
相似度计算工具
实现Jaccard相似度、莱文斯坦距离等算法
"""

import logging
from typing import List, Set, Tuple, Dict, Any
import numpy as np

logger = logging.getLogger(__name__)

class SimilarityCalculator:
    """相似度计算器"""
    
    def __init__(self, node_weight: float = 0.4, edge_weight: float = 0.3, sequence_weight: float = 0.3):
        """
        初始化相似度计算器
        
        Args:
            node_weight: 节点相似度权重
            edge_weight: 边相似度权重  
            sequence_weight: 序列相似度权重
        """
        self.node_weight = node_weight
        self.edge_weight = edge_weight
        self.sequence_weight = sequence_weight
        self.logger = logger
        
        # 确保权重和为1
        total_weight = node_weight + edge_weight + sequence_weight
        if abs(total_weight - 1.0) > 1e-6:
            self.logger.warning(f"权重和不为1 ({total_weight})，将进行归一化")
            self.node_weight /= total_weight
            self.edge_weight /= total_weight
            self.sequence_weight /= total_weight
    
    def jaccard_similarity(self, set_a: Set[str], set_b: Set[str]) -> float:
        """
        计算两个集合的Jaccard相似度
        
        Args:
            set_a: 集合A
            set_b: 集合B
            
        Returns:
            Jaccard相似度 (0-1之间)
        """
        if not set_a and not set_b:
            return 1.0
        
        intersection = len(set_a.intersection(set_b))
        union = len(set_a.union(set_b))
        
        return intersection / union if union > 0 else 0.0
    
    def levenshtein_distance(self, seq_a: List[str], seq_b: List[str]) -> int:
        """
        计算两个序列的莱文斯坦距离
        
        Args:
            seq_a: 序列A
            seq_b: 序列B
            
        Returns:
            莱文斯坦距离
        """
        if not seq_a:
            return len(seq_b)
        if not seq_b:
            return len(seq_a)
        
        # 创建距离矩阵
        rows = len(seq_a) + 1
        cols = len(seq_b) + 1
        dist = [[0 for _ in range(cols)] for _ in range(rows)]
        
        # 初始化第一行和第一列
        for i in range(1, rows):
            dist[i][0] = i
        for j in range(1, cols):
            dist[0][j] = j
        
        # 填充距离矩阵
        for i in range(1, rows):
            for j in range(1, cols):
                if seq_a[i-1] == seq_b[j-1]:
                    cost = 0
                else:
                    cost = 1
                
                dist[i][j] = min(
                    dist[i-1][j] + 1,      # 删除
                    dist[i][j-1] + 1,      # 插入
                    dist[i-1][j-1] + cost  # 替换
                )
        
        return dist[rows-1][cols-1]
    
    def sequence_similarity(self, seq_a: List[str], seq_b: List[str]) -> float:
        """
        计算序列相似度（基于归一化的莱文斯坦距离）
        
        Args:
            seq_a: 序列A
            seq_b: 序列B
            
        Returns:
            序列相似度 (0-1之间)
        """
        if not seq_a and not seq_b:
            return 1.0
        
        max_len = max(len(seq_a), len(seq_b))
        if max_len == 0:
            return 1.0
        
        distance = self.levenshtein_distance(seq_a, seq_b)
        return 1.0 - (distance / max_len)
    
    def path_similarity(self, path_a: Dict[str, Any], path_b: Dict[str, Any]) -> float:
        """
        计算两条路径的相似度
        
        Args:
            path_a: 路径A
            path_b: 路径B
            
        Returns:
            路径相似度 (0-1之间)
        """
        try:
            # 提取节点集合
            nodes_a = set(path_a.get('nodes', []))
            nodes_b = set(path_b.get('nodes', []))
            node_similarity = self.jaccard_similarity(nodes_a, nodes_b)
            
            # 提取边集合
            edges_a = set(tuple(edge) if isinstance(edge, list) else edge 
                         for edge in path_a.get('edges', []))
            edges_b = set(tuple(edge) if isinstance(edge, list) else edge 
                         for edge in path_b.get('edges', []))
            edge_similarity = self.jaccard_similarity(edges_a, edges_b)
            
            # 提取序列
            sequence_a = path_a.get('sequence', [])
            sequence_b = path_b.get('sequence', [])
            seq_similarity = self.sequence_similarity(sequence_a, sequence_b)
            
            # 加权组合
            total_similarity = (
                self.node_weight * node_similarity +
                self.edge_weight * edge_similarity +
                self.sequence_weight * seq_similarity
            )
            
            self.logger.debug(f"路径相似度计算: 节点={node_similarity:.3f}, 边={edge_similarity:.3f}, 序列={seq_similarity:.3f}, 总计={total_similarity:.3f}")
            
            return total_similarity
            
        except Exception as e:
            self.logger.error(f"路径相似度计算失败: {e}")
            return 0.0
    
    def cross_similarity_matrix(self, paths_a: List[Dict[str, Any]], paths_b: List[Dict[str, Any]]) -> np.ndarray:
        """
        计算跨集合的相似度矩阵
        
        Args:
            paths_a: 路径集合A
            paths_b: 路径集合B
            
        Returns:
            相似度矩阵 (|paths_a| × |paths_b|)
        """
        if not paths_a or not paths_b:
            return np.zeros((len(paths_a), len(paths_b)))
        
        matrix = np.zeros((len(paths_a), len(paths_b)))
        
        for i, path_a in enumerate(paths_a):
            for j, path_b in enumerate(paths_b):
                matrix[i][j] = self.path_similarity(path_a, path_b)
        
        return matrix
    
    def symmetric_average_best_match(self, similarity_matrix: np.ndarray) -> float:
        """
        计算双向平均最佳匹配相似度
        
        Args:
            similarity_matrix: 相似度矩阵
            
        Returns:
            双向平均最佳匹配相似度
        """
        if similarity_matrix.size == 0:
            return 0.0
        
        rows, cols = similarity_matrix.shape
        
        if rows == 0 or cols == 0:
            return 0.0
        
        # 从A到B的相似度：每行的最大值的平均
        a_to_b = np.mean(np.max(similarity_matrix, axis=1))
        
        # 从B到A的相似度：每列的最大值的平均
        b_to_a = np.mean(np.max(similarity_matrix, axis=0))
        
        # 双向平均
        symmetric_similarity = (a_to_b + b_to_a) / 2.0
        
        self.logger.debug(f"双向平均最佳匹配: A→B={a_to_b:.3f}, B→A={b_to_a:.3f}, 对称={symmetric_similarity:.3f}")
        
        return symmetric_similarity
    
    def paths_similarity(self, paths_a: List[Dict[str, Any]], paths_b: List[Dict[str, Any]]) -> float:
        """
        计算两个路径集合的相似度
        
        Args:
            paths_a: 路径集合A
            paths_b: 路径集合B
            
        Returns:
            路径集合相似度
        """
        if not paths_a and not paths_b:
            return 1.0
        
        if not paths_a or not paths_b:
            return 0.0
        
        # 计算跨集合相似度矩阵
        similarity_matrix = self.cross_similarity_matrix(paths_a, paths_b)
        
        # 使用双向平均最佳匹配
        return self.symmetric_average_best_match(similarity_matrix)
    
    def points_similarity(self, points_a: List[str], points_b: List[str]) -> float:
        """
        计算两个独立点集合的相似度（使用Jaccard相似度）
        
        Args:
            points_a: 独立点集合A
            points_b: 独立点集合B
            
        Returns:
            独立点集合相似度
        """
        set_a = set(points_a)
        set_b = set(points_b)
        return self.jaccard_similarity(set_a, set_b)
    
    def total_similarity(
        self, 
        paths_a: List[Dict[str, Any]], 
        points_a: List[str],
        paths_b: List[Dict[str, Any]], 
        points_b: List[str],
        paths_weight: float = 0.7,
        points_weight: float = 0.3
    ) -> Dict[str, float]:
        """
        计算总体相似度
        
        Args:
            paths_a: 路径集合A
            points_a: 独立点集合A
            paths_b: 路径集合B
            points_b: 独立点集合B
            paths_weight: 路径相似度权重
            points_weight: 独立点相似度权重
            
        Returns:
            包含各项相似度的字典
        """
        # 确保权重和为1
        total_weight = paths_weight + points_weight
        if abs(total_weight - 1.0) > 1e-6:
            paths_weight /= total_weight
            points_weight /= total_weight
        
        # 计算各项相似度
        paths_sim = self.paths_similarity(paths_a, paths_b)
        points_sim = self.points_similarity(points_a, points_b)
        
        # 计算总体相似度
        total_sim = paths_weight * paths_sim + points_weight * points_sim
        
        return {
            'paths_similarity': paths_sim,
            'points_similarity': points_sim,
            'total_similarity': total_sim,
            'weights': {
                'paths_weight': paths_weight,
                'points_weight': points_weight
            }
        }
