# GraphMASAL 环境配置模板
# 复制此文件为 .env 并填入实际配置值

# ================================
# Neo4j 数据库配置
# ================================
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password

# ================================
# LLM API 配置
# ================================

# OpenAI 官方 API 配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini
OPENAI_BASE_URL=https://api.openai.com/v1/

# 或者使用豆包 (Doubao) API
# OPENAI_API_KEY=your_doubao_api_key
# OPENAI_MODEL=doubao-seed-1.6-250615
# OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/

# ================================
# Embedding 配置
# ================================

# 如果使用不同的 Embedding 服务，可以单独配置
# 如果不配置，将使用上面的 OPENAI_* 配置
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_API_KEY=your_embedding_api_key
EMBEDDING_BASE_URL=https://api.openai.com/v1/

# 豆包 Embedding 配置示例
# EMBEDDING_MODEL=doubao-embedding-text-240715
# EMBEDDING_API_KEY=your_doubao_api_key
# EMBEDDING_BASE_URL=https://ark.cn-beijing.volces.com/api/v3/

# ================================
# 系统配置
# ================================

# 模型温度设置 (0.0-1.0)
# 0.0 = 最确定性，1.0 = 最随机性
TEMPERATURE=0.0

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 最大重试次数
MAX_RETRIES=3

# 请求超时时间（秒）
REQUEST_TIMEOUT=30

# ================================
# 可选配置
# ================================

# 如果需要使用代理
# HTTP_PROXY=http://your-proxy:port
# HTTPS_PROXY=https://your-proxy:port

# 自定义用户代理
# USER_AGENT=GraphMASAL/1.0

# 调试模式
# DEBUG=false
