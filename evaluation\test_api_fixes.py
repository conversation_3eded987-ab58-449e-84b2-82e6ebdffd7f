#!/usr/bin/env python3
"""
测试API修复
验证Graphiti.search和OptimalPathPlanner方法调用的修复
"""

import asyncio
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_graphiti_search_fix():
    """测试Graphiti.search方法修复"""
    logger.info("=== 测试Graphiti.search方法修复 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator, create_graphiti_client
        
        # 创建客户端和生成器
        client = create_graphiti_client()
        generator = ST1GroundTruthGenerator(client)
        
        # 测试知识点验证
        test_knowledge_points = [
            "力学/运动学/位移概念",
            "力学/运动学/速度概念"
        ]
        
        logger.info(f"测试验证知识点: {test_knowledge_points}")
        
        verification_results = await generator.verify_knowledge_points_in_graph(test_knowledge_points)
        
        logger.info("✅ Graphiti.search方法调用成功")
        
        for kp, result in verification_results.items():
            logger.info(f"知识点 '{kp}': 存在={result.get('exists', False)}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_optimal_path_planner_fix():
    """测试OptimalPathPlanner方法修复"""
    logger.info("=== 测试OptimalPathPlanner方法修复 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator, create_graphiti_client
        
        # 创建客户端和生成器
        client = create_graphiti_client()
        generator = ST1GroundTruthGenerator(client)
        
        # 测试路径规划生成
        test_student_id = "test_student_001"
        test_student_mastery = {
            "力学/运动学/位移概念": 0.8,
            "力学/运动学/速度概念": 0.3
        }
        
        logger.info(f"测试为学生 {test_student_id} 生成路径规划")
        
        planning_result = await generator.generate_planning_ground_truth(
            student_id=test_student_id,
            student_mastery=test_student_mastery
        )
        
        logger.info("✅ OptimalPathPlanner方法调用成功")
        
        # 检查结果结构
        if 'planning_result' in planning_result:
            planning_data = planning_result['planning_result']
            logger.info(f"生成的路径数: {len(planning_data.get('paths_subset', []))}")
            logger.info(f"独立点数: {len(planning_data.get('points_subset', []))}")
        else:
            logger.warning("规划结果结构异常")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_complete_workflow():
    """测试完整工作流程"""
    logger.info("=== 测试完整工作流程 ===")
    
    try:
        from evaluation.st1_ground_truth_generator import ST1GroundTruthGenerator, create_graphiti_client
        
        # 创建客户端和生成器
        client = create_graphiti_client()
        generator = ST1GroundTruthGenerator(client)
        
        # 模拟ST1学生数据（简化版）
        student_data = {
            'student_id': 'test_student_001',
            'name': '测试学生',
            'problem_attempts': [
                {
                    'problem_id': 10201107,
                    'selected_options': ['C'],
                    'is_fully_correct': True,
                    'timestamp': '2025-01-27T14:01:00Z'
                }
            ]
        }
        
        logger.info("步骤1: 从Neo4j获取题目数据")
        problem_ids = [attempt['problem_id'] for attempt in student_data['problem_attempts']]
        problems_data = await generator.fetch_problems_from_neo4j(problem_ids)
        
        if not problems_data:
            logger.warning("未能获取题目数据，跳过后续测试")
            return True
        
        logger.info(f"✅ 成功获取 {len(problems_data)} 道题目")
        
        logger.info("步骤2: 转换数据格式")
        converted_problems = generator.convert_st1_to_problem_format(student_data, problems_data)
        logger.info(f"✅ 成功转换 {len(converted_problems)} 道题目")
        
        logger.info("步骤3: 验证知识点")
        all_kp_ids = set()
        for problem in problems_data:
            all_kp_ids.update(problem.get('linked_kp_ids', []))
        
        if all_kp_ids:
            knowledge_verification = await generator.verify_knowledge_points_in_graph(list(all_kp_ids))
            valid_kp_count = sum(1 for v in knowledge_verification.values() if v.get('exists', False))
            logger.info(f"✅ 验证知识点: {valid_kp_count}/{len(all_kp_ids)} 个有效")
        
        logger.info("步骤4: 生成诊断结果")
        diagnostic_result = await generator.generate_student_diagnostic_ground_truth(
            student_data['student_id'], 
            converted_problems, 
            knowledge_verification if all_kp_ids else {}
        )
        
        if 'error' not in diagnostic_result:
            logger.info("✅ 诊断结果生成成功")
        else:
            logger.warning(f"诊断结果生成有错误: {diagnostic_result.get('error')}")
        
        logger.info("步骤5: 生成路径规划")
        # 提取掌握度信息
        student_mastery = {}
        if 'diagnostic_result' in diagnostic_result:
            for concept in diagnostic_result['diagnostic_result'].get('mastered_concepts', []):
                student_mastery[concept['concept_id']] = concept['mastery_level']
            for concept in diagnostic_result['diagnostic_result'].get('not_mastered_concepts', []):
                student_mastery[concept['concept_id']] = concept['mastery_level']
        
        planning_result = await generator.generate_planning_ground_truth(
            student_data['student_id'], 
            student_mastery
        )
        
        if 'error' not in planning_result:
            logger.info("✅ 路径规划生成成功")
        else:
            logger.warning(f"路径规划生成有错误: {planning_result.get('error')}")
        
        logger.info("✅ 完整工作流程测试通过")
        return True
        
    except Exception as e:
        logger.error(f"完整工作流程测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    logger.info("开始测试API修复...")
    
    tests = [
        ("Graphiti.search方法修复", test_graphiti_search_fix),
        ("OptimalPathPlanner方法修复", test_optimal_path_planner_fix),
        ("完整工作流程", test_complete_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            if result:
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试总结: {passed}/{total} 个测试通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        print("\n🎉 所有API修复测试通过！")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，请检查相关功能")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
