#!/usr/bin/env python3
"""
Graphiti 客户端管理器
独立模块，避免循环导入问题
"""

import logging
from typing import Optional
from graphiti_core import Graphiti

logger = logging.getLogger(__name__)

# 全局客户端变量
_graphiti_client: Optional[Graphiti] = None

def set_graphiti_client(client: Graphiti) -> None:
    """设置全局Graphiti客户端"""
    global _graphiti_client
    _graphiti_client = client
    logger.info(f"全局Graphiti客户端已设置: {client is not None}")

def get_graphiti_client() -> Graphiti:
    """获取全局Graphiti客户端"""
    global _graphiti_client
    if _graphiti_client is None:
        raise RuntimeError("Graphiti客户端未初始化")
    return _graphiti_client

def is_graphiti_client_initialized() -> bool:
    """检查Graphiti客户端是否已初始化"""
    global _graphiti_client
    return _graphiti_client is not None

def clear_graphiti_client() -> None:
    """清除全局Graphiti客户端（用于测试或重置）"""
    global _graphiti_client
    _graphiti_client = None
    logger.info("全局Graphiti客户端已清除")
