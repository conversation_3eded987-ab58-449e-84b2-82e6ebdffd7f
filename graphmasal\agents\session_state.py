#!/usr/bin/env python3
"""
会话状态管理器
用于在智能体之间共享数据，避免重复的工具调用
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import json

logger = logging.getLogger(__name__)

class SessionStateManager:
    """会话状态管理器"""
    
    def __init__(self):
        self._state: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        self._cache_ttl = 300  # 缓存5分钟
    
    def set_student_diagnostic_result(self, student_id: str, diagnostic_data: Dict[str, Any]) -> None:
        """设置学生诊断结果"""
        key = f"diagnostic_{student_id}"
        self._state[key] = diagnostic_data
        self._cache_timestamps[key] = datetime.now(timezone.utc)
        logger.info(f"已缓存学生 {student_id} 的诊断结果")
    
    def get_student_diagnostic_result(self, student_id: str) -> Optional[Dict[str, Any]]:
        """获取学生诊断结果"""
        key = f"diagnostic_{student_id}"
        if self._is_cache_valid(key):
            logger.info(f"从缓存获取学生 {student_id} 的诊断结果")
            return self._state.get(key)
        return None
    
    def set_student_mastery(self, student_id: str, mastery_data: Dict[str, float]) -> None:
        """设置学生掌握情况"""
        key = f"mastery_{student_id}"
        self._state[key] = mastery_data
        self._cache_timestamps[key] = datetime.now(timezone.utc)
        logger.info(f"已缓存学生 {student_id} 的掌握情况，包含 {len(mastery_data)} 个概念")
    
    def get_student_mastery(self, student_id: str) -> Optional[Dict[str, float]]:
        """获取学生掌握情况"""
        key = f"mastery_{student_id}"
        if self._is_cache_valid(key):
            logger.info(f"从缓存获取学生 {student_id} 的掌握情况")
            return self._state.get(key)
        return None
    
    def set_student_misconceptions(self, student_id: str, misconceptions: List[Dict[str, Any]]) -> None:
        """设置学生易错点"""
        key = f"misconceptions_{student_id}"
        self._state[key] = misconceptions
        self._cache_timestamps[key] = datetime.now(timezone.utc)
        logger.info(f"已缓存学生 {student_id} 的易错点，包含 {len(misconceptions)} 个")
    
    def get_student_misconceptions(self, student_id: str) -> Optional[List[Dict[str, Any]]]:
        """获取学生易错点"""
        key = f"misconceptions_{student_id}"
        if self._is_cache_valid(key):
            logger.info(f"从缓存获取学生 {student_id} 的易错点")
            return self._state.get(key)
        return None
    
    def set_learning_path(self, student_id: str, target_concept: str, learning_path: List[Dict[str, Any]]) -> None:
        """设置学习路径"""
        key = f"learning_path_{student_id}_{target_concept}"
        self._state[key] = learning_path
        self._cache_timestamps[key] = datetime.now(timezone.utc)
        logger.info(f"已缓存学生 {student_id} 针对 {target_concept} 的学习路径")
    
    def get_learning_path(self, student_id: str, target_concept: str) -> Optional[List[Dict[str, Any]]]:
        """获取学习路径"""
        key = f"learning_path_{student_id}_{target_concept}"
        if self._is_cache_valid(key):
            logger.info(f"从缓存获取学生 {student_id} 针对 {target_concept} 的学习路径")
            return self._state.get(key)
        return None
    
    def invalidate_student_data(self, student_id: str) -> None:
        """使学生相关的所有缓存失效"""
        keys_to_remove = []
        for key in self._state.keys():
            if student_id in key:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self._state[key]
            if key in self._cache_timestamps:
                del self._cache_timestamps[key]
        
        logger.info(f"已清除学生 {student_id} 的所有缓存数据")
    
    def _is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        if key not in self._state or key not in self._cache_timestamps:
            return False
        
        cache_time = self._cache_timestamps[key]
        current_time = datetime.now(timezone.utc)
        age_seconds = (current_time - cache_time).total_seconds()
        
        return age_seconds < self._cache_ttl
    
    def get_cache_summary(self) -> Dict[str, Any]:
        """获取缓存摘要信息"""
        summary = {
            'total_cached_items': len(self._state),
            'cache_keys': list(self._state.keys()),
            'cache_ages': {}
        }
        
        current_time = datetime.now(timezone.utc)
        for key, timestamp in self._cache_timestamps.items():
            age_seconds = (current_time - timestamp).total_seconds()
            summary['cache_ages'][key] = f"{age_seconds:.1f}s"
        
        return summary
    
    def clear_all_cache(self) -> None:
        """清除所有缓存"""
        self._state.clear()
        self._cache_timestamps.clear()
        logger.info("已清除所有缓存数据")

# 全局会话状态管理器实例
_session_state_manager: Optional[SessionStateManager] = None

def get_session_state_manager() -> SessionStateManager:
    """获取全局会话状态管理器"""
    global _session_state_manager
    if _session_state_manager is None:
        _session_state_manager = SessionStateManager()
    return _session_state_manager

def reset_session_state() -> None:
    """重置会话状态（用于新会话或测试）"""
    global _session_state_manager
    if _session_state_manager:
        _session_state_manager.clear_all_cache()
    _session_state_manager = SessionStateManager()
    logger.info("会话状态已重置")
