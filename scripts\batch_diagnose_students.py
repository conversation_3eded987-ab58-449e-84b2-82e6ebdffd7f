#!/usr/bin/env python3
"""
批量诊断学生脚本
为数据库中的学生生成诊断信息
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from graphmasal.config import settings
from graphmasal.main import TutorSystem
from graphmasal.models.state import StudentInfo
from graphmasal.agents.tutor_agent import TutorAgent
from graphmasal.agents.tools.diagnostic_tool import analyze_student_from_database

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchStudentDiagnoser:
    """批量学生诊断器"""
    
    def __init__(self):
        self.tutor_system = None
        self.tutor_agent = None
        self.logger = logger
    
    async def setup_system(self):
        """设置智能体系统"""
        try:
            self.tutor_system = TutorSystem()
            client = self.tutor_system._create_graphiti_client()
            self.tutor_agent = TutorAgent(client)
            
            self.logger.info("智能体系统设置完成")
            
        except Exception as e:
            self.logger.error(f"智能体系统设置失败: {e}")
            raise
    
    async def get_all_students(self) -> List[str]:
        """获取数据库中所有学生的ID"""
        try:
            client = self.tutor_system._create_graphiti_client()
            
            query = """
            MATCH (s:Student)
            RETURN s.studentId as student_id, s.name as student_name
            ORDER BY s.studentId
            """
            
            result = await client.driver.execute_query(query)
            
            students = []
            for record in result.records:
                students.append({
                    'student_id': record['student_id'],
                    'student_name': record['student_name']
                })
            
            self.logger.info(f"找到 {len(students)} 个学生")
            return students
            
        except Exception as e:
            self.logger.error(f"获取学生列表失败: {e}")
            return []
    
    async def diagnose_single_student(self, student_info: Dict[str, str]) -> Dict[str, Any]:
        """为单个学生生成诊断信息"""
        
        student_id = student_info['student_id']
        student_name = student_info['student_name']
        
        try:
            self.logger.info(f"开始诊断学生: {student_id} ({student_name})")
            
            # 使用诊断工具分析学生
            diagnostic_result = await analyze_student_from_database.ainvoke({"student_id": student_id})
            
            # 解析诊断结果
            diagnostic_data = json.loads(diagnostic_result)
            
            # 检查是否有错误
            if 'error' in diagnostic_data:
                self.logger.warning(f"学生 {student_id} 诊断出现错误: {diagnostic_data['error']}")
                return {
                    'student_id': student_id,
                    'student_name': student_name,
                    'status': 'error',
                    'error': diagnostic_data['error'],
                    'diagnostic_data': None
                }
            
            # 成功获取诊断数据
            self.logger.info(f"学生 {student_id} 诊断完成 - 准确率: {diagnostic_data.get('overall_accuracy', 0):.2f}")
            
            return {
                'student_id': student_id,
                'student_name': student_name,
                'status': 'success',
                'diagnostic_data': diagnostic_data,
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"诊断学生 {student_id} 失败: {e}")
            return {
                'student_id': student_id,
                'student_name': student_name,
                'status': 'error',
                'error': str(e),
                'diagnostic_data': None
            }
    
    async def batch_diagnose_all_students(self, output_dir: Path) -> Dict[str, Any]:
        """批量诊断所有学生"""
        
        self.logger.info("=" * 60)
        self.logger.info("🔍 开始批量学生诊断")
        self.logger.info("=" * 60)
        
        # 获取所有学生
        students = await self.get_all_students()
        
        if not students:
            self.logger.error("没有找到学生数据")
            return {}
        
        # 批量诊断
        diagnostic_results = []
        successful_diagnoses = 0
        failed_diagnoses = 0
        
        for i, student_info in enumerate(students):
            try:
                result = await self.diagnose_single_student(student_info)
                diagnostic_results.append(result)
                
                if result['status'] == 'success':
                    successful_diagnoses += 1
                else:
                    failed_diagnoses += 1
                
                self.logger.info(f"进度: {i+1}/{len(students)} - 成功: {successful_diagnoses}, 失败: {failed_diagnoses}")
                
            except Exception as e:
                self.logger.error(f"处理学生 {student_info['student_id']} 时出错: {e}")
                failed_diagnoses += 1
                continue
        
        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存诊断结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = output_dir / f"batch_diagnostic_results_{timestamp}.json"
        
        batch_results = {
            'batch_info': {
                'processed_at': datetime.now().isoformat(),
                'total_students': len(students),
                'successful_diagnoses': successful_diagnoses,
                'failed_diagnoses': failed_diagnoses,
                'success_rate': successful_diagnoses / len(students) if students else 0
            },
            'diagnostic_results': diagnostic_results
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(batch_results, f, ensure_ascii=False, indent=2)
        
        # 生成统计报告
        self.generate_diagnostic_summary(batch_results, output_dir)
        
        self.logger.info("=" * 60)
        self.logger.info("✅ 批量诊断完成！")
        self.logger.info(f"  总学生数: {len(students)}")
        self.logger.info(f"  成功诊断: {successful_diagnoses}")
        self.logger.info(f"  失败诊断: {failed_diagnoses}")
        self.logger.info(f"  成功率: {successful_diagnoses/len(students)*100:.1f}%")
        self.logger.info(f"  结果文件: {results_file}")
        self.logger.info("=" * 60)
        
        return batch_results
    
    def generate_diagnostic_summary(self, batch_results: Dict[str, Any], output_dir: Path):
        """生成诊断摘要报告"""
        
        successful_results = [
            r for r in batch_results['diagnostic_results'] 
            if r['status'] == 'success' and r['diagnostic_data']
        ]
        
        if not successful_results:
            self.logger.warning("没有成功的诊断结果，无法生成摘要")
            return
        
        # 统计信息
        total_attempts = sum(r['diagnostic_data']['total_attempts'] for r in successful_results)
        total_correct = sum(r['diagnostic_data']['correct_attempts'] for r in successful_results)
        overall_accuracy = total_correct / total_attempts if total_attempts > 0 else 0
        
        # 收集所有易错点
        all_misconceptions = []
        for result in successful_results:
            if 'detailed_analysis' in result['diagnostic_data']:
                for analysis in result['diagnostic_data']['detailed_analysis']:
                    all_misconceptions.extend(analysis.get('identified_misconceptions', []))
        
        # 统计最常见的易错点
        misconception_counts = {}
        for misc in all_misconceptions:
            desc = misc.get('description', 'Unknown')
            misconception_counts[desc] = misconception_counts.get(desc, 0) + 1
        
        # 生成摘要
        summary = {
            'summary_info': {
                'generated_at': datetime.now().isoformat(),
                'successful_students': len(successful_results),
                'total_attempts': total_attempts,
                'total_correct': total_correct,
                'overall_accuracy': round(overall_accuracy, 3)
            },
            'performance_distribution': {
                'excellent': len([r for r in successful_results if r['diagnostic_data']['overall_accuracy'] >= 0.8]),
                'good': len([r for r in successful_results if 0.6 <= r['diagnostic_data']['overall_accuracy'] < 0.8]),
                'needs_improvement': len([r for r in successful_results if r['diagnostic_data']['overall_accuracy'] < 0.6])
            },
            'top_misconceptions': sorted(
                misconception_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10],
            'student_summaries': [
                {
                    'student_id': r['student_id'],
                    'student_name': r['student_name'],
                    'accuracy': r['diagnostic_data']['overall_accuracy'],
                    'attempts': r['diagnostic_data']['total_attempts'],
                    'performance_level': r['diagnostic_data']['summary']['performance_level']
                }
                for r in successful_results
            ]
        }
        
        # 保存摘要
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = output_dir / f"diagnostic_summary_{timestamp}.json"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"诊断摘要已保存: {summary_file}")
        
        # 打印简要统计
        print(f"\n📊 诊断统计摘要:")
        print(f"  总体准确率: {overall_accuracy:.1%}")
        print(f"  优秀学生: {summary['performance_distribution']['excellent']} 人")
        print(f"  良好学生: {summary['performance_distribution']['good']} 人")
        print(f"  需提高学生: {summary['performance_distribution']['needs_improvement']} 人")
        
        if summary['top_misconceptions']:
            print(f"\n🔍 最常见易错点:")
            for i, (desc, count) in enumerate(summary['top_misconceptions'][:5]):
                print(f"  {i+1}. {desc} ({count}次)")

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批量诊断学生')
    parser.add_argument('--output-dir',
                       default='evaluation/diagnostic_results',
                       help='诊断结果输出目录')
    
    args = parser.parse_args()
    
    try:
        # 验证配置
        settings.validate()
        
        # 创建诊断器
        diagnoser = BatchStudentDiagnoser()
        
        # 设置系统
        await diagnoser.setup_system()
        
        # 批量诊断
        output_dir = Path(args.output_dir)
        results = await diagnoser.batch_diagnose_all_students(output_dir)
        
        print(f"\n✅ 批量诊断完成！")
        print(f"📁 结果目录: {output_dir}")
        
    except Exception as e:
        logger.error(f"批量诊断失败: {e}")
        print(f"❌ 诊断失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
