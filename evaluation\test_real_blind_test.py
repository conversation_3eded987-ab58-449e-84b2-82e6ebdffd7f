#!/usr/bin/env python3
"""
测试真实盲测流程
验证从ground_truth_generator生成的答题记录到complete_blind_test的完整流程
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_answer_records_structure():
    """测试答题记录文件结构"""
    logger.info("🔍 测试答题记录文件结构...")
    
    records_file = Path("evaluation/student_answer_records/all_students_answer_records.json")
    
    if not records_file.exists():
        logger.error(f"❌ 答题记录文件不存在: {records_file}")
        return False
    
    try:
        with open(records_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查基本结构
        if 'metadata' not in data:
            logger.error("❌ 缺少metadata字段")
            return False
        
        if 'students' not in data:
            logger.error("❌ 缺少students字段")
            return False
        
        students = data['students']
        if not students:
            logger.error("❌ 没有学生数据")
            return False
        
        # 检查第一个学生的数据结构
        first_student_id = list(students.keys())[0]
        first_student = students[first_student_id]
        
        required_fields = ['student_id', 'timestamp', 'total_problems', 'problems']
        for field in required_fields:
            if field not in first_student:
                logger.error(f"❌ 学生数据缺少字段: {field}")
                return False
        
        # 检查题目数据结构
        if first_student['problems']:
            first_problem = first_student['problems'][0]
            problem_fields = ['problem_id', 'concept_id', 'student_answer', 'correct_answer']
            for field in problem_fields:
                if field not in first_problem:
                    logger.error(f"❌ 题目数据缺少字段: {field}")
                    return False
        
        logger.info(f"✅ 答题记录文件结构正确")
        logger.info(f"   学生数量: {len(students)}")
        logger.info(f"   第一个学生题目数: {first_student['total_problems']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 读取答题记录文件失败: {e}")
        return False

def test_blind_test_loading():
    """测试盲测加载功能"""
    logger.info("🔍 测试盲测加载功能...")
    
    try:
        from evaluation.complete_blind_test import CompleteBlindTestEvaluator
        
        evaluator = CompleteBlindTestEvaluator()
        
        # 测试加载答题记录
        student_records = evaluator.load_student_answer_records()
        
        if not student_records:
            logger.error("❌ 加载答题记录失败")
            return False
        
        # 测试创建盲测用例
        blind_test_cases = evaluator.create_blind_test_cases_from_records(student_records)
        
        if not blind_test_cases:
            logger.error("❌ 创建盲测用例失败")
            return False
        
        # 检查盲测用例结构
        first_case = blind_test_cases[0]
        required_fields = ['student_id', 'test_metadata', 'real_problems']
        for field in required_fields:
            if field not in first_case:
                logger.error(f"❌ 盲测用例缺少字段: {field}")
                return False
        
        logger.info(f"✅ 盲测加载功能正常")
        logger.info(f"   盲测用例数量: {len(blind_test_cases)}")
        logger.info(f"   第一个用例题目数: {len(first_case['real_problems'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试盲测加载功能失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_problem_data_integrity():
    """测试题目数据完整性"""
    logger.info("🔍 测试题目数据完整性...")
    
    try:
        from evaluation.complete_blind_test import CompleteBlindTestEvaluator
        
        evaluator = CompleteBlindTestEvaluator()
        student_records = evaluator.load_student_answer_records()
        blind_test_cases = evaluator.create_blind_test_cases_from_records(student_records)
        
        # 检查是否移除了不应该有的字段
        for case in blind_test_cases[:3]:  # 检查前3个用例
            for problem in case['real_problems']:
                # 这些字段不应该存在（避免泄露答案）
                forbidden_fields = ['linked_kp_ids', 'misconception_map', 'is_correct']
                for field in forbidden_fields:
                    if field in problem:
                        logger.warning(f"⚠️ 题目包含不应有的字段: {field}")
                
                # 这些字段应该存在
                required_fields = ['problem_id', 'concept_id', 'student_answer', 'correct_answer']
                for field in required_fields:
                    if field not in problem:
                        logger.error(f"❌ 题目缺少必要字段: {field}")
                        return False
        
        logger.info("✅ 题目数据完整性检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 题目数据完整性检查失败: {e}")
        return False

async def test_single_diagnostic():
    """测试单个学生的诊断流程"""
    logger.info("🔍 测试单个学生的诊断流程...")
    
    try:
        from evaluation.complete_blind_test import CompleteBlindTestEvaluator
        
        evaluator = CompleteBlindTestEvaluator()
        
        # 设置系统
        await evaluator.setup_system()
        
        # 加载数据
        student_records = evaluator.load_student_answer_records()
        blind_test_cases = evaluator.create_blind_test_cases_from_records(student_records)
        
        if not blind_test_cases:
            logger.error("❌ 没有盲测用例")
            return False
        
        # 测试第一个学生的诊断
        first_case = blind_test_cases[0]
        student_id = first_case['student_id']
        
        logger.info(f"🤖 开始测试学生 {student_id} 的诊断...")
        
        # 运行诊断
        diagnostic_result = await evaluator.run_agent_diagnostic(first_case)
        
        if not diagnostic_result:
            logger.error("❌ 诊断结果为空")
            return False
        
        # 检查诊断结果结构
        required_fields = ['evaluation_metadata', 'diagnostic_result']
        for field in required_fields:
            if field not in diagnostic_result:
                logger.error(f"❌ 诊断结果缺少字段: {field}")
                return False
        
        logger.info(f"✅ 学生 {student_id} 诊断成功")
        logger.info(f"   处理题目数: {len(first_case['real_problems'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 单个诊断测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始真实盲测流程测试")
    logger.info("=" * 60)
    
    # 测试1: 答题记录文件结构
    test1_result = test_answer_records_structure()
    
    # 测试2: 盲测加载功能
    test2_result = False
    if test1_result:
        test2_result = test_blind_test_loading()
    
    # 测试3: 题目数据完整性
    test3_result = False
    if test2_result:
        test3_result = test_problem_data_integrity()
    
    # 测试4: 单个诊断流程
    test4_result = False
    if test3_result:
        test4_result = await test_single_diagnostic()
    
    # 总结
    logger.info("=" * 60)
    logger.info("📊 真实盲测流程测试结果:")
    logger.info(f"   答题记录文件结构: {'✅ 通过' if test1_result else '❌ 失败'}")
    logger.info(f"   盲测加载功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    logger.info(f"   题目数据完整性: {'✅ 通过' if test3_result else '❌ 失败'}")
    logger.info(f"   单个诊断流程: {'✅ 通过' if test4_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result, test4_result]):
        logger.info("🎉 所有测试通过！真实盲测流程正常工作")
        logger.info("💡 现在可以运行完整的真实盲测了")
        return True
    else:
        logger.error("💥 部分测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    asyncio.run(main())
